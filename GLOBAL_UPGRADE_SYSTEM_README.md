# Global Upgrade System

A persistent progression system that rewards players with points for winning games and allows permanent tower upgrades. Each individual tower can be upgraded independently.

## Victory Points
- **Difficulty 1**: 1 point
- **Difficulty 100**: 15 points
- Linear scaling between difficulties

## Upgrade Types
- **Damage**: +1 per level
- **Range**: +5 per level  
- **Fire Rate**: +2 per level (faster)
- **Projectile Speed**: +0.1 per level

## Cost Scaling
- Base cost: 5 points
- Exponential scaling: 1.5x per level
- Level 0→1: 5 points, Level 1→2: 7 points, etc.

## How to Use
1. **Win a game** to earn points
2. **Access from main menu** via "Upgrades" button
3. **Browse upgraded towers** in the list
4. **Click on a tower** to view its upgrades
5. **Purchase upgrades** for that specific tower
6. **Press ESC** to close menu

## Features
- **Per-tower upgrades** - Each tower instance is upgraded individually
- **Unique tower IDs** - Each tower gets a persistent identifier
- **Main menu access** - Upgrade system available before starting games
- **Dynamic application** to upgraded towers
- **Persistent progress** across games
- **Save data** in `global_upgrades.json`

## Example
- Win difficulty 50 = 7 points
- Place a Basic Tower (gets ID "basic_1")
- Buy Basic Tower #1 Damage +1 (5 points)
- Place another Basic Tower (gets ID "basic_2")
- Buy Basic Tower #2 Range +5 (2 points)
- Each tower keeps its specific upgrades

## Technical Details

### Files
- `game_systems/global_upgrade_system.py` - Core upgrade logic
- `game_systems/global_upgrade_ui.py` - User interface
- `global_upgrades.json` - Save data (dynamically generated)

### Integration
- **Main menu integration** - Accessible via launcher
- **Tower ID assignment** - Unique IDs assigned on placement
- **Dynamic application** to upgraded towers
- **Victory detection** and point awarding
- **UI integration** with existing game systems
- **Save/load functionality** for persistence

### Tower ID System
- Each tower gets a unique ID when placed: `{tower_type}_{counter}`
- Examples: "basic_1", "sniper_2", "freezer_3"
- Counter increments for each tower placed in the session
- IDs are persistent and used to track individual tower upgrades

## Tips for Players

1. **Upgrade your best towers** - Focus on towers that perform well for you
2. **Balance your upgrades** - Don't neglect range or fire rate
3. **Plan for higher difficulties** - Save points for expensive upgrades
4. **Experiment with different builds** - Try upgrading different tower types
5. **Replay easier difficulties** - Farm points for expensive upgrades
6. **Access upgrades anytime** - Use the main menu to manage upgrades between games
7. **Track your progress** - Each tower keeps its upgrades across games

## Future Enhancements

Potential additions to the system:
- **Special abilities** for high-level upgrades
- **Tower-specific bonuses** (unique to each tower type)
- **Achievement system** for milestone upgrades
- **Prestige system** for long-term progression
- **Multiplayer leaderboards** based on upgrade progress 