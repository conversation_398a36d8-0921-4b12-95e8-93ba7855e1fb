@echo off
echo Simple Enemy Sprite Viewer
echo ==========================
echo.

if "%1"=="" (
    set /p enemy_type="Enter enemy type (basic, flying, boss, etc.): "
    if "!enemy_type!"=="" set enemy_type=basic
) else (
    set enemy_type=%1
)

echo.
echo Starting Simple Sprite Viewer for: %enemy_type%
echo Controls: Arrow keys to step frames, R to reset, ESC to exit
echo.

E:\Anaconda3\envs\tower_defense\python.exe enemy_viewer\simple_sprite_viewer.py %enemy_type%

pause
