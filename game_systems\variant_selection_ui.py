"""
Variant Selection UI System
Provides a user interface for players to select level modifiers and create custom variants
"""

import pygame
import json
import os
from typing import Dict, List, Any, Optional, Tuple, Set
from .level_variant_generator import (LevelVariantGenerator, LevelModifier, ModifierType, 
                                    ModifierDifficulty, LevelVariant)

class VariantSelectionUI:
    """UI for selecting level modifiers and creating variants"""
    
    def __init__(self, screen_width: int, screen_height: int):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.variant_generator = LevelVariantGenerator()
        
        # UI State
        self.active = False
        self.base_level_path = None
        self.base_level_name = ""
        self.selected_modifier: Optional[LevelModifier] = None  # Only one modifier can be selected
        self.available_modifiers: Dict[str, List[LevelModifier]] = {}
        self.scroll_offset = 0
        self.max_scroll = 0
        
        # UI Layout
        self.margin = 30
        self.content_width = screen_width - 2 * self.margin
        self.content_height = screen_height - 100  # Leave space for buttons
        
        # Colors (modern design)
        self.BG_COLOR = (245, 247, 250)          # Light gray background
        self.CARD_BG = (255, 255, 255)           # White cards
        self.SELECTED_BG = (59, 130, 246)        # Blue selection
        self.BORDER_COLOR = (209, 213, 219)      # Light borders
        self.TEXT_PRIMARY = (17, 24, 39)         # Dark text
        self.TEXT_SECONDARY = (107, 114, 128)    # Gray text
        self.ACCENT_GREEN = (34, 197, 94)        # Success green
        self.ACCENT_RED = (239, 68, 68)          # Warning red
        self.ACCENT_ORANGE = (251, 146, 60)      # Warning orange
        
        # Fonts
        pygame.font.init()
        self.title_font = pygame.font.Font(None, 32)
        self.subtitle_font = pygame.font.Font(None, 24)
        self.text_font = pygame.font.Font(None, 20)
        self.small_font = pygame.font.Font(None, 16)
        
        # Click areas
        self.clickable_areas = {}
        
        # Preview variant
        self.preview_variant = None
        
    def open_for_level(self, base_level_path: str):
        """Open the variant selection UI for a specific level"""
        self.active = True
        self.base_level_path = base_level_path
        self.base_level_name = os.path.basename(base_level_path).replace('.json', '')
        self.selected_modifier = None
        self.scroll_offset = 0
        
        # Load base config to get difficulty
        try:
            with open(base_level_path, 'r') as f:
                base_config = json.load(f)
            base_difficulty = self.variant_generator._extract_difficulty(base_config)
        except:
            base_difficulty = 50
        
        # Get available modifiers
        self.available_modifiers = self.variant_generator.get_available_modifiers(base_difficulty)
        
        # Calculate scroll limits
        self._calculate_scroll_limits()
        
    def close(self):
        """Close the variant selection UI"""
        self.active = False
        self.selected_modifier = None
        self.preview_variant = None
        
    def handle_click(self, pos: Tuple[int, int], button: int = 1) -> Optional[str]:
        """Handle mouse clicks, returns action if any
        Args:
            pos: Mouse position tuple
            button: Mouse button (1=left, 3=right)
        """
        if not self.active:
            return None

        # Handle right-click cancellation
        if button == 3:
            if self.selected_modifier:
                # Clear selection
                self.selected_modifier = None
                self._update_preview_display()
                return None
            else:
                # Close the UI
                self.close()
                return "close"

        x, y = pos
        
        # Check scroll navigation buttons first
        if self.max_scroll > 0:
            # Up button
            up_button_x = self.screen_width - 80
            up_button_y = 150
            up_button_w, up_button_h = 60, 40
            if (up_button_x <= x <= up_button_x + up_button_w and 
                up_button_y <= y <= up_button_y + up_button_h and 
                self.scroll_offset > 0):
                self.scroll_offset = max(0, self.scroll_offset - 5)  # Scroll up
                return None
            
            # Down button  
            down_button_x = self.screen_width - 80
            down_button_y = self.screen_height - 150
            down_button_w, down_button_h = 60, 40
            if (down_button_x <= x <= down_button_x + down_button_w and 
                down_button_y <= y <= down_button_y + down_button_h and 
                self.scroll_offset < self.max_scroll):
                self.scroll_offset = min(self.max_scroll, self.scroll_offset + 5)  # Scroll down
                return None
        
        # Check clickable areas
        for area_id, (area_x, area_y, area_w, area_h) in self.clickable_areas.items():
            if area_x <= x <= area_x + area_w and area_y <= y <= area_y + area_h:
                return self._handle_area_click(area_id)
        
        return None
    
    def handle_scroll(self, scroll_delta: int):
        """Handle scroll events"""
        if not self.active:
            return
        
        self.scroll_offset = max(0, min(self.max_scroll, self.scroll_offset - scroll_delta * 2))
    
    def _handle_area_click(self, area_id: str) -> Optional[str]:
        """Handle clicks on specific UI areas"""
        if area_id.startswith("modifier_"):
            # Modifier selection - only one can be selected at a time
            modifier_id = area_id.replace("modifier_", "")
            modifier = self.variant_generator.modifiers.get(modifier_id)
            
            if modifier:
                if self.selected_modifier == modifier:
                    # Deselect if clicking on already selected modifier
                    self.selected_modifier = None
                else:
                    # Select this modifier (replaces any previously selected)
                    self.selected_modifier = modifier
                
                # Update preview display but don't create variant yet
                self._update_preview_display()
        
        elif area_id == "create_variant":
            # Only create variant when this button is clicked
            if self.selected_modifier:
                return "create_variant"
        
        elif area_id == "close":
            self.close()
            return "close"
        
        elif area_id == "preset_easy":
            self._apply_preset("easy")
        
        elif area_id == "preset_challenge":
            self._apply_preset("challenge")
        
        elif area_id == "preset_extreme":
            self._apply_preset("extreme")
        
        elif area_id == "clear_all":
            self.selected_modifier = None
            self._update_preview_display()
        
        return None
    

    
    def _apply_preset(self, preset_type: str):
        """Apply a preset modifier selection"""
        self.selected_modifier = None
        
        if preset_type == "easy":
            # Fun but not too challenging
            easy_mods = [m for m in self.variant_generator.modifiers.values() 
                        if m.difficulty in [ModifierDifficulty.EASY, ModifierDifficulty.NORMAL] 
                        and m.fun_factor >= 7]
            if easy_mods:
                self.selected_modifier = easy_mods[0]
        
        elif preset_type == "challenge":
            # Balanced challenge
            challenge_mods = [m for m in self.variant_generator.modifiers.values() 
                            if m.difficulty == ModifierDifficulty.HARD 
                            and m.fun_factor >= 6]
            if challenge_mods:
                self.selected_modifier = challenge_mods[0]
        
        elif preset_type == "extreme":
            # Maximum difficulty
            extreme_mods = [m for m in self.variant_generator.modifiers.values() 
                          if m.difficulty == ModifierDifficulty.EXTREME]
            if extreme_mods:
                self.selected_modifier = extreme_mods[0]
        
        self._update_preview_display()
    
    def _update_preview_display(self):
        """Update the preview display without creating the actual variant"""
        if self.selected_modifier and self.base_level_path:
            # Just show basic preview info without creating the variant
            self.preview_variant = None  # Clear any existing preview
            # We'll show preview info in the draw method based on selected_modifier
        else:
            self.preview_variant = None
    
    def _calculate_scroll_limits(self):
        """Calculate scrolling limits based on content"""
        # Calculate actual content height more accurately
        header_height = 120  # Header + preset buttons
        preview_height = 220  # Preview panel (increased for more detailed info)
        action_buttons_height = 80  # Action buttons at bottom
        section_spacing = 30
        
        # Calculate modifier sections height
        modifier_sections_height = 0
        for section_name, modifiers in self.available_modifiers.items():
            section_header_height = 35
            cards_per_row = 2
            card_height = 80
            card_spacing = 10
            
            rows_needed = (len(modifiers) + cards_per_row - 1) // cards_per_row
            section_content_height = rows_needed * (card_height + card_spacing)
            
            modifier_sections_height += section_header_height + section_content_height + section_spacing
        
        total_content_height = header_height + modifier_sections_height + preview_height + action_buttons_height
        
        # Set max scroll if content exceeds visible area
        if total_content_height > self.content_height:
            self.max_scroll = max(0, (total_content_height - self.content_height) // 20 + 5)
        else:
            self.max_scroll = 0
    
    def draw(self, screen: pygame.Surface):
        """Draw the variant selection UI"""
        if not self.active:
            return
        
        # Clear clickable areas
        self.clickable_areas = {}
        
        # Background
        screen.fill(self.BG_COLOR)
        
        # Main container
        container_rect = pygame.Rect(self.margin, 50, self.content_width, self.content_height)
        pygame.draw.rect(screen, self.CARD_BG, container_rect, border_radius=12)
        pygame.draw.rect(screen, self.BORDER_COLOR, container_rect, 2, border_radius=12)
        
        # Calculate scroll offset
        scroll_y = self.scroll_offset * 20
        
        # Header
        header_y = 70 - scroll_y
        self._draw_header(screen, header_y)
        
        # Preset buttons
        preset_y = header_y + 80
        self._draw_preset_buttons(screen, preset_y)
        
        # Modifier sections
        section_y = preset_y + 60
        section_y = self._draw_modifier_sections(screen, section_y)
        
        # Preview panel
        preview_y = section_y + 20
        self._draw_preview_panel(screen, preview_y)
        
        # Action buttons
        self._draw_action_buttons(screen)
        
        # Draw navigation buttons
        self._draw_navigation_buttons(screen)
    
    def _draw_header(self, screen: pygame.Surface, y: int):
        """Draw the header section"""
        if y < -100 or y > self.screen_height:
            return  # Skip if not visible
        
        # Title
        title = f"Create Variant: {self.base_level_name.title()}"
        title_text = self.title_font.render(title, True, self.TEXT_PRIMARY)
        title_rect = title_text.get_rect(centerx=self.screen_width // 2, y=y)
        screen.blit(title_text, title_rect)
        
        # Subtitle
        subtitle = "Select one modifier to create a custom challenge variant"
        subtitle_text = self.text_font.render(subtitle, True, self.TEXT_SECONDARY)
        subtitle_rect = subtitle_text.get_rect(centerx=self.screen_width // 2, y=y + 35)
        screen.blit(subtitle_text, subtitle_rect)
    
    def _draw_preset_buttons(self, screen: pygame.Surface, y: int):
        """Draw preset combination buttons"""
        if y < -50 or y > self.screen_height:
            return  # Skip if not visible
        
        button_width = 120
        button_height = 35
        button_spacing = 20
        
        # Center the buttons
        total_width = 4 * button_width + 3 * button_spacing
        start_x = (self.screen_width - total_width) // 2
        
        buttons = [
            ("preset_easy", "Fun & Easy", self.ACCENT_GREEN),
            ("preset_challenge", "Challenge", self.ACCENT_ORANGE),
            ("preset_extreme", "Extreme", self.ACCENT_RED),
            ("clear_all", "Clear All", self.TEXT_SECONDARY)
        ]
        
        for i, (btn_id, btn_text, btn_color) in enumerate(buttons):
            btn_x = start_x + i * (button_width + button_spacing)
            btn_rect = pygame.Rect(btn_x, y, button_width, button_height)
            
            # Draw button
            pygame.draw.rect(screen, btn_color, btn_rect, border_radius=8)
            if btn_id == "clear_all":
                pygame.draw.rect(screen, self.CARD_BG, btn_rect.inflate(-2, -2), border_radius=6)
            
            # Button text
            text_color = self.CARD_BG if btn_id != "clear_all" else btn_color
            btn_text_surf = self.text_font.render(btn_text, True, text_color)
            btn_text_rect = btn_text_surf.get_rect(center=btn_rect.center)
            screen.blit(btn_text_surf, btn_text_rect)
            
            # Store clickable area
            self.clickable_areas[btn_id] = (btn_x, y, button_width, button_height)
    
    def _draw_modifier_sections(self, screen: pygame.Surface, start_y: int) -> int:
        """Draw modifier sections grouped by type"""
        current_y = start_y
        
        for section_name, modifiers in self.available_modifiers.items():
            if current_y > self.screen_height + 100:
                break  # Stop if way off screen
            
            # Section header
            if current_y > -50:  # Only draw if potentially visible
                section_title = section_name.replace('_', ' ').title()
                header_text = self.subtitle_font.render(section_title, True, self.TEXT_PRIMARY)
                screen.blit(header_text, (self.margin + 20, current_y))
            
            current_y += 35
            
            # Modifier cards
            cards_per_row = 2
            card_width = (self.content_width - 60) // cards_per_row
            card_height = 80
            card_spacing = 20
            
            for i, modifier in enumerate(modifiers):
                row = i // cards_per_row
                col = i % cards_per_row
                
                card_x = self.margin + 20 + col * (card_width + card_spacing)
                card_y = current_y + row * (card_height + 10)
                
                if card_y > self.screen_height + 100:
                    break  # Stop if way off screen
                
                if card_y > -card_height:  # Only draw if potentially visible
                    self._draw_modifier_card(screen, modifier, card_x, card_y, card_width, card_height)
            
            # Calculate next section position
            rows_needed = (len(modifiers) + cards_per_row - 1) // cards_per_row
            current_y += rows_needed * (card_height + 10) + 30
        
        return current_y
    
    def _draw_modifier_card(self, screen: pygame.Surface, modifier: LevelModifier, 
                           x: int, y: int, width: int, height: int):
        """Draw a single modifier card"""
        # Card background
        is_selected = modifier == self.selected_modifier
        
        if is_selected:
            card_color = self.SELECTED_BG
            text_color = self.CARD_BG
            border_color = self.SELECTED_BG
        else:
            card_color = self.CARD_BG
            text_color = self.TEXT_PRIMARY
            border_color = self.BORDER_COLOR
        
        card_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(screen, card_color, card_rect, border_radius=8)
        pygame.draw.rect(screen, border_color, card_rect, 2, border_radius=8)
        
        # Store clickable area
        self.clickable_areas[f"modifier_{modifier.id}"] = (x, y, width, height)
        
        # Modifier name
        name_text = self.text_font.render(modifier.name, True, text_color)
        name_rect = name_text.get_rect(x=x + 10, y=y + 8)
        if name_rect.width > width - 100:  # Truncate if too long
            name_text = self.text_font.render(modifier.name[:20] + "...", True, text_color)
        screen.blit(name_text, name_rect)
        
        # Difficulty indicator
        diff_colors = {
            ModifierDifficulty.EASY: self.ACCENT_GREEN,
            ModifierDifficulty.NORMAL: (59, 130, 246),  # Blue
            ModifierDifficulty.HARD: self.ACCENT_ORANGE,
            ModifierDifficulty.EXTREME: self.ACCENT_RED
        }
        diff_color = diff_colors.get(modifier.difficulty, self.TEXT_SECONDARY)
        
        diff_x = x + width - 60
        diff_y = y + 8
        diff_rect = pygame.Rect(diff_x, diff_y, 50, 20)
        pygame.draw.rect(screen, diff_color, diff_rect, border_radius=10)
        
        diff_text = self.small_font.render(modifier.difficulty.value.upper(), True, self.CARD_BG)
        diff_text_rect = diff_text.get_rect(center=diff_rect.center)
        screen.blit(diff_text, diff_text_rect)
        
        # Description
        desc_text = self.small_font.render(modifier.description[:60] + "..." if len(modifier.description) > 60 else modifier.description, 
                                         True, text_color)
        screen.blit(desc_text, (x + 10, y + 35))
        
        # Reward multiplier
        reward_text = self.small_font.render(f"Reward: {modifier.reward_multiplier:.1f}x", True, text_color)
        screen.blit(reward_text, (x + 10, y + 55))
    
    def _draw_preview_panel(self, screen: pygame.Surface, y: int):
        """Draw the variant preview panel with specific changes"""
        if y > self.screen_height or not self.selected_modifier:
            return
        
        panel_height = 200  # Increased height for more detailed information
        panel_rect = pygame.Rect(self.margin + 20, y, self.content_width - 40, panel_height)
        
        # Panel background
        pygame.draw.rect(screen, (248, 250, 252), panel_rect, border_radius=8)
        pygame.draw.rect(screen, self.BORDER_COLOR, panel_rect, 2, border_radius=8)
        
        # Panel header
        header_text = self.subtitle_font.render("Variant Preview", True, self.TEXT_PRIMARY)
        screen.blit(header_text, (panel_rect.x + 15, panel_rect.y + 10))
        
        if self.selected_modifier:
            # Load base config to show specific changes
            try:
                with open(self.base_level_path, 'r') as f:
                    base_config = json.load(f)
                
                # Show specific changes based on the modifier
                info_y = panel_rect.y + 40
                line_height = 20
                current_line = 0
                
                # Name preview
                variant_name = f"{self.base_level_name.title()}: {self.selected_modifier.name}"
                name_text = self.text_font.render(f"Name: {variant_name}", True, self.TEXT_PRIMARY)
                screen.blit(name_text, (panel_rect.x + 15, info_y + current_line * line_height))
                current_line += 1
                
                # Show specific changes based on config_changes
                changes_shown = False
                for config_key, config_value in self.selected_modifier.config_changes.items():
                    change_text = self._get_change_description(base_config, config_key, config_value)
                    if change_text:
                        change_surface = self.text_font.render(change_text, True, self.ACCENT_ORANGE)
                        screen.blit(change_surface, (panel_rect.x + 15, info_y + current_line * line_height))
                        current_line += 1
                        changes_shown = True
                        
                        # Limit to avoid overflow
                        if current_line >= 6:
                            break
                
                # If no specific changes shown, show generic info
                if not changes_shown:
                    desc_text = self.text_font.render(f"Effect: {self.selected_modifier.description}", True, self.TEXT_SECONDARY)
                    screen.blit(desc_text, (panel_rect.x + 15, info_y + current_line * line_height))
                    current_line += 1
                
                # Difficulty and reward info
                diff_text = self.text_font.render(f"Difficulty: {self.selected_modifier.difficulty_multiplier:.1f}x", True, self.ACCENT_ORANGE)
                screen.blit(diff_text, (panel_rect.x + 15, info_y + current_line * line_height))
                current_line += 1
                
                reward_text = self.text_font.render(f"Reward: {self.selected_modifier.reward_multiplier:.1f}x", True, self.ACCENT_GREEN)
                screen.blit(reward_text, (panel_rect.x + 15, info_y + current_line * line_height))
                current_line += 1
                
                # Ready status
                ready_text = self.text_font.render("Ready to create variant", True, self.ACCENT_GREEN)
                screen.blit(ready_text, (panel_rect.x + 300, info_y))
                
            except Exception as e:
                # Fallback to generic preview if config loading fails
                error_text = self.text_font.render("Error loading base config for preview", True, self.ACCENT_RED)
                screen.blit(error_text, (panel_rect.x + 15, info_y))
    
    def _get_change_description(self, base_config: dict, config_key: str, config_value: Any) -> Optional[str]:
        """Get a human-readable description of what a config change will do"""
        game_config = base_config.get('game_config', {})
        wave_config = base_config.get('wave_config', {})
        
        # Money changes
        if config_key == "starting_money_multiplier":
            base_money = game_config.get('starting_money', 20)
            new_money = max(1, int(base_money * config_value))
            reduction_percent = int((1 - config_value) * 100)
            return f"Starting money: {base_money} → {new_money} ({reduction_percent}% reduction)"
        
        elif config_key == "starting_money_override":
            base_money = game_config.get('starting_money', 20)
            return f"Starting money: {base_money} → {config_value}"
        
        # Lives changes
        elif config_key == "starting_lives_multiplier":
            base_lives = game_config.get('starting_lives', 20)
            new_lives = max(1, int(base_lives * config_value))
            reduction_percent = int((1 - config_value) * 100)
            return f"Starting lives: {base_lives} → {new_lives} ({reduction_percent}% reduction)"
        
        elif config_key == "starting_lives_override":
            base_lives = game_config.get('starting_lives', 20)
            return f"Starting lives: {base_lives} → {config_value}"
        
        # Tower cost changes
        elif config_key == "tower_cost_multiplier":
            increase_percent = int((config_value - 1) * 100)
            return f"Tower costs: +{increase_percent}% more expensive"
        
        # Tower limits
        elif config_key == "max_towers":
            return f"Max towers: No limit → {config_value} towers"
        
        # Speed changes
        elif config_key == "spawn_speed_multiplier":
            increase_percent = int((config_value - 1) * 100)
            return f"Enemy spawn speed: +{increase_percent}% faster"
        
        # Enemy health changes
        elif config_key == "enemy_health_boost":
            increase_percent = int(config_value * 100)
            return f"Enemy health: +{increase_percent}% stronger"
        
        # Enemy count changes
        elif config_key == "enemy_count_multiplier":
            base_count = wave_config.get('spawn_config', {}).get('base_enemy_count', 5)
            new_count = max(1, int(base_count * config_value))
            increase_percent = int((config_value - 1) * 100)
            return f"Enemy count: {base_count} → {new_count} per wave (+{increase_percent}%)"
        
        # Wave count changes
        elif config_key == "wave_count_multiplier":
            base_waves = wave_config.get('total_waves', 80)
            new_waves = max(1, int(base_waves * config_value))
            increase_percent = int((config_value - 1) * 100)
            return f"Total waves: {base_waves} → {new_waves} (+{increase_percent}%)"
        
        # Boss wave changes
        elif config_key == "boss_wave_multiplier":
            increase_percent = int((config_value - 1) * 100)
            return f"Boss waves: +{increase_percent}% more boss encounters"
        
        # Special round changes
        elif config_key == "special_round_boost":
            increase_percent = int(config_value * 100)
            return f"Special rounds: +{increase_percent}% more enemies"
        
        # Unknown change
        else:
            return f"{config_key}: {config_value}"
    
    def _draw_action_buttons(self, screen: pygame.Surface):
        """Draw action buttons at the bottom"""
        button_y = self.screen_height - 60
        button_height = 40
        button_spacing = 20
        
        # Create Variant button
        create_enabled = self.selected_modifier is not None
        create_width = 200
        create_x = self.screen_width // 2 - create_width // 2 - button_spacing // 2
        
        create_color = self.ACCENT_GREEN if create_enabled else self.TEXT_SECONDARY
        create_rect = pygame.Rect(create_x, button_y, create_width, button_height)
        
        pygame.draw.rect(screen, create_color, create_rect, border_radius=8)
        if not create_enabled:
            pygame.draw.rect(screen, self.CARD_BG, create_rect.inflate(-2, -2), border_radius=6)
        
        create_text_color = self.CARD_BG if create_enabled else create_color
        create_text = self.text_font.render("Create Variant", True, create_text_color)
        create_text_rect = create_text.get_rect(center=create_rect.center)
        screen.blit(create_text, create_text_rect)
        
        if create_enabled:
            self.clickable_areas["create_variant"] = (create_x, button_y, create_width, button_height)
        
        # Close button
        close_width = 100
        close_x = create_x + create_width + button_spacing
        close_rect = pygame.Rect(close_x, button_y, close_width, button_height)
        
        pygame.draw.rect(screen, self.TEXT_SECONDARY, close_rect, border_radius=8)
        pygame.draw.rect(screen, self.CARD_BG, close_rect.inflate(-2, -2), border_radius=6)
        
        close_text = self.text_font.render("Close", True, self.TEXT_SECONDARY)
        close_text_rect = close_text.get_rect(center=close_rect.center)
        screen.blit(close_text, close_text_rect)
        
        self.clickable_areas["close"] = (close_x, button_y, close_width, button_height)
    
    def _draw_navigation_buttons(self, screen: pygame.Surface):
        """Draw navigation buttons for scrolling"""
        if self.max_scroll <= 0:
            return  # No scrolling needed
        
        # Button dimensions and colors
        button_width, button_height = 60, 40
        up_button_x = self.screen_width - 80
        down_button_x = self.screen_width - 80
        up_button_y = 150
        down_button_y = self.screen_height - 150
        
        # Up button
        up_enabled = self.scroll_offset > 0
        up_color = self.SELECTED_BG if up_enabled else (70, 70, 70)
        up_rect = pygame.Rect(up_button_x, up_button_y, button_width, button_height)
        
        pygame.draw.rect(screen, up_color, up_rect, border_radius=8)
        pygame.draw.rect(screen, self.BORDER_COLOR, up_rect, 2, border_radius=8)
        
        # Add a subtle shadow effect for enabled buttons
        if up_enabled:
            shadow_rect = pygame.Rect(up_button_x + 2, up_button_y + 2, button_width, button_height)
            pygame.draw.rect(screen, (0, 0, 0, 50), shadow_rect, border_radius=8)
        
        # Up arrow
        up_text = self.subtitle_font.render("↑", True, self.CARD_BG if up_enabled else (150, 150, 150))
        up_text_rect = up_text.get_rect(center=up_rect.center)
        screen.blit(up_text, up_text_rect)
        
        # Down button
        down_enabled = self.scroll_offset < self.max_scroll
        down_color = self.SELECTED_BG if down_enabled else (70, 70, 70)
        down_rect = pygame.Rect(down_button_x, down_button_y, button_width, button_height)
        
        pygame.draw.rect(screen, down_color, down_rect, border_radius=8)
        pygame.draw.rect(screen, self.BORDER_COLOR, down_rect, 2, border_radius=8)
        
        # Add a subtle shadow effect for enabled buttons
        if down_enabled:
            shadow_rect = pygame.Rect(down_button_x + 2, down_button_y + 2, button_width, button_height)
            pygame.draw.rect(screen, (0, 0, 0, 50), shadow_rect, border_radius=8)
        
        # Down arrow
        down_text = self.subtitle_font.render("↓", True, self.CARD_BG if down_enabled else (150, 150, 150))
        down_text_rect = down_text.get_rect(center=down_rect.center)
        screen.blit(down_text, down_text_rect)
        
        # Optional: Add scroll position indicator
        if self.max_scroll > 0:
            # Calculate scroll position as percentage
            scroll_percent = self.scroll_offset / self.max_scroll
            indicator_height = 100
            indicator_y = up_button_y + button_height + 20
            
            # Background track
            track_rect = pygame.Rect(up_button_x + 25, indicator_y, 10, indicator_height)
            pygame.draw.rect(screen, self.BORDER_COLOR, track_rect, border_radius=5)
            
            # Scroll thumb
            thumb_height = max(10, indicator_height // 4)
            thumb_y = indicator_y + int((indicator_height - thumb_height) * scroll_percent)
            thumb_rect = pygame.Rect(up_button_x + 25, thumb_y, 10, thumb_height)
            pygame.draw.rect(screen, self.ACCENT_ORANGE, thumb_rect, border_radius=5)
    
    def get_selected_variant(self) -> Optional[LevelVariant]:
        """Create and return the selected variant"""
        if self.selected_modifier and self.base_level_path:
            try:
                variant = self.variant_generator.create_variant(
                    self.base_level_path, 
                    [self.selected_modifier],  # Convert single modifier to list for API
                    None  # Let it auto-generate name
                )
                return variant
            except Exception as e:
                print(f"Error creating variant: {e}")
                return None
        return None 