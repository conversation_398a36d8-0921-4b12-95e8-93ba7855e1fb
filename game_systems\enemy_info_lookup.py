import pygame
import math
from typing import Dict, List, Optional
from config.game_config import get_wave_config

class EnemyInfoLookup:
    """System for looking up enemy information on demand"""
    
    def __init__(self, screen_width: int, screen_height: int):
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # UI state
        self.show_lookup = False
        self.selected_enemy = None
        self.scroll_offset = 0
        self.max_scroll = 0
        
        # UI dimensions
        self.panel_width = 800
        self.panel_height = 600
        self.panel_x = (screen_width - self.panel_width) // 2
        self.panel_y = (screen_height - self.panel_height) // 2
        
        # Enemy list dimensions (now takes more space since no search box)
        self.list_start_y = self.panel_y + 60  # Start after title
        self.list_height = 250  # Increased height for better browsing
        self.item_height = 35   # Larger for icon + text
        
        # Enemy detail area
        self.detail_start_y = self.list_start_y + self.list_height + 20
        self.detail_height = self.panel_height - (self.detail_start_y - self.panel_y) - 40
        
        # Icon dimensions
        self.icon_size = 28
        self.icon_margin = 5
        
        # Fonts
        self.title_font = pygame.font.Font(None, 32)
        self.header_font = pygame.font.Font(None, 24)
        self.text_font = pygame.font.Font(None, 20)
        self.small_font = pygame.font.Font(None, 16)
        
        # Colors
        self.bg_color = (30, 30, 40)
        self.border_color = (100, 100, 120)
        self.selected_color = (60, 60, 80)
        self.text_color = (255, 255, 255)
        self.highlight_color = (100, 150, 255)
        self.scrollbar_color = (80, 80, 100)
        self.scrollbar_handle_color = (120, 120, 140)
        
        # Scrollbar dimensions
        self.scrollbar_width = 20
        self.scrollbar_x = self.panel_x + self.panel_width - 40
        
        # Load dynamic wave information from game config
        self.wave_config = get_wave_config()
        self.enemy_wave_ranges = self._extract_wave_ranges()
        
        # Enemy information database (organized by tier for better browsing)
        self.enemy_info = self._get_organized_enemy_info()
        
        # Sort enemies by tier and name for organized display
        tier_order = ['Basic', 'Intermediate', 'Advanced', 'Elite', 'Legendary', 'Boss', 'Ultimate Boss']
        self.sorted_enemies = sorted(
            self.enemy_info.items(),
            key=lambda x: (tier_order.index(x[1]['tier']) if x[1]['tier'] in tier_order else 999, x[1]['name'])
        )
        
    def _extract_wave_ranges(self) -> Dict[str, str]:
        """Extract actual wave ranges for each enemy from game configuration"""
        wave_ranges = {}
        
        # Get wave compositions
        wave_compositions = self.wave_config.get('wave_compositions', {})
        for wave_range, enemies in wave_compositions.items():
            if isinstance(wave_range, tuple):
                range_str = f"{wave_range[0]}-{wave_range[1]}"
            else:
                range_str = str(wave_range)
            
            for enemy_data in enemies:
                enemy_name = enemy_data[0]  # First element is enemy name
                if enemy_name not in wave_ranges:
                    wave_ranges[enemy_name] = []
                wave_ranges[enemy_name].append(range_str)
        
        # Get boss waves
        boss_waves = self.wave_config.get('boss_waves', {})
        boss_wave_list = []
        for wave_num, boss_name in boss_waves.items():
            boss_wave_list.append(str(wave_num))
            if boss_name not in wave_ranges:
                wave_ranges[boss_name] = []
            wave_ranges[boss_name].append(f"Wave {wave_num}")
        
        # Convert lists to readable strings
        for enemy_name, ranges in wave_ranges.items():
            if len(ranges) == 1:
                wave_ranges[enemy_name] = ranges[0]
            else:
                # Combine ranges for readability
                wave_ranges[enemy_name] = ", ".join(ranges)
        
        return wave_ranges
    
    def _get_tier_from_wave_range(self, enemy_name: str) -> str:
        """Determine tier based on when enemy first appears"""
        wave_range = self.enemy_wave_ranges.get(enemy_name, "1")
        
        # Extract first wave number
        first_wave = 1
        if "Wave" in wave_range:
            # Boss enemy
            return "Boss" if enemy_name not in ["TimeLordBoss", "NecromancerBoss", "ShadowKing", "CrystalOverlord"] else "Ultimate Boss"
        elif "-" in wave_range:
            first_wave = int(wave_range.split("-")[0])
        else:
            try:
                first_wave = int(wave_range)
            except:
                first_wave = 1
        
        # Determine tier based on first appearance
        if first_wave <= 10:
            return "Basic"
        elif first_wave <= 25:
            return "Intermediate"
        elif first_wave <= 40:
            return "Advanced"
        elif first_wave <= 60:
            return "Elite"
        else:
            return "Legendary"
    
    def _draw_enemy_icon(self, screen: pygame.Surface, enemy_name: str, x: int, y: int, color: tuple):
        """Draw a visual icon for the enemy"""
        center_x = x + self.icon_size // 2
        center_y = y + self.icon_size // 2
        
        # Create icon based on enemy type
        if "Basic" in enemy_name:
            # Simple circle
            pygame.draw.circle(screen, color, (center_x, center_y), self.icon_size // 3)
            pygame.draw.circle(screen, (255, 255, 255), (center_x, center_y), self.icon_size // 3, 2)
            
        elif "Fast" in enemy_name:
            # Triangle pointing right with speed lines
            points = [(center_x - 8, center_y - 6), (center_x + 8, center_y), (center_x - 8, center_y + 6)]
            pygame.draw.polygon(screen, color, points)
            pygame.draw.polygon(screen, (255, 255, 255), points, 2)
            # Speed lines
            for i in range(3):
                line_x = center_x - 12 - i * 3
                pygame.draw.line(screen, color, (line_x, center_y - 2), (line_x, center_y + 2), 2)
                
        elif "Tank" in enemy_name:
            # Rectangle (tank-like)
            rect = pygame.Rect(center_x - 10, center_y - 6, 20, 12)
            pygame.draw.rect(screen, color, rect)
            pygame.draw.rect(screen, (255, 255, 255), rect, 2)
            # Tank barrel
            pygame.draw.line(screen, color, (center_x + 10, center_y), (center_x + 16, center_y), 3)
            
        elif "Flying" in enemy_name:
            # Wing shape
            pygame.draw.ellipse(screen, color, (center_x - 12, center_y - 4, 10, 8))
            pygame.draw.ellipse(screen, color, (center_x + 2, center_y - 4, 10, 8))
            pygame.draw.circle(screen, color, (center_x, center_y), 4)
            pygame.draw.circle(screen, (255, 255, 255), (center_x, center_y), 4, 1)
            
        elif "Shielded" in enemy_name:
            # Circle with shield effect
            pygame.draw.circle(screen, color, (center_x, center_y), self.icon_size // 3)
            # Shield lines
            for angle in [0, 60, 120, 180, 240, 300]:
                rad = math.radians(angle)
                start_x = center_x + math.cos(rad) * 8
                start_y = center_y + math.sin(rad) * 8
                end_x = center_x + math.cos(rad) * 12
                end_y = center_y + math.sin(rad) * 12
                pygame.draw.line(screen, (0, 255, 255), (start_x, start_y), (end_x, end_y), 2)
                
        elif "Invisible" in enemy_name:
            # Dotted circle to show partial visibility
            for angle in range(0, 360, 30):
                rad = math.radians(angle)
                dot_x = center_x + math.cos(rad) * 8
                dot_y = center_y + math.sin(rad) * 8
                pygame.draw.circle(screen, color, (int(dot_x), int(dot_y)), 2)
                
        elif "Armored" in enemy_name:
            # Hexagon (armor plating)
            points = []
            for angle in range(0, 360, 60):
                rad = math.radians(angle)
                point_x = center_x + math.cos(rad) * 8
                point_y = center_y + math.sin(rad) * 8
                points.append((point_x, point_y))
            pygame.draw.polygon(screen, color, points)
            pygame.draw.polygon(screen, (255, 255, 255), points, 2)
            
        elif "Regenerating" in enemy_name:
            # Circle with plus symbols
            pygame.draw.circle(screen, color, (center_x, center_y), self.icon_size // 3)
            pygame.draw.line(screen, (0, 255, 0), (center_x - 4, center_y), (center_x + 4, center_y), 2)
            pygame.draw.line(screen, (0, 255, 0), (center_x, center_y - 4), (center_x, center_y + 4), 2)
            
        elif "Teleporting" in enemy_name:
            # Circle with teleport effect
            pygame.draw.circle(screen, color, (center_x, center_y), self.icon_size // 4)
            # Teleport rings
            for radius in [6, 10]:
                pygame.draw.circle(screen, color, (center_x, center_y), radius, 1)
                
        elif "Splitting" in enemy_name:
            # Two connected circles
            pygame.draw.circle(screen, color, (center_x - 4, center_y), 6)
            pygame.draw.circle(screen, color, (center_x + 4, center_y), 6)
            pygame.draw.line(screen, color, (center_x - 4, center_y), (center_x + 4, center_y), 2)
            
        elif "Crystal" in enemy_name:
            # Diamond shape
            points = [(center_x, center_y - 10), (center_x + 8, center_y), 
                     (center_x, center_y + 10), (center_x - 8, center_y)]
            pygame.draw.polygon(screen, color, points)
            pygame.draw.polygon(screen, (255, 255, 255), points, 2)
            
        elif "Fire" in enemy_name or "Elemental" in enemy_name:
            # Flame shape
            flame_points = [(center_x, center_y + 8), (center_x - 6, center_y + 2), 
                           (center_x - 3, center_y - 8), (center_x + 3, center_y - 8), 
                           (center_x + 6, center_y + 2)]
            pygame.draw.polygon(screen, color, flame_points)
            
        elif "Spectral" in enemy_name or "Phase" in enemy_name:
            # Wavy circle (ghostly)
            for i in range(8):
                angle = i * 45
                rad = math.radians(angle)
                radius = 8 + math.sin(math.radians(angle * 4)) * 2
                point_x = center_x + math.cos(rad) * radius
                point_y = center_y + math.sin(rad) * radius
                pygame.draw.circle(screen, color, (int(point_x), int(point_y)), 2)
                
        elif "Toxic" in enemy_name:
            # Circle with toxic bubbles
            pygame.draw.circle(screen, color, (center_x, center_y), self.icon_size // 3)
            for i, (offset_x, offset_y) in enumerate([(-8, -8), (8, -8), (0, 10)]):
                bubble_color = (100 + i * 50, 255, 100)
                pygame.draw.circle(screen, bubble_color, (center_x + offset_x, center_y + offset_y), 3)
                
        elif "Void" in enemy_name:
            # Black hole effect
            pygame.draw.circle(screen, (0, 0, 0), (center_x, center_y), 8)
            pygame.draw.circle(screen, color, (center_x, center_y), 8, 2)
            for radius in [4, 6]:
                pygame.draw.circle(screen, color, (center_x, center_y), radius, 1)
                
        elif "Adaptive" in enemy_name:
            # Changing pattern
            pygame.draw.circle(screen, color, (center_x, center_y), 6)
            for i in range(4):
                angle = i * 90
                rad = math.radians(angle)
                segment_color = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)][i]
                start_angle = math.radians(angle - 20)
                end_angle = math.radians(angle + 20)
                # Draw arc segments
                pygame.draw.arc(screen, segment_color, 
                              (center_x - 10, center_y - 10, 20, 20), start_angle, end_angle, 3)
                
        elif "Boss" in enemy_name:
            # Crown-like shape for bosses
            if "Speed" in enemy_name:
                # Lightning bolt
                points = [(center_x - 4, center_y - 8), (center_x + 2, center_y - 2), 
                         (center_x - 2, center_y - 2), (center_x + 4, center_y + 8), 
                         (center_x - 2, center_y + 2), (center_x + 2, center_y + 2)]
                pygame.draw.polygon(screen, color, points)
            elif "Mega" in enemy_name:
                # Large square
                rect = pygame.Rect(center_x - 8, center_y - 8, 16, 16)
                pygame.draw.rect(screen, color, rect)
                pygame.draw.rect(screen, (255, 255, 255), rect, 2)
            elif "Time" in enemy_name:
                # Clock-like
                pygame.draw.circle(screen, color, (center_x, center_y), 8)
                pygame.draw.line(screen, (255, 255, 255), (center_x, center_y), (center_x, center_y - 6), 2)
                pygame.draw.line(screen, (255, 255, 255), (center_x, center_y), (center_x + 4, center_y), 2)
            elif "Necromancer" in enemy_name:
                # Skull-like
                pygame.draw.circle(screen, color, (center_x, center_y), 8)
                pygame.draw.circle(screen, (0, 0, 0), (center_x - 3, center_y - 2), 2)
                pygame.draw.circle(screen, (0, 0, 0), (center_x + 3, center_y - 2), 2)
            elif "Shadow" in enemy_name:
                # Dark crown
                points = [(center_x - 8, center_y + 4), (center_x - 4, center_y - 6), 
                         (center_x, center_y), (center_x + 4, center_y - 6), (center_x + 8, center_y + 4)]
                pygame.draw.polygon(screen, color, points)
            else:
                # Default boss crown
                points = [(center_x - 6, center_y + 2), (center_x - 2, center_y - 6), 
                         (center_x, center_y - 2), (center_x + 2, center_y - 6), (center_x + 6, center_y + 2)]
                pygame.draw.polygon(screen, color, points)
        else:
            # Default circle for unknown types
            pygame.draw.circle(screen, color, (center_x, center_y), self.icon_size // 3)
            pygame.draw.circle(screen, (255, 255, 255), (center_x, center_y), self.icon_size // 3, 2)

    def _get_organized_enemy_info(self) -> Dict:
        """Get enemy info organized by tiers for better browsing"""
        # Base enemy definitions
        base_info = {
            'BasicEnemy': {
                'name': 'Basic Enemy',
                'description': 'Standard ground unit with no special abilities.',
                'counters': 'Any tower works well. BasicTower with poison support is cost-effective.',
                'color': (255, 100, 100),
                'icon': 'BASIC',
                'immunities': ['None'],
                'special_abilities': ['None']
            },
            'FastEnemy': {
                'name': 'Fast Enemy',
                'description': 'Double-speed enemy that moves at 2x normal rate.',
                'counters': 'Use FreezerTower, IceTower, or SniperTower for effective hits.',
                'color': (255, 165, 0),
                'icon': 'FAST',
                'immunities': ['None'],
                'special_abilities': ['Double movement speed']
            },
            'TankEnemy': {
                'name': 'Tank Enemy',
                'description': 'Heavily armored slow enemy with high health.',
                'counters': 'Use CannonTower or focus fire with multiple towers.',
                'color': (128, 128, 128),
                'icon': 'TANK',
                'immunities': ['High physical resistance'],
                'special_abilities': ['High health', 'Slow movement']
            },
            'FlyingEnemy': {
                'name': 'Flying Enemy',
                'description': 'Airborne enemy that cannot be targeted by ground-only towers.',
                'counters': 'Requires AntiAirTower, MissileTower, or IceTower.',
                'color': (173, 216, 230),
                'icon': 'FLY',
                'immunities': ['Ground-only tower attacks'],
                'special_abilities': ['Flight', 'Hovering animation']
            },
            'ShieldedEnemy': {
                'name': 'Shielded Enemy',
                'description': 'Energy shield provides damage reduction and regenerates over time.',
                'counters': 'Use CannonTower for kinetic damage that bypasses shields.',
                'color': (100, 149, 237),
                'icon': 'SHLD',
                'immunities': ['Partial energy resistance'],
                'special_abilities': ['Regenerating shield', 'Shield blocks damage']
            },
            'InvisibleEnemy': {
                'name': 'Invisible Enemy',
                'description': 'Cannot be targeted by any tower without DetectorTower.',
                'counters': 'MANDATORY DetectorTower first, then AntiAir or Sniper.',
                'color': (169, 169, 169),
                'icon': 'INVIS',
                'immunities': ['Invisibility (requires detection)'],
                'special_abilities': ['Complete invisibility', 'Stealth movement']
            },
            'ArmoredEnemy': {
                'name': 'Armored Enemy',
                'description': 'Heavy physical armor provides high resistance to basic attacks.',
                'counters': 'Use CannonTower, LaserTower, or FlameTower for armor penetration.',
                'color': (255, 215, 0),
                'icon': 'ARM',
                'immunities': ['High physical armor'],
                'special_abilities': ['Armor plating', 'Basic attack resistance']
            },
            'RegeneratingEnemy': {
                'name': 'Regenerating Enemy',
                'description': 'Continuously heals over time. Must overwhelm regeneration rate.',
                'counters': 'Use PoisonTower to stop healing or burst damage with SplashTower.',
                'color': (0, 255, 127),
                'icon': 'REGEN',
                'immunities': ['Slow damage over time'],
                'special_abilities': ['Health regeneration', 'Healing over time']
            },
            'TeleportingEnemy': {
                'name': 'Teleporting Enemy',
                'description': 'Teleports forward when hit (50% chance). Jumps 1/3 of remaining path.',
                'counters': 'Use area damage: ExplosiveTower, SplashTower, or FreezerTower.',
                'color': (138, 43, 226),
                'icon': 'TELE',
                'immunities': ['None'],
                'special_abilities': ['Teleportation on hit', 'Dimensional jumps']
            },
            'SplittingEnemy': {
                'name': 'Splitting Enemy',
                'description': 'Splits into 2 random enemies when destroyed.',
                'counters': 'Use AOE damage: MissileTower, ExplosiveTower, or SplashTower.',
                'color': (255, 20, 147),
                'icon': 'SPLIT',
                'immunities': ['None'],
                'special_abilities': ['Splits on death', 'Spawns 2 enemies']
            },
            'EnergyShieldEnemy': {
                'name': 'Energy Shield Enemy',
                'description': 'Energy shield absorbs laser attacks but vulnerable to lightning.',
                'counters': 'Use LightningTower or kinetic weapons. AVOID LaserTower.',
                'color': (0, 255, 255),
                'icon': 'ESHLD',
                'immunities': ['Laser damage reduction'],
                'special_abilities': ['Energy absorption', 'Lightning vulnerability']
            },
            'GroundedEnemy': {
                'name': 'Grounded Enemy',
                'description': 'Electrical grounding provides immunity to lightning attacks.',
                'counters': 'Use any non-electric towers. AVOID LightningTower.',
                'color': (139, 69, 19),
                'icon': 'GND',
                'immunities': ['Lightning attacks'],
                'special_abilities': ['Electrical grounding', 'Lightning immunity']
            },
            'FireElementalEnemy': {
                'name': 'Fire Elemental',
                'description': 'HEALS from fire damage. FlameTowers make it stronger.',
                'counters': 'Use IceTower or FreezerTower. NEVER use FlameTower.',
                'color': (255, 69, 0),
                'icon': 'FIRE',
                'immunities': ['Fire damage (heals instead)'],
                'special_abilities': ['Fire healing', 'Flame immunity']
            },
            'PhaseShiftEnemy': {
                'name': 'Phase Shifter',
                'description': 'Phases through dimensions, avoiding many physical attacks.',
                'counters': 'Use FlameTower to disrupt phasing mechanism.',
                'color': (128, 0, 128),
                'icon': 'PHASE',
                'immunities': ['Physical attacks while phased'],
                'special_abilities': ['Dimensional phasing', 'Phase states']
            },
            'BlastProofEnemy': {
                'name': 'Blast-Proof Enemy',
                'description': 'Reinforced armor highly resistant to explosive damage.',
                'counters': 'Use LaserTower. AVOID Missile/Explosive Towers.',
                'color': (192, 192, 192),
                'icon': 'BLAST',
                'immunities': ['Explosive damage'],
                'special_abilities': ['Blast resistance', 'Reinforced armor']
            },
            'SpectralEnemy': {
                'name': 'Spectral Enemy',
                'description': 'Partially invisible, phases through attacks. Requires detection.',
                'counters': 'DetectorTower required, then ONLY LightningTower works.',
                'color': (230, 230, 250),
                'icon': 'GHOST',
                'immunities': ['Most physical attacks', 'Invisibility'],
                'special_abilities': ['Spectral form', 'Phase particles']
            },
            'CrystallineEnemy': {
                'name': 'Crystalline Enemy',
                'description': 'Living crystal reflects most attacks except focused light beams.',
                'counters': 'ONLY LaserTower can shatter crystal - all others ineffective.',
                'color': (224, 255, 255),
                'icon': 'CRYST',
                'immunities': ['All attacks except laser'],
                'special_abilities': ['Crystal structure', 'Attack reflection']
            },
            'ToxicEnemy': {
                'name': 'Toxic Enemy',
                'description': 'Poisonous creature completely immune to poison damage.',
                'counters': 'AVOID poison towers. Use basic, laser, or lightning towers.',
                'color': (76, 175, 80),
                'icon': 'TOX',
                'immunities': ['Poison damage'],
                'special_abilities': ['Poison immunity', 'Toxic aura']
            },
            'ToxicMutantEnemy': {
                'name': 'Toxic Mutant',
                'description': 'Evolved toxic creature immune to poison and most physical attacks.',
                'counters': 'ONLY FlameTower works - immune to poison and all other types.',
                'color': (127, 255, 0),
                'icon': 'TOXIC',
                'immunities': ['Poison', 'Most physical attacks'],
                'special_abilities': ['Advanced toxicity', 'Mutation resistance']
            },
            'VoidEnemy': {
                'name': 'Void Enemy',
                'description': 'Absorbs ALL attack types except explosives.',
                'counters': 'ONLY MissileTower and ExplosiveTower work - everything else feeds it.',
                'color': (25, 25, 112),
                'icon': 'VOID',
                'immunities': ['All non-explosive attacks'],
                'special_abilities': ['Void absorption', 'Energy consumption']
            },
            'AdaptiveEnemy': {
                'name': 'Adaptive Enemy',
                'description': 'Changes immunities every 3 seconds through 5 adaptation cycles.',
                'counters': 'ONLY SniperTower and IceTower ALWAYS work regardless of adaptation.',
                'color': (255, 105, 180),
                'icon': 'ADAPT',
                'immunities': ['Rotating immunities'],
                'special_abilities': ['Adaptation cycles', 'Immunity rotation']
            },
            'TimeLordBoss': {
                'name': 'TimeLord Boss',
                'description': 'Master of time manipulation. Slows projectiles, rewinds damage.',
                'counters': 'Focus fire during vulnerability windows - spread towers.',
                'color': (100, 149, 237),
                'icon': 'TIME',
                'immunities': ['Periodic invincibility phases'],
                'special_abilities': ['Time slow field', 'Damage rewind', 'Temporal rifts']
            },
            'NecromancerBoss': {
                'name': 'Necromancer Boss',
                'description': 'Dark sorcerer of death. Life drain, undead summoning, death auras.',
                'counters': 'Eliminate minions quickly, use non-poison damage.',
                'color': (148, 0, 211),
                'icon': 'NECRO',
                'immunities': ['Poison damage (complete immunity)'],
                'special_abilities': ['Life drain aura', 'Undead summoning', 'Death aura']
            },
            'ShadowKing': {
                'name': 'Shadow King',
                'description': 'Ruler of shadows. 70% dodge chance, shadow duplicates.',
                'counters': 'High rate of fire to overcome dodge - area effects for duplicates.',
                'color': (72, 61, 139),
                'icon': 'KING',
                'immunities': ['70% dodge chance when phased'],
                'special_abilities': ['Dimension phasing', 'Shadow duplicates', 'Void step']
            },
            'CrystalOverlord': {
                'name': 'Crystal Overlord',
                'description': 'Crystalline titan. 40% projectile reflection, crystal barriers.',
                'counters': 'Use non-laser high-damage weapons - beware of reflected projectiles.',
                'color': (0, 206, 209),
                'icon': 'CRYST',
                'immunities': ['Laser damage (complete immunity)'],
                'special_abilities': ['Projectile reflection', 'Crystal barriers', 'Shard rain']
            },
            'MegaBoss': {
                'name': 'Mega Boss',
                'description': 'Massive boss with multiple phases and damage reduction.',
                'counters': 'Focus all available firepower - prepare for extended engagement.',
                'color': (255, 0, 0),
                'icon': 'MEGA',
                'immunities': ['50% damage reduction'],
                'special_abilities': ['Multi-phase system', 'Minion spawning']
            },
            'SpeedBoss': {
                'name': 'Speed Boss',
                'description': 'Becomes faster as it takes damage. Lightning-fast dashes.',
                'counters': 'High burst damage to eliminate quickly before speed overwhelms.',
                'color': (255, 255, 0),
                'icon': 'SPEED',
                'immunities': ['None'],
                'special_abilities': ['Speed increases with damage', 'Dash ability']
            }
        }
        
        # Add dynamic tier and wave range information
        for enemy_name, enemy_data in base_info.items():
            # Add dynamic tier based on when enemy first appears
            enemy_data['tier'] = self._get_tier_from_wave_range(enemy_name)
            # Add actual wave range from configuration
            enemy_data['wave_range'] = self.enemy_wave_ranges.get(enemy_name, 'Unknown')
        
        return base_info
        
    def toggle_lookup(self):
        """Toggle the enemy lookup interface"""
        self.show_lookup = not self.show_lookup
        if not self.show_lookup:
            self.selected_enemy = None
            self.scroll_offset = 0
    
    def handle_event(self, event) -> bool:
        """Handle input events for the lookup interface"""
        if not self.show_lookup:
            return False
        
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                self.toggle_lookup()
                return True
        
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                # Check if clicking outside the panel
                mouse_x, mouse_y = event.pos
                if not (self.panel_x <= mouse_x <= self.panel_x + self.panel_width and
                        self.panel_y <= mouse_y <= self.panel_y + self.panel_height):
                    self.toggle_lookup()
                    return True
                
                # Check if clicking on scrollbar
                if (self.scrollbar_x <= mouse_x <= self.scrollbar_x + self.scrollbar_width and
                    self.list_start_y <= mouse_y <= self.list_start_y + self.list_height):
                    self._handle_scrollbar_click(mouse_y)
                    return True
                
                # Check if clicking on enemy list
                if (self.panel_x + 20 <= mouse_x <= self.scrollbar_x and
                    self.list_start_y <= mouse_y <= self.list_start_y + self.list_height):
                    
                    item_index = int((mouse_y - self.list_start_y + self.scroll_offset) // self.item_height)
                    
                    if 0 <= item_index < len(self.sorted_enemies):
                        self.selected_enemy = self.sorted_enemies[item_index][0]  # Get enemy key from tuple
                        return True
        
        elif event.type == pygame.MOUSEWHEEL:
            # Handle scrolling in enemy list
            mouse_x, mouse_y = pygame.mouse.get_pos()
            if (self.panel_x + 20 <= mouse_x <= self.panel_x + self.panel_width - 20 and
                self.list_start_y <= mouse_y <= self.list_start_y + self.list_height):
                
                self.scroll_offset = max(0, min(self.max_scroll, 
                                              self.scroll_offset - event.y * self.item_height))
                return True
        
        return False
    
    def _handle_scrollbar_click(self, mouse_y):
        """Handle clicking on the scrollbar"""
        relative_y = mouse_y - self.list_start_y
        scroll_ratio = relative_y / self.list_height
        self.scroll_offset = max(0, min(self.max_scroll, int(scroll_ratio * self.max_scroll)))
    
    def draw(self, screen: pygame.Surface):
        """Draw the enemy lookup interface"""
        if not self.show_lookup:
            return
        
        # Draw semi-transparent background
        overlay = pygame.Surface((self.screen_width, self.screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 150))
        screen.blit(overlay, (0, 0))
        
        # Draw main panel
        pygame.draw.rect(screen, self.bg_color, (self.panel_x, self.panel_y, self.panel_width, self.panel_height))
        pygame.draw.rect(screen, self.border_color, (self.panel_x, self.panel_y, self.panel_width, self.panel_height), 3)
        
        # Title
        title_text = self.title_font.render("Enemy Information Database", True, self.text_color)
        title_rect = title_text.get_rect(center=(self.panel_x + self.panel_width // 2, self.panel_y + 25))
        screen.blit(title_text, title_rect)
        
        # Enemy list with scrollbar
        self._draw_enemy_list(screen)
        
        # Enemy details
        if self.selected_enemy and self.selected_enemy in self.enemy_info:
            self._draw_enemy_details(screen)
        else:
            # Draw instruction text when no enemy is selected
            instruction_text = self.text_font.render("Click on an enemy above to view details", True, (150, 150, 150))
            instruction_rect = instruction_text.get_rect(center=(self.panel_x + self.panel_width // 2, 
                                                               self.detail_start_y + self.detail_height // 2))
            screen.blit(instruction_text, instruction_rect)
        
        # Instructions
        instructions = "Press ESC or click outside to close • Mouse wheel to scroll • Click enemy for details"
        instr_surface = self.small_font.render(instructions, True, (180, 180, 180))
        instr_rect = instr_surface.get_rect(center=(self.panel_x + self.panel_width // 2, 
                                                  self.panel_y + self.panel_height - 20))
        screen.blit(instr_surface, instr_rect)
    
    def _draw_enemy_list(self, screen: pygame.Surface):
        """Draw the scrollable enemy list"""
        # List background
        list_rect = pygame.Rect(self.panel_x + 20, self.list_start_y, 
                               self.panel_width - 60, self.list_height)  # Leave space for scrollbar
        pygame.draw.rect(screen, (40, 40, 50), list_rect)
        pygame.draw.rect(screen, self.border_color, list_rect, 2)
        
        # Calculate max scroll
        total_height = len(self.sorted_enemies) * self.item_height
        self.max_scroll = max(0, total_height - self.list_height)
        
        # Draw enemy list items
        visible_start = int(self.scroll_offset // self.item_height)
        visible_end = min(len(self.sorted_enemies), visible_start + int(self.list_height // self.item_height) + 2)
        
        for i in range(visible_start, visible_end):
            enemy_key, enemy_data = self.sorted_enemies[i]
            item_y = self.list_start_y + i * self.item_height - self.scroll_offset
            
            if item_y < self.list_start_y - self.item_height or item_y > self.list_start_y + self.list_height:
                continue
            
            # Highlight selected item
            if enemy_key == self.selected_enemy:
                highlight_rect = pygame.Rect(self.panel_x + 22, item_y, 
                                           self.panel_width - 64, self.item_height)
                pygame.draw.rect(screen, self.selected_color, highlight_rect)
            
            # Draw enemy icon
            icon_x = self.panel_x + 25
            icon_y = item_y + 3
            self._draw_enemy_icon(screen, enemy_key, icon_x, icon_y, enemy_data['color'])
            
            # Draw tier color indicator next to icon
            tier_color = self._get_tier_color(enemy_data['tier'])
            tier_rect = pygame.Rect(self.panel_x + 25 + self.icon_size + 5, item_y + 5, 
                                   20, self.item_height - 10)
            pygame.draw.rect(screen, tier_color, tier_rect)
            
            # Enemy name and tier
            name_text = f"{enemy_data['name']} ({enemy_data['tier']})"
            text_surface = self.text_font.render(name_text, True, enemy_data['color'])
            screen.blit(text_surface, (self.panel_x + 25 + self.icon_size + 30, item_y + 5))
            
            # Wave range (right aligned)
            wave_text = f"Waves {enemy_data['wave_range']}"
            wave_surface = self.small_font.render(wave_text, True, (180, 180, 180))
            wave_rect = wave_surface.get_rect(right=self.scrollbar_x - 10, centery=item_y + self.item_height // 2)
            screen.blit(wave_surface, wave_rect)
        
        # Draw scrollbar
        self._draw_scrollbar(screen)
    
    def _draw_scrollbar(self, screen: pygame.Surface):
        """Draw the scrollbar for the enemy list"""
        if self.max_scroll <= 0:
            return
        
        # Scrollbar background
        scrollbar_rect = pygame.Rect(self.scrollbar_x, self.list_start_y, 
                                   self.scrollbar_width, self.list_height)
        pygame.draw.rect(screen, self.scrollbar_color, scrollbar_rect)
        pygame.draw.rect(screen, self.border_color, scrollbar_rect, 1)
        
        # Scrollbar handle
        handle_height = max(20, int(self.list_height * (self.list_height / (self.list_height + self.max_scroll))))
        handle_y = self.list_start_y + int((self.scroll_offset / self.max_scroll) * (self.list_height - handle_height))
        
        handle_rect = pygame.Rect(self.scrollbar_x + 2, handle_y, 
                                self.scrollbar_width - 4, handle_height)
        pygame.draw.rect(screen, self.scrollbar_handle_color, handle_rect)
        pygame.draw.rect(screen, self.text_color, handle_rect, 1)
    
    def _get_tier_color(self, tier: str) -> tuple:
        """Get color for tier indicator"""
        tier_colors = {
            'Basic': (100, 255, 100),
            'Intermediate': (255, 255, 100),
            'Advanced': (255, 150, 100),
            'Elite': (255, 100, 255),
            'Legendary': (255, 100, 100),
            'Boss': (255, 0, 0),
            'Ultimate Boss': (150, 0, 255)
        }
        return tier_colors.get(tier, (150, 150, 150))
    
    def _draw_enemy_details(self, screen: pygame.Surface):
        """Draw detailed information for the selected enemy"""
        detail_rect = pygame.Rect(self.panel_x + 20, self.detail_start_y, 
                                self.panel_width - 40, self.detail_height)
        pygame.draw.rect(screen, (40, 40, 50), detail_rect)
        pygame.draw.rect(screen, self.border_color, detail_rect, 2)
        
        enemy_data = self.enemy_info[self.selected_enemy]
        y_offset = self.detail_start_y + 15
        
        # Enemy name and icon
        name_text = f"{enemy_data['name']} [{enemy_data['icon']}]"
        name_surface = self.header_font.render(name_text, True, enemy_data['color'])
        screen.blit(name_surface, (self.panel_x + 30, y_offset))
        y_offset += 35
        
        # Basic info
        info_lines = [
            f"Tier: {enemy_data['tier']}",
            f"Wave Range: {enemy_data['wave_range']}",
            "",
            "Description:",
            enemy_data['description'],
            "",
            "Counter Strategy:",
            enemy_data['counters'],
            "",
            f"Immunities: {', '.join(enemy_data['immunities'])}",
            f"Special Abilities: {', '.join(enemy_data['special_abilities'])}"
        ]
        
        for line in info_lines:
            if line == "":
                y_offset += 10
                continue
            
            if line.startswith(("Tier:", "Wave Range:", "Description:", "Counter Strategy:", "Immunities:", "Special Abilities:")):
                text_surface = self.text_font.render(line, True, self.highlight_color)
            else:
                # Wrap long lines for better readability
                wrapped_lines = self._wrap_text(line, self.small_font, self.panel_width - 80)
                for wrapped_line in wrapped_lines:
                    text_surface = self.small_font.render(wrapped_line, True, self.text_color)
                    screen.blit(text_surface, (self.panel_x + 30, y_offset))
                    y_offset += 18
                continue
            
            screen.blit(text_surface, (self.panel_x + 30, y_offset))
            y_offset += 25
    
    def _wrap_text(self, text: str, font: pygame.font.Font, max_width: int) -> List[str]:
        """Wrap text to fit within specified width"""
        words = text.split(' ')
        lines = []
        current_line = []
        
        for word in words:
            test_line = ' '.join(current_line + [word])
            if font.size(test_line)[0] <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                current_line = [word]
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return lines
    
    def is_lookup_active(self) -> bool:
        """Check if the lookup interface is currently active"""
        return self.show_lookup 