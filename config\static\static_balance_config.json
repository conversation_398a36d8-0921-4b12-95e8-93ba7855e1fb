{"description": "Static balance configuration - counter strategies, currency generation, and game mechanics that don't change", "version": "1.0", "currency": {"description": "Currency generation mechanics", "damage_divisor": 40, "utility_hit_reward": 1, "detector_reward_per_enemy": 2, "detector_reward_interval": 60, "firing_reward": 1}, "counter_system": {"description": "Tower vs Enemy damage multiplier system", "tower_enemy_multipliers": {"antiair": {"FlyingEnemy": 2.5, "InvisibleEnemy": 1.8}, "cannon": {"ArmoredEnemy": 2.0, "TankEnemy": 1.8, "ShieldedEnemy": 1.5}, "sniper": {"FastEnemy": 2.0, "InvisibleEnemy": 1.8, "TeleportingEnemy": 1.5, "AdaptiveEnemy": 2.0}, "poison": {"RegeneratingEnemy": 2.5, "ToxicMutantEnemy": 2.0, "BasicEnemy": 1.3}, "flame": {"ArmoredEnemy": 1.5, "ToxicMutantEnemy": 2.0, "PhaseShiftEnemy": 1.8}, "ice": {"FastEnemy": 2.0, "FireElementalEnemy": 3.0, "AdaptiveEnemy": 2.0, "FlyingEnemy": 1.5}, "laser": {"EnergyShieldEnemy": 0.5, "CrystallineEnemy": 3.0, "ArmoredEnemy": 1.8, "BlastProofEnemy": 2.0}, "lightning": {"GroundedEnemy": 0.3, "SpectralEnemy": 3.0, "EnergyShieldEnemy": 2.0, "wet_enemies": 2.0}, "missile": {"VoidEnemy": 2.5, "BlastProofEnemy": 0.5, "FlyingEnemy": 2.0, "SplittingEnemy": 1.8}, "explosive": {"VoidEnemy": 2.5, "BlastProofEnemy": 0.3, "SplittingEnemy": 2.0, "TeleportingEnemy": 1.5}, "splash": {"SplittingEnemy": 2.2, "TeleportingEnemy": 1.8, "RegeneratingEnemy": 1.5}, "freezer": {"FastEnemy": 2.5, "FireElementalEnemy": 2.0, "TeleportingEnemy": 1.8}, "destroyer": {"FlyingEnemy": 2.0, "ArmoredEnemy": 2.5, "TankEnemy": 2.2}}, "default_multiplier": 1.0, "max_multiplier": 3.0, "min_multiplier": 0.1}, "immunity": {"description": "Enemy immunity system mechanics", "base_chance_per_wave": 0.01, "max_immunity_chance": 0.15, "boss_wave_multiplier": 2.0, "mini_boss_multiplier": 1.5, "early_game_waves": 10, "early_game_max_immunities": 2}, "freeze": {"description": "Freeze and slow effect mechanics", "slow_factor": 0.25, "resistance_duration_multiplier": 0.5, "resistance_slow_factor": 0.6}, "terrain_effects": {"description": "How different terrain types affect gameplay", "water": {"freeze_damage_multiplier": 1.5, "lightning_damage_multiplier": 2.0, "makes_enemies_wet": true}, "forest": {"cover_bonus": 0.1, "fire_damage_multiplier": 1.3}, "sand": {"speed_multiplier": 1.2, "explosive_damage_multiplier": 0.8}}, "special_mechanics": {"description": "Special game mechanics and rules", "rock_removal": {"base_cost": 100, "wave_scaling_factor": 0.1, "max_cost": 1000}, "boss_scaling": {"health_multiplier": 10.0, "reward_multiplier": 5.0, "immunity_chance": 0.3}}, "enemy_buff_system": {"description": "Complete enemy buff system - stackable and combinable abilities", "available_buffs": {"invisibility": {"name": "Invisibility", "description": "Makes enemy invisible to most towers", "icon": "INVIS", "stackable": false, "max_stacks": 1, "duration": -1, "color": [128, 128, 128], "glow_radius": 15, "detection_range": 80, "transparency": 0.3}, "anti_explosive": {"name": "Blast Resistant", "description": "Provides resistance to explosive damage", "icon": "BLAST", "stackable": true, "max_stacks": 3, "duration": -1, "color": [64, 64, 64], "glow_radius": 0, "damage_reduction": 0.4, "explosive_towers": ["explosive", "cannon", "missile", "destroyer"]}, "flying": {"name": "Flying", "description": "Makes enemy airborne and harder to target", "icon": "FLY", "stackable": false, "max_stacks": 1, "duration": -1, "color": [255, 165, 0], "glow_radius": 20, "altitude": 10, "hover_speed": 0.2, "allowed_towers": ["antiair", "missile", "destroyer", "lightning"]}, "armor": {"name": "Armor Plating", "description": "Reduces all incoming damage", "icon": "ARMOR", "stackable": true, "max_stacks": 5, "duration": -1, "color": [169, 169, 169], "glow_radius": 0, "damage_reduction": 0.2}, "speed_boost": {"name": "Speed Boost", "description": "Increases movement speed significantly", "icon": "SPEED", "stackable": true, "max_stacks": 3, "duration": -1, "color": [255, 255, 0], "glow_radius": 12, "speed_multiplier": 1.4}, "regeneration": {"name": "Regeneration", "description": "Slowly heals over time", "icon": "REGEN", "stackable": true, "max_stacks": 4, "duration": -1, "color": [0, 255, 0], "glow_radius": 18, "heal_rate": 1, "heal_interval": 90}, "spell_resistance": {"name": "Spell Resistance", "description": "Reduces damage from magical towers", "icon": "MAGIC", "stackable": true, "max_stacks": 3, "duration": -1, "color": [128, 0, 128], "glow_radius": 0, "magical_towers": ["lightning", "laser", "ice", "flame", "freezer"], "damage_reduction": 0.35}, "fire_immunity": {"name": "Fire Immunity", "description": "Completely immune to fire damage", "icon": "FIRE", "stackable": false, "max_stacks": 1, "duration": -1, "color": [255, 69, 0], "glow_radius": 0, "immune_towers": ["flame"]}, "poison_immunity": {"name": "Poison Immunity", "description": "Immune to poison effects and damage", "icon": "POISON", "stackable": false, "max_stacks": 1, "duration": -1, "color": [50, 205, 50], "glow_radius": 0, "immune_towers": ["poison"]}, "freeze_immunity": {"name": "Freeze Immunity", "description": "Cannot be slowed or frozen", "icon": "FREEZE", "stackable": false, "max_stacks": 1, "duration": -1, "color": [173, 216, 230], "glow_radius": 0, "immune_towers": ["freezer", "ice"]}, "berserker": {"name": "Berserker Rage", "description": "Gets faster and stronger when damaged", "icon": "RAGE", "stackable": false, "max_stacks": 1, "duration": -1, "color": [255, 0, 0], "glow_radius": 25, "damage_threshold": 0.5, "speed_bonus": 2.0, "damage_bonus": 1.5}, "phase_shift": {"name": "Phase Shift", "description": "Occasionally phases through attacks", "icon": "PHASE", "stackable": false, "max_stacks": 1, "duration": -1, "color": [138, 43, 226], "glow_radius": 22, "dodge_chance": 0.25, "phase_cooldown": 120}}, "predefined_combinations": {"stealth_assassin": {"buffs": ["invisibility", "speed_boost"], "wave_requirements": 15}, "flying_fortress": {"buffs": ["flying", "armor", "anti_explosive"], "wave_requirements": 20}, "regenerating_tank": {"buffs": ["regeneration", "armor", "spell_resistance"], "wave_requirements": 25}, "berserker_speedster": {"buffs": ["berserker", "speed_boost", "fire_immunity"], "wave_requirements": 30}, "phase_wraith": {"buffs": ["phase_shift", "invisibility", "freeze_immunity"], "wave_requirements": 35}, "ultimate_defender": {"buffs": ["armor", "anti_explosive", "spell_resistance", "regeneration"], "wave_requirements": 40}, "chaos_demon": {"buffs": ["flying", "berserker", "phase_shift", "fire_immunity", "poison_immunity"], "wave_requirements": 50}}, "wave_progression": {"1-10": {"base_chance": 0.05, "max_buffs": 1, "allowed_buffs": ["speed_boost", "armor"]}, "11-20": {"base_chance": 0.15, "max_buffs": 2, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration"]}, "21-30": {"base_chance": 0.25, "max_buffs": 3, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive"]}, "31-40": {"base_chance": 0.35, "max_buffs": 4, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "fire_immunity"]}, "41-50": {"base_chance": 0.45, "max_buffs": 5, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "fire_immunity", "berserker", "phase_shift"]}, "51+": {"base_chance": 0.6, "max_buffs": 7, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "fire_immunity", "poison_immunity", "freeze_immunity", "berserker", "phase_shift"]}}, "boss_multipliers": {"mini_boss": 2.0, "boss": 3.0, "super_boss": 4.0}}}