import unittest
import sys
import os
import pygame

# Add parent directory to path to import game modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from towers import (
    <PERSON>Tower, <PERSON>niperTower, <PERSON>zerTower, DetectorTower, AntiAirTower,
    PoisonTower, LaserTower, CannonTower, LightningTower, FlameTower,
    IceTower, ExplosiveTower, MissileTower, SplashTower, DestroyerTower
)
from enemies import BasicEnemy, FlyingEnemy, InvisibleEnemy, FastEnemy


class TestStandardizedTargeting(unittest.TestCase):
    """Test the standardized targeting system for all tower types"""

    def setUp(self):
        """Set up test environment"""
        pygame.init()
        self.path = [(100, 100), (200, 100), (300, 100)]
        
        # Create test enemies
        self.ground_enemy = BasicEnemy(self.path)
        self.flying_enemy = FlyingEnemy(self.path)
        self.invisible_enemy = InvisibleEnemy(self.path)
        self.invisible_detected_enemy = InvisibleEnemy(self.path)
        self.invisible_detected_enemy.detected_by_detector = True

    def test_ground_only_towers_cannot_target_flying(self):
        """Test that ground-only towers cannot target flying enemies"""
        ground_only_towers = [
            BasicTower(150, 150),
            SniperTower(150, 150),
            FreezerTower(150, 150),
            CannonTower(150, 150),
            FlameTower(150, 150),
            PoisonTower(150, 150)
        ]
        
        for tower in ground_only_towers:
            with self.subTest(tower=tower.__class__.__name__):
                # Should target ground enemies
                self.assertTrue(tower.can_target_enemy(self.ground_enemy),
                              f"{tower.__class__.__name__} should target ground enemies")
                
                # Should NOT target flying enemies
                self.assertFalse(tower.can_target_enemy(self.flying_enemy),
                               f"{tower.__class__.__name__} should NOT target flying enemies")

    def test_anti_air_capable_towers_can_target_flying(self):
        """Test that anti-air capable towers can target flying enemies"""
        anti_air_towers = [
            AntiAirTower(150, 150),
            DestroyerTower(150, 150),
            LaserTower(150, 150),
            MissileTower(150, 150),
            ExplosiveTower(150, 150),
            LightningTower(150, 150),
            IceTower(150, 150),
            SplashTower(150, 150)
        ]
        
        for tower in anti_air_towers:
            with self.subTest(tower=tower.__class__.__name__):
                # Should target ground enemies
                self.assertTrue(tower.can_target_enemy(self.ground_enemy),
                              f"{tower.__class__.__name__} should target ground enemies")
                
                # Should target flying enemies
                self.assertTrue(tower.can_target_enemy(self.flying_enemy),
                              f"{tower.__class__.__name__} should target flying enemies")

    def test_invisible_enemy_targeting(self):
        """Test that towers can only target invisible enemies when detected"""
        all_towers = [
            BasicTower(150, 150),
            SniperTower(150, 150),
            FreezerTower(150, 150),
            AntiAirTower(150, 150),
            PoisonTower(150, 150),
            LaserTower(150, 150),
            CannonTower(150, 150),
            LightningTower(150, 150),
            FlameTower(150, 150),
            IceTower(150, 150),
            ExplosiveTower(150, 150),
            MissileTower(150, 150),
            SplashTower(150, 150),
            DestroyerTower(150, 150)
        ]
        
        for tower in all_towers:
            with self.subTest(tower=tower.__class__.__name__):
                # Should NOT target undetected invisible enemies
                self.assertFalse(tower.can_target_enemy(self.invisible_enemy),
                               f"{tower.__class__.__name__} should NOT target undetected invisible enemies")
                
                # Should target detected invisible enemies
                self.assertTrue(tower.can_target_enemy(self.invisible_detected_enemy),
                              f"{tower.__class__.__name__} should target detected invisible enemies")

    def test_detector_tower_special_behavior(self):
        """Test that DetectorTower has special targeting behavior"""
        detector = DetectorTower(150, 150)
        
        # DetectorTower doesn't attack, so it should not target anything for combat
        self.assertFalse(detector.can_target_ground)
        self.assertFalse(detector.can_target_flying)
        self.assertTrue(detector.can_target_invisible)  # Can detect invisible

    def test_targeting_properties_set_correctly(self):
        """Test that all towers have the correct targeting properties set"""
        # Ground-only towers
        ground_only = [BasicTower(0, 0), SniperTower(0, 0), FreezerTower(0, 0), 
                      CannonTower(0, 0), FlameTower(0, 0), PoisonTower(0, 0)]
        
        for tower in ground_only:
            with self.subTest(tower=tower.__class__.__name__):
                self.assertFalse(tower.can_target_flying)
                self.assertFalse(tower.can_target_invisible)
                self.assertTrue(tower.can_target_ground)

        # Anti-air capable towers
        anti_air = [AntiAirTower(0, 0), DestroyerTower(0, 0), LaserTower(0, 0),
                   MissileTower(0, 0), ExplosiveTower(0, 0), LightningTower(0, 0),
                   IceTower(0, 0), SplashTower(0, 0)]
        
        for tower in anti_air:
            with self.subTest(tower=tower.__class__.__name__):
                self.assertTrue(tower.can_target_flying)
                self.assertFalse(tower.can_target_invisible)
                self.assertTrue(tower.can_target_ground)

        # Detector tower
        detector = DetectorTower(0, 0)
        self.assertFalse(detector.can_target_flying)
        self.assertTrue(detector.can_target_invisible)
        self.assertFalse(detector.can_target_ground)

    def test_acquire_target_uses_standardized_logic(self):
        """Test that acquire_target methods use the standardized can_target_enemy logic"""
        tower = BasicTower(150, 150)
        enemies = [self.ground_enemy, self.flying_enemy, self.invisible_enemy]
        
        # Should only target ground enemy
        tower.acquire_target(enemies)
        self.assertEqual(tower.target, self.ground_enemy)
        
        # Test with anti-air tower
        anti_air = AntiAirTower(150, 150)
        anti_air.acquire_target(enemies)
        # Should target either ground or flying enemy (not invisible)
        self.assertIn(anti_air.target, [self.ground_enemy, self.flying_enemy])


if __name__ == '__main__':
    unittest.main()
