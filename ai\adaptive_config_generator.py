#!/usr/bin/env python3
"""
Adaptive Configuration Generator using OpenAI API

This system generates tower defense configurations based on player performance
in the previous level, using AI to intelligently adjust difficulty and mechanics.
"""

import json
import os
from typing import Dict, Any, Optional, List
from openai import AzureOpenAI
from datetime import datetime

# Import from our modular components
from .performance_analysis import (PerformanceData, load_all_recent_performances, load_performance_from_file,
                                   get_manual_performance_input, list_performance_files, analyze_buff_performance_trends,
                                   get_buff_difficulty_recommendations, generate_buff_config_from_performance)
from .adjustment_strategies import create_fallback_adjustments, create_multi_game_fallback_adjustments, summarize_config
from .config_naming import get_unique_creative_filename, generate_fallback_level_name
from .difficulty_factors import get_difficulty_70_reference, understand_difficulty_components, calculate_target_difficulty_adjustment
from .intelligent_enemy_buff_selector import IntelligentEnemyBuffSelector, SelectionStrategy


class AdaptiveConfigGenerator:
    """AI-powered adaptive configuration generator"""

    def __init__(self, api_key: Optional[str] = None, use_full_ai: bool = False, use_modular_ai: bool = False):
        """Initialize with Azure OpenAI API key and AI generation options"""
        # First try environment variables (more secure)
        self.api_key = api_key or os.getenv(
            'AZURE_OPENAI_API_KEY') or os.getenv('OPENAI_API_KEY')

        # Use provided API key
        if not self.api_key:
            self.api_key = "dcd901140ba54ea6aa738ec0e069b9f4"  # Your API key
            print("🔑 Using provided API key (AI generation enabled)")

        # Track if AI is available
        self.ai_available = False
        self.client = None
        self.use_full_ai = use_full_ai
        self.use_modular_ai = use_modular_ai
        self.full_ai_generator = None
        self.modular_ai_generator = None

        if self.api_key:
            try:
                # Azure OpenAI configuration (standardized)
                self.azure_endpoint = "https://matrixnewopenai.openai.azure.com"
                self.api_version = "2025-01-01-preview"
                self.deployment_name = "gpt-4o"  # Using gpt-4o as specified

                self.client = AzureOpenAI(
                    api_key=self.api_key,
                    azure_endpoint=self.azure_endpoint,
                    api_version=self.api_version
                )
                self.ai_available = True
                print("🤖✅ ADAPTIVE AI GENERATION ENABLED")
                print(f"   API Key: ...{self.api_key[-8:]}")
                print(f"   Endpoint: matrixnewopenai.openai.azure.com")

                # Initialize AI generators if requested
                if use_full_ai:
                    try:
                        from .full_ai_generator import FullAIGenerator
                        self.full_ai_generator = FullAIGenerator(self.api_key)
                        print(
                            "🤖🎨 FULL AI MODE ENABLED - AI will design complete maps from scratch!")
                        print(
                            "   This includes: terrain layout, enemy strategies, wave composition, and map design")
                    except ImportError as e:
                        print(f"⚠️ Full AI generator not available: {e}")
                        print(
                            "🔄 Using standard AI analysis with procedural generation")

                if use_modular_ai:
                    try:
                        from .modular_ai_generator import ModularAIGenerator
                        self.modular_ai_generator = ModularAIGenerator(
                            self.api_key)
                        print(
                            "🧩🎨 MODULAR AI MODE ENABLED - AI designs specific components!")
                        print(
                            "   This includes: AI terrain + AI enemy strategy + procedural framework")
                    except ImportError as e:
                        print(f"⚠️ Modular AI generator not available: {e}")
                        print(
                            "🔄 Using standard AI analysis with procedural generation")

            except Exception as e:
                print(f"🤖❌ AI initialization failed: {e}")
                print("🔄 Falling back to rule-based analysis and procedural generation")
                self.ai_available = False
        else:
            print(
                "🔄 No API key available, using rule-based analysis and procedural generation")

        # Load base config generator for fallback
        try:
            from .make_specific_config import ConfigGenerator
        except ImportError:
            # Fallback for when running as standalone script
            import sys
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from make_specific_config import ConfigGenerator
        self.base_generator = ConfigGenerator()

    def _calculate_base_waves_from_difficulty(self, difficulty: int) -> int:
        """
        Calculate base wave count using smooth mathematical progression

        Target progression:
        - Difficulty 1: ~8 waves
        - Difficulty 50: ~50 waves
        - Difficulty 100: ~100 waves

        Uses a combination of linear and logarithmic scaling for smooth progression
        """
        # Clamp difficulty to valid range
        difficulty = max(1, min(100, difficulty))

        # Use a smooth curve that starts low and scales up
        # Adjusted formula: base_waves = 5 + (difficulty * 0.8) + (difficulty^1.15 * 0.03)
        # This gives us:
        # - Difficulty 1: ~6 waves
        # - Difficulty 10: ~14 waves
        # - Difficulty 25: ~27 waves
        # - Difficulty 50: ~47 waves
        # - Difficulty 75: ~70 waves
        # - Difficulty 100: ~93 waves

        linear_component = difficulty * 0.8
        exponential_component = (difficulty ** 1.15) * 0.03
        base_waves = 5 + linear_component + exponential_component

        # Round to nearest integer and ensure minimum
        base_waves = max(5, int(round(base_waves)))

        return base_waves

    def _analyze_tower_usage(self, performance: PerformanceData) -> str:
        """Analyze player's tower usage patterns to identify preferred strategies"""
        tower_usage = performance.towers_built
        total_towers = sum(tower_usage.values())

        if total_towers == 0:
            return "No towers built - player may have struggled with basic gameplay"

        # Calculate performance score to determine counter-strategy approach
        performance_score = performance.score

        # Calculate percentages and identify dominant strategies
        tower_percentages = {tower: (
            count / total_towers) * 100 for tower, count in tower_usage.items() if count > 0}

        # Sort by usage frequency
        sorted_towers = sorted(tower_percentages.items(),
                               key=lambda x: x[1], reverse=True)

        analysis_parts = []
        analysis_parts.append(f"Total towers built: {total_towers}")
        analysis_parts.append(
            f"Tower diversity: {len(sorted_towers)} different types")
        analysis_parts.append(
            f"Performance Score: {performance_score:.1f}/100")

        # Identify dominant tower types (>25% usage)
        dominant_towers = [(tower, pct)
                           for tower, pct in sorted_towers if pct >= 25]
        if dominant_towers:
            analysis_parts.append("\nDOMINANT TOWER STRATEGIES:")
            for tower, pct in dominant_towers:
                analysis_parts.append(
                    f"  • {tower}: {pct:.1f}% - HEAVY RELIANCE")

        # Identify moderate usage (10-25%)
        moderate_towers = [(tower, pct)
                           for tower, pct in sorted_towers if 10 <= pct < 25]
        if moderate_towers:
            analysis_parts.append("\nMODERATE USAGE:")
            for tower, pct in moderate_towers:
                analysis_parts.append(f"  • {tower}: {pct:.1f}%")

        # Identify strategy patterns
        strategy_analysis = []

        # Check for splash/area damage reliance
        splash_towers = sum(tower_usage.get(t, 0)
                            for t in ['cannon', 'splash', 'explosive', 'flame'])
        if splash_towers / total_towers > 0.4:
            strategy_analysis.append(
                "HEAVY SPLASH DAMAGE STRATEGY - Vulnerable to single strong enemies")

        # Check for single-target reliance
        single_towers = sum(tower_usage.get(t, 0)
                            for t in ['sniper', 'laser', 'basic'])
        if single_towers / total_towers > 0.5:
            strategy_analysis.append(
                "SINGLE-TARGET FOCUS - Vulnerable to swarms and fast enemies")

        # Check for defensive/slow reliance
        slow_towers = sum(tower_usage.get(t, 0)
                          for t in ['freezer', 'ice', 'poison'])
        if total_towers > 0 and slow_towers / total_towers > 0.3:
            strategy_analysis.append(
                "DEFENSIVE/SLOW STRATEGY - Vulnerable to immune and fast enemies")

        # Check for lack of anti-air
        air_towers = sum(tower_usage.get(t, 0)
                         for t in ['antiair', 'missile', 'lightning'])
        if total_towers > 5 and air_towers / total_towers < 0.1:
            strategy_analysis.append(
                "WEAK ANTI-AIR DEFENSES - Vulnerable to flying enemies")

        if strategy_analysis:
            analysis_parts.append("\nSTRATEGIC VULNERABILITIES:")
            for vulnerability in strategy_analysis:
                analysis_parts.append(f"  • {vulnerability}")

        # ★ PERFORMANCE-BASED COUNTER-STRATEGY RECOMMENDATIONS ★
        if performance_score < 40:
            # Struggling players - NO harsh counters, focus on encouragement
            analysis_parts.append("\n🎓 LEARNING PHASE - NO HARSH COUNTERS:")
            analysis_parts.append(
                "  • Player is still learning - provide supportive challenges")
            analysis_parts.append(
                "  • Focus on economic help and easier enemies")
            analysis_parts.append(
                "  • Avoid counter-enemies that make preferred towers useless")
        elif performance_score < 60:
            # Average players - Gentle educational counters
            analysis_parts.append("\n📚 EDUCATIONAL COUNTERS (Gentle):")
            counter_recommendations = []
            for tower, pct in dominant_towers:
                if tower in ['basic', 'cannon']:
                    counter_recommendations.append(
                        "Light Flying enemy presence (teach anti-air value)")
                elif tower in ['sniper', 'laser']:
                    counter_recommendations.append(
                        "Some Fast enemies (encourage positioning)")
                elif tower in ['freezer', 'ice']:
                    counter_recommendations.append(
                        "Minor Fire Elemental presence (teach diversity)")
                # Skip harsh counters for average players

            if counter_recommendations:
                for counter in set(counter_recommendations):
                    analysis_parts.append(f"  • {counter}")
        else:
            # Strong players - Full counter-strategies to force adaptation
            analysis_parts.append("\n🎯 ADVANCED COUNTERS (Force Adaptation):")
            counter_recommendations = []

            for tower, pct in dominant_towers:
                if tower in ['basic', 'cannon']:
                    counter_recommendations.append(
                        "Flying enemies (immune to ground splash)")
                elif tower in ['sniper', 'laser']:
                    counter_recommendations.append(
                        "Fast/Teleporting enemies (hard to target)")
                elif tower in ['freezer', 'ice']:
                    counter_recommendations.append(
                        "Fire Elemental enemies (heat immunity)")
                elif tower == 'lightning':
                    counter_recommendations.append(
                        "Grounded enemies (lightning immunity)")
                elif tower == 'poison':
                    counter_recommendations.append(
                        "Spectral/Phase enemies (poison resistance)")
                elif tower in ['splash', 'explosive']:
                    counter_recommendations.append(
                        "Splitting enemies (create more targets)")
                elif tower == 'flame':
                    counter_recommendations.append(
                        "Fire Elemental enemies (heals from fire damage)")

            if counter_recommendations:
                for counter in set(counter_recommendations):  # Remove duplicates
                    analysis_parts.append(f"  • {counter}")

        return "\n".join(analysis_parts)

    def _analyze_buff_performance(self, performance: PerformanceData) -> str:
        """Analyze player's performance against enemy buffs"""
        analysis_parts = []
        analysis_parts.append("=== ENEMY BUFF SYSTEM PERFORMANCE ===")

        # Basic buff encounter data
        if hasattr(performance, 'buff_encounters') and performance.buff_encounters:
            total_buffed = sum(performance.buff_encounters.values())
            analysis_parts.append(
                f"Total buffed enemies encountered: {total_buffed}")
            analysis_parts.append(
                f"Buff types encountered: {len(performance.buff_encounters)}")

            # Most common buffs
            sorted_buffs = sorted(
                performance.buff_encounters.items(), key=lambda x: x[1], reverse=True)
            analysis_parts.append("Most encountered buffs:")
            for buff, count in sorted_buffs[:5]:
                analysis_parts.append(f"  • {buff}: {count} enemies")

        # Buff intensity
        if hasattr(performance, 'enemy_buff_intensity'):
            analysis_parts.append(
                f"Buff intensity level: {performance.enemy_buff_intensity}")

        # Challenge assessment
        if hasattr(performance, 'most_challenging_buffs') and performance.most_challenging_buffs:
            analysis_parts.append("Most challenging buffs:")
            for buff in performance.most_challenging_buffs:
                analysis_parts.append(f"  • {buff}")

        # Adaptation score
        if hasattr(performance, 'buff_adaptation_score') and performance.buff_adaptation_score > 0:
            analysis_parts.append(
                f"Player adaptation score: {performance.buff_adaptation_score:.1f}/100")
            if performance.buff_adaptation_score < 40:
                analysis_parts.append(
                    "  → Player struggled with buff adaptation")
            elif performance.buff_adaptation_score < 70:
                analysis_parts.append("  → Player showed moderate adaptation")
            else:
                analysis_parts.append(
                    "  → Player adapted well to buffed enemies")

        # Buff-related deaths
        if hasattr(performance, 'buff_related_deaths') and performance.buff_related_deaths > 0:
            analysis_parts.append(
                f"Lives lost to buffed enemies: {performance.buff_related_deaths}")

        # Counter strategy effectiveness
        if hasattr(performance, 'tower_counter_effectiveness') and performance.tower_counter_effectiveness:
            analysis_parts.append("Tower counter-effectiveness:")
            for tower, effectiveness in performance.tower_counter_effectiveness.items():
                analysis_parts.append(
                    f"  • {tower}: {effectiveness:.1f}% effective")

        # Successful strategies
        if hasattr(performance, 'effective_counter_strategies') and performance.effective_counter_strategies:
            analysis_parts.append("Effective counter strategies used:")
            for strategy in performance.effective_counter_strategies:
                analysis_parts.append(f"  • {strategy}")

        return "\n".join(analysis_parts)

    def _apply_buff_system_config(self, config: Dict[str, Any],
                                  adjustments: Dict[str, Any],
                                  performance: PerformanceData) -> Dict[str, Any]:
        """Apply buff system configuration using intelligent AI selection based on difficulty and performance"""

        # Get buff system recommendations from AI if available
        buff_adjustments = adjustments.get('buff_system_adjustments', {})

        # If buff system is disabled, don't add config
        if not buff_adjustments.get('enabled', True):
            return config

        # Extract difficulty and wave information from config
        difficulty_score = 50  # Default
        max_wave = 50  # Default

        # Get difficulty from AI adjustments if available
        if 'difficulty_adjustment' in adjustments and 'new_difficulty' in adjustments['difficulty_adjustment']:
            difficulty_score = adjustments['difficulty_adjustment']['new_difficulty']
        elif '_generation_metadata' in config and 'difficulty' in config['_generation_metadata']:
            difficulty_score = config['_generation_metadata']['difficulty']

        # Get max wave from wave config
        if 'wave_config' in config and 'wave_ranges' in config['wave_config']:
            wave_ranges = config['wave_config']['wave_ranges']
            if wave_ranges:
                max_wave = max(end for start, end in wave_ranges.keys())

        # Load all recent performances for trend analysis
        try:
            recent_performances = load_all_recent_performances()
            if not recent_performances:
                recent_performances = [performance]
        except:
            recent_performances = [performance]

        # Calculate average performance data
        performance_data = None
        if len(recent_performances) > 1:
            avg_score = sum(p.score for p in recent_performances) / \
                len(recent_performances)
            win_rate = sum(
                1 for p in recent_performances if p.win_flag) / len(recent_performances) * 100

            # Determine trend
            if len(recent_performances) >= 3:
                recent_scores = [p.score for p in recent_performances[:2]]
                older_scores = [p.score for p in recent_performances[2:]]
                recent_avg = sum(recent_scores) / len(recent_scores)
                older_avg = sum(older_scores) / len(older_scores)

                if recent_avg > older_avg + 10:
                    trend = 'improving'
                elif recent_avg < older_avg - 10:
                    trend = 'declining'
                else:
                    trend = 'stable'
            else:
                trend = 'stable'

            performance_data = {
                'average_score': avg_score,
                'win_rate': win_rate,
                'trend': trend
            }

        # Initialize intelligent enemy/buff selector
        selector = IntelligentEnemyBuffSelector()

        # Generate intelligent enemy and buff configuration
        ai_config = selector.generate_complete_enemy_buff_config(
            difficulty_score=difficulty_score,
            max_wave=max_wave,
            performance_data=performance_data
        )

        # Apply the AI-generated enemy and buff configuration
        if 'wave_compositions' in ai_config:
            # Update wave compositions with AI-selected enemies
            if 'wave_config' in config:
                config['wave_config']['wave_compositions'] = ai_config['wave_compositions']

        if 'enemy_buff_config' in ai_config:
            # Apply AI-selected buff configuration
            buff_config = ai_config['enemy_buff_config']

            # Apply AI-specific adjustments if available from LLM analysis
            if buff_adjustments:
                intensity_rec = buff_adjustments.get(
                    'intensity_recommendation', 'medium')
                focus_buffs = buff_adjustments.get('focus_buffs', [])
                avoid_buffs = buff_adjustments.get('avoid_buffs', [])

                # Override intensity if AI provided specific recommendation
                if intensity_rec in ['low', 'medium', 'high', 'extreme']:
                    buff_config['buff_intensity'] = intensity_rec

                # Merge LLM recommendations with intelligent selector results
                if 'custom_spawn_rates' in buff_config and 'wave_ranges' in buff_config['custom_spawn_rates']:
                    for wave_range, wave_config in buff_config['custom_spawn_rates']['wave_ranges'].items():
                        # Remove avoided buffs from AI-selected buffs
                        if avoid_buffs and 'allowed_buffs' in wave_config:
                            wave_config['allowed_buffs'] = [
                                b for b in wave_config['allowed_buffs'] if b not in avoid_buffs]

                        # Add focus buffs to AI-selected buffs if appropriate
                        if focus_buffs and 'allowed_buffs' in wave_config:
                            for focus_buff in focus_buffs:
                                if focus_buff not in wave_config['allowed_buffs']:
                                    # Check if buff is appropriate for this wave range
                                    available_buffs = selector.buff_database
                                    if (focus_buff in available_buffs and
                                            available_buffs[focus_buff].wave_introduction <= int(wave_range.split('-')[1].replace('+', ''))):
                                        wave_config['allowed_buffs'].append(
                                            focus_buff)

                # Add combined AI reasoning to buff config
                buff_config['ai_adjustments'] = {
                    'llm_intensity_reasoning': buff_adjustments.get('explanation', ''),
                    'llm_focus_buffs': focus_buffs,
                    'llm_avoided_buffs': avoid_buffs,
                    'llm_recommendation': intensity_rec,
                    'intelligent_selector_metadata': ai_config.get('ai_selection_metadata', {}),
                    'generation_method': 'combined_llm_and_intelligent_selector'
                }
            else:
                # Pure intelligent selector
                buff_config['ai_adjustments'] = {
                    'intelligent_selector_metadata': ai_config.get('ai_selection_metadata', {}),
                    'generation_method': 'pure_intelligent_selector'
                }

            # Add the finalized buff configuration
            config['enemy_buff_config'] = buff_config

        # Add metadata about AI selection
        if '_adaptive_metadata' not in config:
            config['_adaptive_metadata'] = {}

        config['_adaptive_metadata']['intelligent_selection'] = ai_config.get(
            'ai_selection_metadata', {})

        print(f"🧠 INTELLIGENT AI SELECTION APPLIED:")
        if 'ai_selection_metadata' in ai_config:
            metadata = ai_config['ai_selection_metadata']
            print(f"   Strategy: {metadata.get('strategy_used', 'unknown')}")
            print(
                f"   Enemy Usage: {metadata.get('enemy_usage_ratio', 0):.1%} ({len(ai_config.get('enemy_buff_config', {}).get('ai_selected_enemies', []))} types)")
            print(
                f"   Buff Usage: {metadata.get('buff_usage_ratio', 0):.1%} ({len(ai_config.get('enemy_buff_config', {}).get('ai_selected_buffs', []))} types)")

        return config

    def analyze_performance_with_ai(self, performance: PerformanceData) -> Dict[str, Any]:
        """Use OpenAI to analyze performance and suggest adjustments"""

        # Check if AI is available
        if not self.ai_available or not self.client:
            print("🔧 AI not available, using rule-based adjustments")
            return create_fallback_adjustments(performance)

        performance_summary = f"Score: {performance.score}/100, Win: {performance.win_flag}, Lives: {performance.lives_remaining}/{performance.starting_lives}, Towers: {performance.tower_diversity} types"

        # Analyze player's tower usage patterns
        tower_analysis = self._analyze_tower_usage(performance)

        # Analyze buff system performance if available
        buff_analysis = ""
        if (hasattr(performance, 'buff_encounters') and performance.buff_encounters) or \
           (hasattr(performance, 'enemy_buff_intensity') and performance.enemy_buff_intensity != 'none'):
            buff_analysis = self._analyze_buff_performance(performance)

        # Extract current difficulty and config details from performance data (now stored directly)
        current_difficulty = 50  # Default
        previous_config_summary = "No previous configuration details available"

        # Check if performance data includes extracted config details
        if hasattr(performance, 'previous_config_details') and performance.previous_config_details:
            config_details = performance.previous_config_details
            current_difficulty = config_details.get('difficulty', 50)

            # Create detailed summary of previous level configuration
            wave_systems = config_details.get('wave_systems', {})
            progression_systems = config_details.get('progression_systems', {})

            previous_config_summary = f"""
PREVIOUS LEVEL CONFIGURATION ANALYSIS:
• Difficulty Level: {current_difficulty}/100
• Level Name: {config_details.get('level_name', 'Unknown')}
• Total Waves: {wave_systems.get('total_waves', 80)}
• Starting Money: ${progression_systems.get('starting_money', 500)}
• Starting Lives: {progression_systems.get('starting_lives', 20)}
• Enemy Types Used: {', '.join(config_details.get('enemy_types_used', []))}
• Map Size: {config_details.get('map_dimensions', {}).get('width', 20)}x{config_details.get('map_dimensions', {}).get('height', 15)}
• Special Mechanics: {config_details.get('special_mechanics', [])}
• Tower Adjustments: {'Applied' if config_details.get('tower_adjustments') else 'None'}
• Terrain Features: {'Applied' if config_details.get('terrain_config') else 'Standard'}"""
        elif performance.previous_config:
            # Fallback to old method if new structure not available
            config = performance.previous_config

            # Method 1: From generation metadata (preferred)
            if '_generation_metadata' in config and 'difficulty' in config['_generation_metadata']:
                current_difficulty = config['_generation_metadata']['difficulty']
            # Method 2: From adaptive metadata (AI-generated configs)
            elif '_adaptive_metadata' in config:
                ai_adj = config['_adaptive_metadata'].get('ai_adjustments', {})
                if 'difficulty_adjustment' in ai_adj and 'new_difficulty' in ai_adj['difficulty_adjustment']:
                    current_difficulty = ai_adj['difficulty_adjustment']['new_difficulty']
            # Method 3: Guess from economic indicators (test configs, etc.)
            else:
                starting_money = config.get(
                    'game_config', {}).get('starting_money', 500)
                starting_lives = config.get(
                    'game_config', {}).get('starting_lives', 20)

                # For test configs like the one with 100,000 money and 1,000 lives
                if starting_money >= 50000 or starting_lives >= 500:
                    current_difficulty = 1  # Test config - very easy
                elif starting_money <= 300:
                    current_difficulty = 80  # Very hard
                elif starting_money <= 400:
                    current_difficulty = 60  # Hard
                elif starting_money >= 600:
                    current_difficulty = 30  # Easy
                else:
                    current_difficulty = 50  # Medium

            previous_config_summary = summarize_config(
                performance.previous_config)

        # Get comprehensive difficulty knowledge
        difficulty_70_reference = get_difficulty_70_reference()
        difficulty_components = understand_difficulty_components()

        # Add buff analysis to the prompt if available
        buff_section = ""
        if buff_analysis:
            buff_section = f"""

ENEMY BUFF SYSTEM PERFORMANCE:
{buff_analysis}

BUFF SYSTEM RECOMMENDATIONS:
- Consider the player's adaptation to buffed enemies
- Adjust buff intensity based on player performance
- Use buff data to inform enemy composition strategy"""

        prompt = f"""
You are an expert tower defense game designer. Analyze this player's performance and suggest specific adjustments for the next level configuration.

COMPREHENSIVE GAME KNOWLEDGE - DIFFICULTY 70 REFERENCE:
This is what difficulty 70 means in our tower defense game:
{json.dumps(difficulty_70_reference, indent=2)}

DIFFICULTY COMPONENTS UNDERSTANDING:
{json.dumps(difficulty_components, indent=2)}

PLAYER PERFORMANCE:
{performance_summary}

CURRENT DIFFICULTY LEVEL: {current_difficulty}/100

🎯 CRITICAL: DIFFICULTY MUST BE ADJUSTED IN EVERY CONFIG
The difficulty value will be stored in the generated config file as:
- _generation_metadata.difficulty = your_new_difficulty_number
- _adaptive_metadata.ai_adjustments.difficulty_adjustment.new_difficulty = your_new_difficulty_number

IMPORTANT: Use difficulty 70 as your reference point! 
- If current difficulty is 70 and player struggled (score < 50): Make it easier than difficulty 70
- If current difficulty is 70 and player excelled (score > 80): Make it harder than difficulty 70
- For average performance (50-80): Target around difficulty 70 with minor adjustments

Make GRADUAL difficulty adjustments! Never jump more than 15 levels at once.
- Excellent performance (80%+): Increase by 10-15 levels
- Good performance (60-79%): Increase by 5-10 levels  
- Average performance (40-59%): Increase by 0-5 levels
- Poor performance (20-39%): Decrease by 5-10 levels
- Very poor performance (<20%): Decrease by 10-15 levels

EXAMPLE: Current difficulty {current_difficulty}, Score 85% → New difficulty should be {current_difficulty + 12} (increase by 12)

PLAYER TOWER USAGE ANALYSIS:
{tower_analysis}

PREVIOUS LEVEL CONFIGURATION:
{previous_config_summary}{buff_section}

PERFORMANCE-BASED COUNTER-STRATEGY GUIDE:

★ FOR STRUGGLING PLAYERS (Score < 40%):
- DO NOT apply harsh counter-enemies that make their towers useless
- Focus on economic help, easier enemies, fewer waves
- Goal: Build confidence and teach basics, not punish their strategy
- Use supportive challenges that encourage learning

★ FOR AVERAGE PLAYERS (Score 40-60%):
- Apply GENTLE educational counters to encourage diversity
- Light Flying enemies (teach anti-air value) vs basic/cannon reliance
- Some Fast enemies (encourage positioning) vs sniper/laser focus
- Minor Fire Elemental presence vs ice tower dependence
- Goal: Gentle nudging toward strategic growth

★ FOR STRONG PLAYERS (Score 60%+):
- Apply FULL counter-strategies to force adaptation and mastery
- Flying enemies (immune to ground splash) vs BASIC/CANNON towers
- Fast/Teleporting enemies (hard to target) vs SNIPER towers
- Fire Elemental enemies (heat immunity) vs FREEZER/ICE towers
- Grounded enemies (lightning immunity) vs LIGHTNING towers
- Spectral/Phase enemies (poison resistance) vs POISON towers
- Splitting enemies (create more targets) vs SPLASH towers
- Fire Elemental enemies (heals from fire damage) vs FLAME towers
- Armored enemies (high damage reduction) vs LASER towers

TERRAIN STRATEGY (SECONDARY):
- Use terrain to support enemy strategies, not replace them
- WATER: Enhances freeze effects (secondary to enemy counters)
- FOREST: Damage bonus areas (secondary to enemy counters)
- SAND: Speed up enemies (if player struggles with fast enemies)

Analyze the performance data and determine appropriate adjustments. ALWAYS include difficulty adjustment as a core part of your analysis. 

IMPORTANT ECONOMIC LOGIC:
- HIGHER difficulty = MORE starting money (to help players with harder challenges)
- LOWER difficulty = LESS starting money (since challenges are easier)
- This is counter-intuitive but correct: harder games need more resources to be fair

Provide recommendations in JSON format:

{{
    "level_name": "Creative level name reflecting the enemy counter-strategy",
    "difficulty_adjustment": {{"explanation": "REQUIRED: explain your difficulty change reasoning based on current {current_difficulty} and performance", "new_difficulty": 55}},
    "wave_adjustments": {{"total_waves_modifier": 0.2-1.25, "explanation": "your wave count reasoning based on performance"}},
    "economic_adjustments": {{"starting_money_modifier": 0.8-1.5, "enemy_rewards_modifier": 0.8-1.3}},
    "enemy_composition_strategy": {{"primary_enemy_types": ["list of enemy types to counter player's towers"], "strategy_explanation": "why these enemies counter player's preferred towers"}},
    "enemy_adjustments": {{"health_modifier": 0.7-1.4, "speed_modifier": 0.8-1.3, "count_modifier": 0.8-1.3}},
    "enemy_buffs": {{"max_health_modifier": 0.7-1.4, "max_speed_modifier": 0.8-1.3, "explanation": "modifiers applied to ALL enemy base stats based on performance analysis"}},
    "buff_system_adjustments": {{"enabled": true, "intensity_recommendation": "low/medium/high/extreme", "focus_buffs": ["list of buffs to emphasize"], "avoid_buffs": ["list of buffs to reduce"], "explanation": "buff system adjustments based on player performance and adaptation"}},
    "map_adjustments": {{"complexity_modifier": 0.8-1.2, "obstacle_density_modifier": 0.8-1.2, "terrain_strategy": "terrain to support enemy strategy"}},
    "tower_adjustments": {{"cost_modifier": 0.8-1.3, "damage_modifier": 0.9-1.2}},
    "rock_removal_adjustments": {{"base_cost": 100-800, "wave_scaling_factor": 0.05-0.15, "explanation": "rock removal should be a hefty strategic investment that scales dramatically with wave progression"}},
    "special_mechanics": ["mechanics to add/remove based on your analysis"],
    "reasoning": "your complete analysis of player tower patterns and enemy counter-strategies"
}}

LEVEL NAME GUIDELINES:
- Reflect enemy counter-strategy (Flying Swarm, Speed Demon Trial, Armored Assault, Phase Walker Challenge)
- Include strategic focus (Anti-Sniper Gauntlet, Freeze Breaker, Lightning Rod Challenge)
- Match difficulty and counter-strategy theme

WAVE COUNT CONSTRAINTS:
- Base wave count scales progressively: Difficulty 1 = ~6 waves, Difficulty 50 = ~48 waves, Difficulty 100 = ~91 waves
- Modifier range is 0.2-1.25 (resulting in 1-115 waves total)
- Consider player skill level, win rate, and performance patterns
- Balance challenge progression with player enjoyment
- Difficulty 1 should result in 5-7 waves maximum
- Difficulty 50 should result in 9-60 waves maximum
- Difficulty 100 should result in 18-113 waves maximum

PRIMARY STRATEGY: ENEMY COMPOSITION COUNTER-PLAY
1. Analyze which towers the player uses most frequently
2. Select enemy types that specifically counter those towers
3. Design wave compositions around these counter-enemies
4. Use terrain only as secondary support for the enemy strategy
5. Force players to diversify their tower strategies

Focus on creating enemy compositions that directly challenge the player's preferred tower strategies, making them adapt and try new approaches.
"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,  # Use Azure deployment name
                messages=[
                    {"role": "system", "content": "You are an expert game designer specializing in adaptive difficulty systems for tower defense games."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )

            ai_response = response.choices[0].message.content

            # Try to parse JSON from response
            try:
                # Find JSON in response (handle cases where AI adds extra text)
                if ai_response is not None:
                    start_idx = ai_response.find('{')
                    end_idx = ai_response.rfind('}') + 1
                    if start_idx != -1 and end_idx > start_idx:
                        json_str = ai_response[start_idx:end_idx]
                        return json.loads(json_str)
                    else:
                        raise ValueError("No JSON found in response")
                else:
                    raise ValueError("AI response is None")
            except (json.JSONDecodeError, ValueError) as e:
                print(f"Warning: Could not parse AI response as JSON: {e}")
                print(f"AI Response: {ai_response}")
                return create_fallback_adjustments(performance)

        except Exception as e:
            print(f"Warning: OpenAI API call failed: {e}")
            return create_fallback_adjustments(performance)

    def generate_adaptive_config(self, performance: PerformanceData,
                                 output_path: Optional[str] = None) -> Dict[str, Any]:
        """Generate new config based on player performance"""

        # Check if full AI generation is available and preferred
        if self.use_full_ai and self.full_ai_generator:
            return self.generate_with_full_ai(performance, output_path)

        if self.ai_available:
            print("🤖🔍 ANALYZING PLAYER PERFORMANCE WITH AI...")
        else:
            print("🔧🔍 Analyzing player performance with rule-based system...")
        print(f"Performance Score: {performance.score:.1f}/100")

        # Get AI recommendations
        adjustments = self.analyze_performance_with_ai(performance)

        if self.ai_available:
            print(f"🤖💡 AI RECOMMENDATION: {adjustments['reasoning']}")
        else:
            print(f"🔧💡 RULE-BASED RECOMMENDATION: {adjustments['reasoning']}")

        # Apply adjustments to generate new config
        new_difficulty = adjustments['difficulty_adjustment']['new_difficulty']

        # Round to integer for consistency
        new_difficulty = int(round(new_difficulty))

        # Calculate adaptive wave count based on difficulty using smooth progression
        # Progressive scaling: difficulty 1 = ~8 waves, difficulty 50 = ~50 waves, difficulty 100 = ~100 waves
        base_waves = self._calculate_base_waves_from_difficulty(new_difficulty)

        if 'wave_adjustments' in adjustments and 'total_waves_modifier' in adjustments['wave_adjustments']:
            wave_modifier = adjustments['wave_adjustments']['total_waves_modifier']
            # Clamp between 5-100 waves (lowered minimum for difficulty 1)
            adaptive_waves = max(5, min(100, int(base_waves * wave_modifier)))
        else:
            adaptive_waves = base_waves

        print(
            f"🎯 Adaptive Wave Count: {adaptive_waves} waves (base: {base_waves})")

        # Generate base config using CORRECTED tower defense principles
        base_config = self.base_generator.generate_config(
            difficulty=new_difficulty,
            width=20,  # Could make this adaptive too
            height=15,
            total_waves=adaptive_waves
        )

        # Apply AI-suggested modifications
        modified_config = self._apply_adjustments(
            base_config, adjustments, performance)

        # Apply buff system configuration based on performance
        modified_config = self._apply_buff_system_config(
            modified_config, adjustments, performance)

        # Add performance tracking metadata with clear AI indicators
        generation_type = '🤖 AI ANALYSIS + PROCEDURAL EXECUTION' if self.ai_available else '🔧 RULE-BASED ANALYSIS + PROCEDURAL EXECUTION'
        modified_config['_adaptive_metadata'] = {
            'previous_performance': {
                'score': performance.score,
                'win_flag': performance.win_flag,
                'lives_remaining_pct': (performance.lives_remaining / performance.starting_lives) * 100,
                'tower_diversity': len([t for t, c in performance.towers_built.items() if c > 0])
            },
            'ai_adjustments': adjustments,
            'generation_timestamp': datetime.now().isoformat(),
            'generation_method': 'ai' if self.ai_available else 'rule_based',
            'creation_type': generation_type,
            'ai_vs_procedural': {
                'analysis': 'AI' if self.ai_available else 'RULE-BASED',
                'map_generation': 'PROCEDURAL',
                'enemy_waves': 'PROCEDURAL',
                'terrain_layout': 'PROCEDURAL',
                'note': 'This config uses AI for performance analysis but procedural algorithms for actual generation'
            }
        }

        # Generate output path if not provided
        if output_path is None:
            # Auto-generate sequential filename in base folder
            config_dir = os.path.join(os.path.dirname(
                os.path.dirname(os.path.abspath(__file__))), 'config')
            base_config_dir = os.path.join(config_dir, 'base')
            os.makedirs(base_config_dir, exist_ok=True)

            # Use creative filename if level name was generated, otherwise use numbered approach
            level_name = modified_config.get('level_name')
            if level_name:
                output_name = get_unique_creative_filename(
                    level_name, base_config_dir)
            else:
                # Fallback to numbered approach for configs without level names
                counter = 1
                while True:
                    output_name = f"{counter}.json"
                    # Check main config dir for numbered files
                    test_path = os.path.join(config_dir, output_name)
                    if not os.path.exists(test_path):
                        break
                    counter += 1

            output_path = os.path.join(
                base_config_dir if level_name else config_dir, output_name)

        # ENFORCE MINIMUM STARTING MONEY (Fix for low starting cash issue)
        modified_config = self._enforce_minimum_starting_money(modified_config)

        # Save the config
        with open(output_path, 'w') as f:
            json.dump(modified_config, f, indent=2)
        method = "🤖 AI-GENERATED" if self.ai_available else "🔧 RULE-BASED"
        level_display = f" - {modified_config['level_name']}" if 'level_name' in modified_config else ""
        print(
            f"✅ {method} adaptive configuration saved to {output_path}{level_display}")

        # Add clear generation type indicator
        creation_type = modified_config.get(
            '_generation_metadata', {}).get('creation_type', 'Unknown')
        if creation_type != 'Unknown':
            print(f"   Generation Type: {creation_type}")

        return modified_config

    def _enforce_minimum_starting_money(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Enforce minimum starting money of 20 dollars across all config sections"""
        MINIMUM_MONEY = 20

        # Check and fix game_config
        if 'game_config' in config and 'starting_money' in config['game_config']:
            if config['game_config']['starting_money'] < MINIMUM_MONEY:
                old_money = config['game_config']['starting_money']
                config['game_config']['starting_money'] = MINIMUM_MONEY
                print(
                    f"⚠️ FIXED: Starting money increased from ${old_money} to ${MINIMUM_MONEY} (minimum enforced)")

        # Check and fix progression_config, and sync with game_config
        if 'progression_config' in config and 'starting_money' in config['progression_config']:
            if config['progression_config']['starting_money'] < MINIMUM_MONEY:
                old_money = config['progression_config']['starting_money']
                config['progression_config']['starting_money'] = MINIMUM_MONEY

                # SYNC: Also update game_config to match
                if 'game_config' not in config:
                    config['game_config'] = {}
                config['game_config']['starting_money'] = MINIMUM_MONEY

                print(
                    f"⚠️ FIXED: Starting money increased from ${old_money} to ${MINIMUM_MONEY} (minimum enforced, synced to both configs)")

        # Check and fix any other money references in the config
        if 'starting_money' in config:
            if config['starting_money'] < MINIMUM_MONEY:
                old_money = config['starting_money']
                config['starting_money'] = MINIMUM_MONEY
                print(
                    f"⚠️ FIXED: Root starting money increased from ${old_money} to ${MINIMUM_MONEY} (minimum enforced)")

        return config

    def generate_with_full_ai(self, performance: PerformanceData,
                              output_path: Optional[str] = None) -> Dict[str, Any]:
        """Generate complete config using full AI system"""

        print(
            "🤖🎨 USING FULL AI GENERATION - AI will design complete map and configuration!")
        print("🎯 This means AI will create: terrain layout, enemy waves, map design, and strategic placement")

        # ★ FIX: Check for multi-game context and use aggregated data ★
        if hasattr(performance, 'multi_game_context') and performance.multi_game_context:
            # Use multi-game aggregated data
            multi_context = performance.multi_game_context
            print(
                f"📊 Multi-Game Performance Score: {multi_context['avg_score']:.1f}/100 (across {multi_context['games_analyzed']} games)")
            print(
                f"   Latest Game: {performance.score:.1f}/100, Trend: {multi_context['trend']}")

            # Calculate aggregated tower diversity across all games
            all_tower_types = set()

            for game_summary in multi_context['performance_summaries']:
                for tower_type in game_summary['towers_built'].keys():
                    all_tower_types.add(tower_type)

            # Convert to format expected by full AI generator using AGGREGATED data
            performance_data = {
                # ★ Use average score, not latest ★
                'score': multi_context['avg_score'],
                'win_flag': multi_context['win_rate'] > 50,  # ★ Use win rate ★
                'lives_remaining': performance.lives_remaining,  # Keep latest for context
                'starting_lives': performance.starting_lives,
                # ★ Use total tower types across games ★
                'tower_diversity': len(all_tower_types),
                # ★ NEW: Pass extracted config details ★
                'previous_config_details': performance.previous_config_details,
                # Fallback
                'previous_difficulty': performance.previous_config.get('_generation_metadata', {}).get('difficulty', 50),
                'multi_game_context': multi_context  # ★ Pass full context to AI ★
            }
        else:
            # Single game analysis
            print(f"Performance Score: {performance.score:.1f}/100")

            # ★ FIXED: Include previous_config_details for Full AI ★
        performance_data = {
            'score': performance.score,
            'win_flag': performance.win_flag,
            'lives_remaining': performance.lives_remaining,
            'starting_lives': performance.starting_lives,
            'tower_diversity': len([t for t, c in performance.towers_built.items() if c > 0]),
            # ★ NEW: Pass extracted config details ★
            'previous_config_details': performance.previous_config_details,
            # Fallback
            'previous_difficulty': performance.previous_config.get('_generation_metadata', {}).get('difficulty', 50)
        }

        # Calculate adaptive wave count for full AI
        adjustments = self.analyze_performance_with_ai(
            performance)  # Get wave recommendations

        # Get difficulty from adjustments for appropriate base wave count
        difficulty = adjustments.get(
            'difficulty_adjustment', {}).get('new_difficulty', 50)
        base_waves = self._calculate_base_waves_from_difficulty(difficulty)

        if 'wave_adjustments' in adjustments and 'total_waves_modifier' in adjustments['wave_adjustments']:
            wave_modifier = adjustments['wave_adjustments']['total_waves_modifier']
            adaptive_waves = max(5, min(100, int(base_waves * wave_modifier)))
        else:
            adaptive_waves = base_waves

        print(f"🎯 Full AI Adaptive Wave Count: {adaptive_waves} waves")

        # Generate complete config with full AI
        if self.full_ai_generator is None:
            raise Exception("Full AI generator not initialized")

        full_config = self.full_ai_generator.generate_complete_config_with_ai(
            performance_data,
            width=20,
            height=15,
            total_waves=adaptive_waves,
            previous_config=performance.previous_config if performance.previous_config else {}
        )

        # Create default AI adjustments for Full AI configs using GRADUAL difficulty progression
        performance_score = performance.score

        # Get current difficulty from performance data (now stored directly)
        current_difficulty = 50  # Default
        if hasattr(performance, 'previous_config_details') and performance.previous_config_details:
            current_difficulty = performance.previous_config_details.get(
                'difficulty', 50)
        elif performance.previous_config and '_generation_metadata' in performance.previous_config:
            current_difficulty = performance.previous_config['_generation_metadata'].get(
                'difficulty', 50)

        # Calculate gradual difficulty change based on performance (same logic as adjustment_strategies.py)
        if performance_score >= 80:
            difficulty_change = 15  # Increase by 15 for excellent performance
            reasoning = f"Full AI increased difficulty by {difficulty_change} based on excellent performance ({current_difficulty} → {current_difficulty + difficulty_change})"
        elif performance_score >= 60:
            difficulty_change = 8   # Increase by 8 for good performance
            reasoning = f"Full AI moderately increased difficulty by {difficulty_change} based on good performance ({current_difficulty} → {current_difficulty + difficulty_change})"
        elif performance_score >= 40:
            difficulty_change = 2   # Small increase for average performance
            reasoning = f"Full AI slightly increased difficulty by {difficulty_change} based on average performance ({current_difficulty} → {current_difficulty + difficulty_change})"
        elif performance_score >= 20:
            difficulty_change = -5  # Decrease by 5 for poor performance
            reasoning = f"Full AI reduced difficulty by {abs(difficulty_change)} to provide better challenge balance ({current_difficulty} → {current_difficulty + difficulty_change})"
        else:
            difficulty_change = -15  # Decrease by 15 for very poor performance
            reasoning = f"Full AI significantly reduced difficulty by {abs(difficulty_change)} for struggling player ({current_difficulty} → {current_difficulty + difficulty_change})"

        # Apply gradual difficulty change (never jump more than 15 levels at once)
        new_difficulty = max(
            1, min(100, current_difficulty + difficulty_change))
        new_difficulty = int(round(new_difficulty))

        # Add adaptive metadata with clear AI indicators and required ai_adjustments field
        full_config['_adaptive_metadata'] = {
            'previous_performance': {
                'score': performance.score,
                'win_flag': performance.win_flag,
                'lives_remaining_pct': (performance.lives_remaining / performance.starting_lives) * 100,
                'tower_diversity': len([t for t, c in performance.towers_built.items() if c > 0])
            },
            'ai_adjustments': {
                'difficulty_adjustment': {
                    'new_difficulty': new_difficulty,
                    'change': new_difficulty - current_difficulty,
                    'reasoning': f"{reasoning} → Final: {new_difficulty}"
                },
                'reasoning': f"Full AI generated complete level configuration with strategic terrain layout and enemy wave composition designed to challenge player's performance patterns (Score: {performance_score:.1f}% on difficulty {current_difficulty})"
            },
            'generation_timestamp': datetime.now().isoformat(),
            'generation_method': 'full_ai',
            'adaptive_waves': adaptive_waves,
            'creation_type': '🤖 FULL AI GENERATION',
            'ai_vs_procedural': {
                'analysis': 'AI',
                'map_generation': 'AI',
                'enemy_waves': 'AI',
                'terrain_layout': 'AI',
                'note': 'This entire config was designed by AI from scratch!'
            }
        }

        # Generate output path if not provided
        if output_path is None:
            config_dir = os.path.join(os.path.dirname(
                os.path.dirname(os.path.abspath(__file__))), 'config')
            base_config_dir = os.path.join(config_dir, 'base')
            os.makedirs(base_config_dir, exist_ok=True)

            # Use creative filename based on AI-generated level name
            level_name = full_config.get('level_name', 'Full AI Challenge')
            output_name = get_unique_creative_filename(
                level_name, base_config_dir)
            output_path = os.path.join(base_config_dir, output_name)

        # ENFORCE MINIMUM STARTING MONEY (Fix for low starting cash issue)
        full_config = self._enforce_minimum_starting_money(full_config)

        # Save the config
        with open(output_path, 'w') as f:
            json.dump(full_config, f, indent=2)

        print(f"🤖✅ FULL AI CONFIGURATION SAVED to {output_path}")
        print(
            f"   Level Name: {full_config.get('level_name', 'Unnamed Level')}")
        generation_method = full_config.get(
            '_generation_metadata', {}).get('generation_method', 'unknown')
        creation_type = full_config.get(
            '_generation_metadata', {}).get('creation_type', 'Unknown')
        print(f"   Generation Method: {generation_method}")
        print(f"   Creation Type: {creation_type}")

        return full_config

    def calculate_weighted_difficulty_adjustment(self, performances: List[PerformanceData]) -> Dict[str, Any]:
        """Calculate difficulty adjustment using weighted analysis system with comprehensive difficulty knowledge"""
        if not performances:
            return {'direction': 'none', 'magnitude': 0, 'new_difficulty': 50, 'reasoning': 'No performance data'}

        latest_game = performances[0]

        # Get comprehensive difficulty knowledge
        difficulty_70_reference = get_difficulty_70_reference()
        difficulty_components = understand_difficulty_components()

        # 1. Direction: Latest game result determines increase/decrease
        direction = 'increase' if latest_game.win_flag else 'decrease'

        # 2. Extract difficulties and scores for weighted calculation
        weighted_scores = []
        total_weight = 0

        # Get current difficulty as baseline
        current_difficulty = 50  # Default
        if latest_game.previous_config:
            config = latest_game.previous_config
            # Multiple methods to extract difficulty
            if '_generation_metadata' in config and 'difficulty' in config['_generation_metadata']:
                current_difficulty = config['_generation_metadata']['difficulty']
            elif '_adaptive_metadata' in config:
                ai_adj = config['_adaptive_metadata'].get('ai_adjustments', {})
                if 'difficulty_adjustment' in ai_adj and 'new_difficulty' in ai_adj['difficulty_adjustment']:
                    current_difficulty = ai_adj['difficulty_adjustment']['new_difficulty']
            else:
                # Guess from economic indicators
                starting_money = config.get(
                    'game_config', {}).get('starting_money', 500)
                if starting_money <= 300:
                    current_difficulty = 80
                elif starting_money <= 400:
                    current_difficulty = 60
                elif starting_money >= 600:
                    current_difficulty = 30

        print(f"🎯 WEIGHTED DIFFICULTY ANALYSIS (with difficulty 70 reference):")
        print(
            f"   Latest game: {'WON' if latest_game.win_flag else 'LOST'} → Direction: {direction}")
        print(f"   Current difficulty: {current_difficulty}")
        print(
            f"   Difficulty 70 reference: {difficulty_70_reference['description']}")

        # 3. Calculate weighted performance scores
        for i, perf in enumerate(performances):
            # Extract game difficulty
            game_difficulty = current_difficulty  # Fallback to current
            if perf.previous_config:
                config = perf.previous_config
                if '_generation_metadata' in config and 'difficulty' in config['_generation_metadata']:
                    game_difficulty = config['_generation_metadata']['difficulty']
                elif '_adaptive_metadata' in config:
                    ai_adj = config['_adaptive_metadata'].get(
                        'ai_adjustments', {})
                    if 'difficulty_adjustment' in ai_adj and 'new_difficulty' in ai_adj['difficulty_adjustment']:
                        game_difficulty = ai_adj['difficulty_adjustment']['new_difficulty']

            # Calculate weight: (difficulty/50) × recency_multiplier
            # Normalize to base 1.0 at difficulty 50
            difficulty_weight = game_difficulty / 50.0
            # Recent games weigh more: 1.0, 0.5, 0.33, 0.25, 0.2
            recency_multiplier = 1.0 / (i + 1)

            weight = difficulty_weight * recency_multiplier
            weighted_scores.append(perf.score * weight)
            total_weight += weight

            print(
                f"   Game {i+1}: Score {perf.score:.1f}, Difficulty {game_difficulty}, Weight {weight:.2f}")

        # 4. Calculate weighted average performance
        if total_weight > 0:
            weighted_avg_score = sum(weighted_scores) / total_weight
        else:
            weighted_avg_score = sum(
                p.score for p in performances) / len(performances)

        # 5. Determine magnitude based on weighted performance
        if direction == 'increase':
            # Won latest game - increase based on how well they're doing overall
            if weighted_avg_score >= 80:
                magnitude = 12  # Excellent performance across harder games
            elif weighted_avg_score >= 65:
                magnitude = 8   # Good performance
            elif weighted_avg_score >= 50:
                magnitude = 5   # Average performance
            else:
                magnitude = 2   # Poor overall but latest win suggests readiness for small increase
        else:
            # Lost latest game - decrease based on overall struggle
            if weighted_avg_score >= 65:
                magnitude = -3  # Good overall, small decrease
            elif weighted_avg_score >= 45:
                magnitude = -6  # Average overall, moderate decrease
            elif weighted_avg_score >= 25:
                magnitude = -10  # Poor overall, significant decrease
            else:
                magnitude = -15  # Very poor, major decrease

        # 6. Calculate new difficulty with bounds
        new_difficulty = max(1, min(100, current_difficulty + magnitude))

        # 7. Generate reasoning with difficulty 70 reference
        difficulty_70_comparison = ""
        if current_difficulty == 70:
            if new_difficulty > 70:
                difficulty_70_comparison = f" (increasing beyond difficulty 70 reference: {difficulty_70_reference['description']})"
            elif new_difficulty < 70:
                difficulty_70_comparison = f" (decreasing below difficulty 70 reference: {difficulty_70_reference['description']})"
            else:
                difficulty_70_comparison = f" (maintaining difficulty 70 reference: {difficulty_70_reference['description']})"
        else:
            difficulty_70_comparison = f" (difficulty 70 reference: {difficulty_70_reference['description']})"

        reasoning = f"Weighted Analysis: Latest game {'WON' if latest_game.win_flag else 'LOST'} " + \
            f"(direction: {direction}). Weighted avg performance: {weighted_avg_score:.1f}% " + \
            f"across {len(performances)} games. Difficulty {current_difficulty} → {new_difficulty} " + \
            f"({magnitude:+d}). Harder games weighted more heavily in decision.{difficulty_70_comparison}"

        print(f"   Weighted avg score: {weighted_avg_score:.1f}%")
        print(f"   Magnitude: {magnitude:+d}")
        print(f"   New difficulty: {new_difficulty}")

        return {
            'direction': direction,
            'magnitude': magnitude,
            'current_difficulty': current_difficulty,
            'new_difficulty': new_difficulty,
            'weighted_avg_score': weighted_avg_score,
            'reasoning': reasoning
        }

    def generate_config_from_recent_games(self, output_path: Optional[str] = None) -> Dict[str, Any]:
        """Generate config based on up to 5 most recent games using FULL AI if available"""

        # Load all recent performance files
        performances = load_all_recent_performances()

        if not performances:
            print("❌ No performance data found. Play some games first!")
            return None

        print(f"\n📊 Analyzing {len(performances)} recent games...")

        # Calculate aggregate statistics
        scores = [p.score for p in performances]
        avg_score = sum(scores) / len(scores)
        win_rate = sum(1 for p in performances if p.win_flag) / \
            len(performances) * 100

        # Get performance trend (improving/declining)
        trend = "stable"
        if len(scores) >= 3:
            recent_avg = sum(scores[:2]) / 2  # Last 2 games
            older_avg = sum(scores[2:]) / len(scores[2:])  # Older games
            if recent_avg > older_avg + 10:
                trend = "improving"
            elif recent_avg < older_avg - 10:
                trend = "declining"

        print(f"   Average Score: {avg_score:.1f}/100")
        print(f"   Win Rate: {win_rate:.1f}%")
        print(f"   Trend: {trend}")

        # ★ ENHANCED: Extract comprehensive multi-game analysis ★
        difficulty_progression = self._extract_difficulty_progression(
            performances)
        strategic_patterns = self._analyze_strategic_patterns(performances)
        performance_trends = self._analyze_performance_trends(performances)

        # Use the most recent performance as the base, but with aggregate context
        latest_performance = performances[0]

        # ★ NEW: Try AI Generation Methods ★

        # Priority 1: Modular AI (most reliable)
        if self.use_modular_ai and self.modular_ai_generator and self.ai_available:
            print("🧩🎨 ATTEMPTING MODULAR AI GENERATION from multi-game analysis...")
            try:
                # ★ FIXED: Include previous_config_details for AI to use ★
                performance_data = {
                    'score': avg_score,
                    # ★ FIX: Use latest game result for direction, not aggregate win rate
                    'win_flag': latest_performance.win_flag,
                    'lives_remaining': latest_performance.lives_remaining,
                    'starting_lives': latest_performance.starting_lives,
                    'tower_diversity': len(set(tower for p in performances for tower in p.towers_built.keys())),
                    # ★ FIX: Ensure not None
                    'previous_config_details': latest_performance.previous_config_details if latest_performance.previous_config_details else {},
                    # ★ FIX: Handle None config
                    'previous_difficulty': latest_performance.previous_config.get('_generation_metadata', {}).get('difficulty', 50) if latest_performance.previous_config else 50,
                    # ★ FIX: Add to top level for modular AI
                    'config_difficulty_score': getattr(latest_performance, 'config_difficulty_score', None),
                    # ★ FIX: Add config file path for reference detection
                    'config_file_path': getattr(latest_performance, 'config_file_path', ''),
                    'multi_game_context': {
                        'games_analyzed': len(performances),
                        'avg_score': avg_score,
                        'win_rate': win_rate,
                        'trend': trend,
                        'difficulty_progression': difficulty_progression,
                        'strategic_patterns': strategic_patterns,
                        'performance_trends': performance_trends,
                        'performance_summaries': [
                            {
                                'score': p.score,
                                'win_flag': p.win_flag,
                                'lives_remaining': p.lives_remaining,
                                'starting_lives': p.starting_lives,
                                'towers_built': p.towers_built if p.towers_built else {},
                                'tower_diversity': p.tower_diversity,
                                'wave_reached': p.wave_reached,
                                'final_wave': p.final_wave,
                                'economic_efficiency': getattr(p, 'economic_efficiency', 0.0),
                                'resource_management_score': getattr(p, 'resource_management_score', 0.0),
                                'most_built_tower_type': getattr(p, 'most_built_tower_type', None),
                                'config_difficulty_score': getattr(p, 'config_difficulty_score', None),
                                'previous_config_details': p.previous_config_details if p.previous_config_details else {}
                            } for p in performances
                        ]
                    }
                }

                # Generate with modular AI
                modular_config = self.modular_ai_generator.generate_modular_config(
                    performance_data)

                # ★ FIX: Check if modular_config is None ★
                if modular_config is None:
                    print("🧩❌ Modular AI returned None config")
                    raise Exception("Modular AI returned None config")

                # Preserve existing adaptive metadata and add multi-game context
                if '_adaptive_metadata' not in modular_config:
                    modular_config['_adaptive_metadata'] = {}

                # Add multi-game context while preserving existing ai_adjustments
                modular_config['_adaptive_metadata']['multi_game_context'] = performance_data['multi_game_context']
                modular_config['_adaptive_metadata']['generation_method'] = 'modular_ai_multi_game'

                # Don't overwrite generation_timestamp if it exists
                if 'generation_timestamp' not in modular_config['_adaptive_metadata']:
                    modular_config['_adaptive_metadata']['generation_timestamp'] = datetime.now(
                    ).isoformat()

                # Save the config
                if output_path is None:
                    config_dir = os.path.join(os.path.dirname(
                        os.path.dirname(os.path.abspath(__file__))), 'config')
                    base_config_dir = os.path.join(config_dir, 'base')
                    os.makedirs(base_config_dir, exist_ok=True)
                    level_name = modular_config.get(
                        'level_name', 'Modular AI Challenge')
                    from .config_naming import get_unique_creative_filename
                    output_name = get_unique_creative_filename(
                        level_name, base_config_dir)
                    output_path = os.path.join(base_config_dir, output_name)

                # ENFORCE MINIMUM STARTING MONEY (Fix for low starting cash issue)
                modular_config = self._enforce_minimum_starting_money(
                    modular_config)

                with open(output_path, 'w') as f:
                    json.dump(modular_config, f, indent=2)

                print("🧩✅ MODULAR AI MULTI-GAME GENERATION SUCCESSFUL!")
                print(f"   Saved to: {output_path}")
                return modular_config

            except Exception as e:
                print(f"🧩❌ Modular AI generation failed: {e}")
                print("🔄 Trying full AI generation...")
                # Add more detailed error information
                import traceback
                print(f"   Error details: {traceback.format_exc()}")

        # Priority 2: Full AI (less reliable but more comprehensive)
        if self.use_full_ai and self.full_ai_generator and self.ai_available:
            print("🤖🎨 ATTEMPTING FULL AI GENERATION from multi-game analysis...")
            try:
                # ★ FIX: Ensure multi_game_context is properly set ★
                if not hasattr(latest_performance, 'multi_game_context'):
                    latest_performance.multi_game_context = {}

                # Add multi-game context to the latest performance
                latest_performance.multi_game_context = {
                    'games_analyzed': len(performances),
                    'avg_score': avg_score,
                    'win_rate': win_rate,
                    'trend': trend,
                    'performance_summaries': []
                }

                # Add performance summaries for context
                for perf in performances:
                    latest_performance.multi_game_context['performance_summaries'].append({
                        'score': perf.score,
                        'win_flag': perf.win_flag,
                        'lives_remaining': perf.lives_remaining,
                        'starting_lives': perf.starting_lives,
                        'lives_remaining_pct': (perf.lives_remaining / perf.starting_lives) * 100,
                        # ★ FIX: Ensure not None
                        'towers_built': perf.towers_built if perf.towers_built else {},
                        # ★ FIX: Handle None
                        'tower_diversity': len([t for t, c in (perf.towers_built if perf.towers_built else {}).items() if c > 0]),
                        'previous_config_name': perf.previous_config.get('level_name', 'Unknown') if perf.previous_config else 'Unknown'
                    })

                # Try full AI generation with multi-game context
                full_ai_config = self.generate_with_full_ai(
                    latest_performance, output_path)

                # ★ FIX: Check if full_ai_config is None ★
                if full_ai_config is None:
                    print("🤖❌ Full AI returned None config")
                    raise Exception("Full AI returned None config")

                # Add multi-game metadata to full AI config
                if '_adaptive_metadata' in full_ai_config and latest_performance.multi_game_context:
                    full_ai_config['_adaptive_metadata']['multi_game_context'] = latest_performance.multi_game_context
                    full_ai_config['_adaptive_metadata']['generation_method'] = 'full_ai_multi_game'

                print("🤖✅ FULL AI MULTI-GAME GENERATION SUCCESSFUL!")
                return full_ai_config

            except Exception as e:
                print(f"🤖❌ Full AI generation failed: {e}")
                print("🔄 Falling back to AI analysis + procedural generation...")
                # Add more detailed error information
                import traceback
                print(f"   Error details: {traceback.format_exc()}")

        # ★ Fallback: AI Analysis + Procedural (existing method) ★
        print("🤖🔧 Using AI analysis + procedural generation...")

        # Create enhanced prompt for multi-game analysis
        performance_history = []
        for i, perf in enumerate(performances):
            performance_history.append({
                'game_number': i + 1,
                'score': perf.score,
                'won': perf.win_flag,
                'lives_remaining_pct': (perf.lives_remaining / perf.starting_lives) * 100,
                # ★ FIX: Handle None
                'towers_used': len([t for t, c in (perf.towers_built if perf.towers_built else {}).items() if c > 0])
            })

        # ★ NEW: Use weighted difficulty calculation instead of just aggregate context
        weighted_analysis = self.calculate_weighted_difficulty_adjustment(
            performances)

        # ★ FIXED: Ensure performance_history is not None ★
        if not performance_history:
            print("❌ No valid performance history, using single-game analysis")
            return self.analyze_performance_with_ai(latest_performance)

        # Generate config with weighted context
        adjustments = self._analyze_multi_game_performance_with_weighting(
            latest_performance, performance_history, avg_score, trend, performances, weighted_analysis)

        # ★ FIX: Ensure adjustments is not None ★
        if adjustments is None:
            print("❌ Multi-game analysis returned None, using single-game analysis")
            return self.analyze_performance_with_ai(latest_performance)

        if self.ai_available:
            print(
                f"🤖📊 AI WEIGHTED MULTI-GAME RECOMMENDATION: {adjustments.get('reasoning', 'No reasoning available')}")
            # Show AI's specific enemy strategy
            enemy_strategy = adjustments.get('enemy_composition_strategy', {})
            if enemy_strategy and 'primary_enemy_types' in enemy_strategy:
                primary_enemies = enemy_strategy['primary_enemy_types']
                print(
                    f"🎯 AI COUNTER-STRATEGY: Target {primary_enemies} to break player's tower dependency")
        else:
            print(
                f"🔧📊 RULE-BASED WEIGHTED RECOMMENDATION: {adjustments.get('reasoning', 'No reasoning available')}")

        # Show weighted analysis summary
        weighted_summary = weighted_analysis.get(
            'reasoning', 'No weighted analysis available')
        print(f"⚖️ WEIGHTED DIFFICULTY ANALYSIS: {weighted_summary}")

        # Apply adjustments to generate new config
        new_difficulty = adjustments.get(
            'difficulty_adjustment', {}).get('new_difficulty', 50)

        # Round to integer for consistency
        new_difficulty = int(round(new_difficulty))

        # Calculate adaptive wave count for multi-game analysis based on difficulty
        base_waves = self._calculate_base_waves_from_difficulty(new_difficulty)

        if 'wave_adjustments' in adjustments and 'total_waves_modifier' in adjustments['wave_adjustments']:
            wave_modifier = adjustments['wave_adjustments']['total_waves_modifier']
            # Clamp between 5-100 waves (lowered minimum for difficulty 1)
            adaptive_waves = max(5, min(100, int(base_waves * wave_modifier)))
        else:
            adaptive_waves = base_waves

        print(
            f"🎯 Multi-Game Adaptive Wave Count: {adaptive_waves} waves (base: {base_waves})")

        # Generate base config
        base_config = self.base_generator.generate_config(
            difficulty=new_difficulty,
            width=20,
            height=15,
            total_waves=adaptive_waves
        )

        # Apply AI-suggested modifications
        modified_config = self._apply_adjustments(
            base_config, adjustments, latest_performance)

        # Add enhanced metadata for multi-game analysis
        modified_config['_adaptive_metadata'] = {
            'recent_performance_summary': {
                'games_analyzed': len(performances),
                'average_score': avg_score,
                'win_rate': win_rate,
                'trend': trend,
                'performance_history': performance_history
            },
            'ai_adjustments': adjustments,
            'generation_timestamp': datetime.now().isoformat(),
            'generation_method': 'ai' if self.ai_available else 'rule_based'
        }

        # Generate output path if not provided
        if output_path is None:
            # Auto-generate sequential filename
            config_dir = os.path.join(os.path.dirname(
                os.path.dirname(os.path.abspath(__file__))), 'config')
            os.makedirs(config_dir, exist_ok=True)

            # Use creative filename if level name was generated, otherwise use numbered approach
            level_name = modified_config.get('level_name')
            if level_name:
                from .config_naming import get_unique_creative_filename
                output_name = get_unique_creative_filename(
                    level_name, config_dir)
            else:
                # Fallback to numbered approach for configs without level names
                counter = 1
                while True:
                    output_name = f"{counter}.json"
                    test_path = os.path.join(config_dir, output_name)
                    if not os.path.exists(test_path):
                        break
                    counter += 1

            output_path = os.path.join(config_dir, output_name)

        # ENFORCE MINIMUM STARTING MONEY (Fix for low starting cash issue)
        modified_config = self._enforce_minimum_starting_money(modified_config)

        # Save the config
        with open(output_path, 'w') as f:
            json.dump(modified_config, f, indent=2)
        method = "AI-generated" if self.ai_available else "Rule-based"
        level_display = f" - {modified_config['level_name']}" if 'level_name' in modified_config else ""
        print(
            f"✅ {method} multi-game adaptive configuration saved to {output_path}{level_display}")

        return modified_config

    def _analyze_multi_game_tower_patterns(self, performances: list) -> str:
        """Analyze tower usage patterns across multiple games to identify consistent strategies"""
        if not performances:
            return "No performance data available for multi-game analysis"

        # Aggregate tower usage across all games
        total_tower_usage = {}
        total_towers_across_games = 0
        games_with_towers = 0

        for performance in performances:
            game_total = sum(performance.towers_built.values())
            if game_total > 0:
                games_with_towers += 1
                total_towers_across_games += game_total
                for tower, count in performance.towers_built.items():
                    total_tower_usage[tower] = total_tower_usage.get(
                        tower, 0) + count

        if games_with_towers == 0:
            return "No towers built across any games - player struggling with basic mechanics"

        analysis_parts = []
        analysis_parts.append(
            f"Multi-game analysis: {len(performances)} games, {games_with_towers} games with towers")
        analysis_parts.append(
            f"Total towers across all games: {total_towers_across_games}")
        analysis_parts.append(
            f"Average towers per game: {total_towers_across_games / games_with_towers:.1f}")

        # Calculate overall percentages
        tower_percentages = {tower: (count / total_towers_across_games) * 100
                             for tower, count in total_tower_usage.items() if count > 0}

        sorted_towers = sorted(tower_percentages.items(),
                               key=lambda x: x[1], reverse=True)

        # Identify consistently dominant towers (>20% across all games)
        consistent_dominant = [(tower, pct)
                               for tower, pct in sorted_towers if pct >= 20]
        if consistent_dominant:
            analysis_parts.append(
                "\nCONSISTENT DOMINANT STRATEGIES (Across All Games):")
            for tower, pct in consistent_dominant:
                analysis_parts.append(
                    f"  • {tower}: {pct:.1f}% - PERSISTENT HEAVY RELIANCE")

        # Identify consistently used towers (10-20%)
        consistent_moderate = [(tower, pct)
                               for tower, pct in sorted_towers if 10 <= pct < 20]
        if consistent_moderate:
            analysis_parts.append("\nCONSISTENT MODERATE USAGE:")
            for tower, pct in consistent_moderate:
                analysis_parts.append(
                    f"  • {tower}: {pct:.1f}% - Regular usage pattern")

        # Identify towers rarely or never used (potential weakness)
        all_possible_towers = ['basic', 'cannon', 'sniper', 'laser', 'freezer', 'ice', 'lightning',
                               'poison', 'splash', 'explosive', 'flame', 'antiair', 'missile', 'detector']
        unused_towers = [
            tower for tower in all_possible_towers if tower not in total_tower_usage]
        underused_towers = [tower for tower, pct in sorted_towers if pct < 5]

        if unused_towers or underused_towers:
            analysis_parts.append("\nCONSISTENT STRATEGIC GAPS:")
            for tower in unused_towers:
                analysis_parts.append(
                    f"  • {tower}: NEVER USED - Major strategic gap")
            for tower in underused_towers:
                pct = tower_percentages[tower]
                analysis_parts.append(
                    f"  • {tower}: {pct:.1f}% - Consistently underutilized")

        # Analyze consistency patterns across games
        consistency_analysis = []

        # Check for pattern consistency (how often player uses same strategies)
        game_patterns = []
        for performance in performances:
            if sum(performance.towers_built.values()) > 0:
                game_total = sum(performance.towers_built.values())
                game_pattern = {tower: (count / game_total) * 100
                                for tower, count in performance.towers_built.items() if count > 0}
                game_patterns.append(game_pattern)

        # Check for strategic inflexibility
        if len(game_patterns) >= 3:
            # Check if player consistently uses the same tower types across games
            consistent_high_usage = {}
            for tower in total_tower_usage.keys():
                high_usage_games = sum(
                    1 for pattern in game_patterns if pattern.get(tower, 0) >= 25)
                if high_usage_games >= len(game_patterns) * 0.6:  # 60% of games
                    consistent_high_usage[tower] = high_usage_games

            if consistent_high_usage:
                consistency_analysis.append(
                    "STRATEGIC INFLEXIBILITY DETECTED:")
                for tower, game_count in consistent_high_usage.items():
                    consistency_analysis.append(
                        f"  • {tower}: Heavily used in {game_count}/{len(game_patterns)} games")

        # Check for adaptation patterns
        if len(performances) >= 3:
            recent_games = performances[:2]  # Last 2 games
            older_games = performances[2:]   # Older games

            recent_diversity = sum(len([t for t, c in p.towers_built.items(
            ) if c > 0]) for p in recent_games) / len(recent_games)
            older_diversity = sum(len([t for t, c in p.towers_built.items(
            ) if c > 0]) for p in older_games) / len(older_games)

            if recent_diversity > older_diversity + 1:
                consistency_analysis.append(
                    "POSITIVE ADAPTATION: Player is diversifying tower usage")
            elif recent_diversity < older_diversity - 1:
                consistency_analysis.append(
                    "NEGATIVE ADAPTATION: Player becoming more narrow in strategy")
            else:
                consistency_analysis.append(
                    "STATIC ADAPTATION: Player not changing strategic approach")

        if consistency_analysis:
            analysis_parts.append("\nMULTI-GAME PATTERN ANALYSIS:")
            for pattern in consistency_analysis:
                analysis_parts.append(f"  • {pattern}")

        # Strategic vulnerability analysis across games
        vulnerability_analysis = []

        # Consistent splash reliance
        total_splash = sum(total_tower_usage.get(t, 0)
                           for t in ['cannon', 'splash', 'explosive', 'flame'])
        if total_splash / total_towers_across_games > 0.35:
            vulnerability_analysis.append(
                "PERSISTENT SPLASH RELIANCE - Consistently vulnerable to single strong enemies")

        # Consistent single-target focus
        total_single = sum(total_tower_usage.get(t, 0)
                           for t in ['sniper', 'laser', 'basic'])
        if total_towers_across_games > 0 and total_single / total_towers_across_games > 0.45:
            vulnerability_analysis.append(
                "PERSISTENT SINGLE-TARGET FOCUS - Consistently vulnerable to swarms")

        # Consistent anti-air weakness
        total_antiair = sum(total_tower_usage.get(t, 0)
                            for t in ['antiair', 'missile', 'lightning'])
        if total_towers_across_games > 0 and total_antiair / total_towers_across_games < 0.15:
            vulnerability_analysis.append(
                "PERSISTENT ANTI-AIR WEAKNESS - Consistently vulnerable to flying enemies")

        # Consistent slow/control reliance
        total_control = sum(total_tower_usage.get(t, 0)
                            for t in ['freezer', 'ice', 'poison'])
        if total_control / total_towers_across_games > 0.3:
            vulnerability_analysis.append(
                "PERSISTENT CONTROL RELIANCE - Consistently vulnerable to immune enemies")

        if vulnerability_analysis:
            analysis_parts.append(
                "\nPERSISTENT VULNERABILITIES (Prime for Enemy Counter-Strategy):")
            for vulnerability in vulnerability_analysis:
                analysis_parts.append(f"  • {vulnerability}")

        # Enemy counter-strategy recommendations
        counter_strategy = []
        for tower, pct in consistent_dominant:
            if tower in ['basic', 'cannon']:
                counter_strategy.append(
                    "ESCALATE Flying enemies (consistently counter ground splash)")
            elif tower in ['sniper', 'laser']:
                counter_strategy.append(
                    "ESCALATE Fast/Teleporting enemies (consistently counter precision targeting)")
            elif tower in ['freezer', 'ice']:
                counter_strategy.append(
                    "ESCALATE Fire Elemental enemies (consistently counter freeze strategies)")
            elif tower == 'lightning':
                counter_strategy.append(
                    "ESCALATE Grounded enemies (consistently counter lightning dependency)")
            elif tower == 'poison':
                counter_strategy.append(
                    "ESCALATE Spectral/Phase enemies (consistently counter poison reliance)")

        if counter_strategy:
            analysis_parts.append("\nRECOMMENDED ESCALATED ENEMY COUNTERS:")
            for strategy in set(counter_strategy):  # Remove duplicates
                analysis_parts.append(f"  • {strategy}")

        return "\n".join(analysis_parts)

    def _analyze_multi_game_performance_with_weighting(self, latest_performance: PerformanceData,
                                                       performance_history: list, avg_score: float, trend: str,
                                                       performances: list, weighted_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze performance across multiple games with AI using weighted difficulty adjustment"""

        # Check if AI is available
        if not self.ai_available or not self.client:
            print(
                "🔄 AI not available, using rule-based multi-game adjustments with weighting")
            return self.create_weighted_fallback_adjustments(latest_performance, weighted_analysis)

        history_summary = "\n".join([
            f"Game {p['game_number']}: Score {p['score']:.1f}, {'Won' if p['won'] else 'Lost'}, "
            f"{p['lives_remaining_pct']:.1f}% lives, {p['towers_used']} tower types"
            for p in performance_history
        ])

        # Analyze player's multi-game tower patterns
        multi_game_tower_analysis = self._analyze_multi_game_tower_patterns(
            performances)

        # Get difficulty progression for AI analysis
        difficulty_progression = self._extract_difficulty_progression(
            performances)
        difficulty_progression_str = " → ".join(map(
            str, difficulty_progression)) if difficulty_progression else "No progression data"

        # Use weighted analysis for difficulty
        current_difficulty = weighted_analysis['current_difficulty']
        new_difficulty = weighted_analysis['new_difficulty']

        prompt = f"""
You are an expert tower defense game designer. Analyze this player's performance using WEIGHTED DIFFICULTY ANALYSIS.

WEIGHTED ANALYSIS RESULTS:
{weighted_analysis['reasoning']}

Direction: {weighted_analysis['direction']}
Current Difficulty: {current_difficulty}
New Difficulty: {new_difficulty} (change: {weighted_analysis['magnitude']:+d})
Weighted Performance Score: {weighted_analysis['weighted_avg_score']:.1f}%

RECENT GAME HISTORY:
{history_summary}

PERFORMANCE SUMMARY:
- Simple Average Score: {avg_score:.1f}/100
- Weighted Average Score: {weighted_analysis['weighted_avg_score']:.1f}/100 (accounts for game difficulty)
- Performance Trend: {trend}
- Games Analyzed: {len(performance_history)}

DIFFICULTY PROGRESSION: {difficulty_progression_str}

MULTI-GAME TOWER PATTERN ANALYSIS:
{multi_game_tower_analysis}

LATEST GAME DETAILS:
{latest_performance.get_performance_summary()}

🎯 IMPORTANT: The difficulty has already been calculated using weighted analysis.
- Use the new difficulty: {new_difficulty}
- Your job is to design enemy compositions and other settings that match this difficulty level
- Focus on creating strategic challenges appropriate for a difficulty {new_difficulty}/100 game

AVAILABLE ENEMIES (you MUST choose only from this list):
Basic Tier: BasicEnemy, FastEnemy, TankEnemy, FlyingEnemy, ShieldedEnemy
Immune Tier: ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy, FireElementalEnemy, ToxicEnemy, PhaseShiftEnemy, BlastProofEnemy
Advanced Tier: InvisibleEnemy, RegeneratingEnemy, TeleportingEnemy, SplittingEnemy, SpectralEnemy, CrystallineEnemy, ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy

TOWER COUNTER-STRATEGY GUIDE:
- vs BASIC/CANNON towers → Use FlyingEnemy (immune to ground splash) or ArmoredEnemy
- vs SNIPER/LASER towers → Use FastEnemy, TeleportingEnemy, or PhaseShiftEnemy
- vs FREEZER/ICE towers → Use FireElementalEnemy (heals from ice damage)
- vs LIGHTNING towers → Use GroundedEnemy (immune to lightning)
- vs POISON towers → Use ToxicEnemy (immune to poison) or SpectralEnemy
- vs SPLASH towers → Use SplittingEnemy or TeleportingEnemy
- vs FLAME towers → Use FireElementalEnemy (heals from fire damage)
- vs LASER towers → Use EnergyShieldEnemy or CrystallineEnemy

Please provide your analysis in JSON format with these sections:
- reasoning: Brief explanation of your strategy
- difficulty_adjustment: {{"new_difficulty": {new_difficulty}, "reasoning": "Using weighted analysis"}}
- enemy_composition_strategy: Enemy types and strategies to use
- wave_adjustments: Any wave-specific modifications
- economic_adjustments: Starting money/lives adjustments if needed
- terrain_strategy: Terrain modifications to support the strategy
"""

        try:
            response = self.client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=2000,
                temperature=0.7,
                messages=[{"role": "user", "content": prompt}]
            )

            # Parse AI response
            ai_response = response.content[0].text.strip()

            # Try to extract JSON from AI response
            import re
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                import json
                adjustments = json.loads(json_match.group())

                # Ensure the weighted difficulty is preserved
                if 'difficulty_adjustment' not in adjustments:
                    adjustments['difficulty_adjustment'] = {}
                adjustments['difficulty_adjustment']['new_difficulty'] = new_difficulty
                adjustments['difficulty_adjustment']['weighted_analysis'] = weighted_analysis

                return adjustments
            else:
                print("❌ Failed to parse AI JSON response, using weighted fallback")
                return self.create_weighted_fallback_adjustments(latest_performance, weighted_analysis)

        except Exception as e:
            print(f"❌ AI analysis failed: {e}, using weighted fallback")
            return self.create_weighted_fallback_adjustments(latest_performance, weighted_analysis)

    def create_weighted_fallback_adjustments(self, latest_performance: PerformanceData,
                                             weighted_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback adjustments using weighted analysis when AI is unavailable"""

        new_difficulty = weighted_analysis['new_difficulty']
        direction = weighted_analysis['direction']
        weighted_score = weighted_analysis['weighted_avg_score']

        # Basic enemy strategy based on weighted performance
        if weighted_score >= 70:
            # Strong player - use counter-strategies
            primary_enemies = ["FlyingEnemy", "TeleportingEnemy",
                               "ArmoredEnemy", "FireElementalEnemy"]
            strategy = "Deploy counter-enemies to challenge strong performance patterns"
        elif weighted_score >= 45:
            # Average player - balanced challenge
            primary_enemies = ["BasicEnemy",
                               "FastEnemy", "TankEnemy", "FlyingEnemy"]
            strategy = "Balanced challenge appropriate for average performance"
        else:
            # Struggling player - easier enemies
            primary_enemies = ["BasicEnemy", "FastEnemy", "TankEnemy"]
            strategy = "Supportive enemies to build confidence"

        return {
            'reasoning': f"Weighted rule-based analysis: {weighted_analysis['reasoning']}",
            'difficulty_adjustment': {
                'new_difficulty': new_difficulty,
                'weighted_analysis': weighted_analysis,
                'reasoning': f"Weighted analysis: {direction} difficulty to {new_difficulty}"
            },
            'enemy_composition_strategy': {
                'primary_enemy_types': primary_enemies,
                'strategy_focus': strategy
            },
            'wave_adjustments': {
                'total_waves_modifier': 1.0,
                'spawn_scaling': 'normal'
            },
            'economic_adjustments': {
                'starting_money_modifier': 1.0,
                'starting_lives_modifier': 1.0
            },
            'terrain_strategy': {
                'terrain_focus': 'balanced',
                'special_terrain_percentage': 0.2
            }
        }

    def _analyze_multi_game_performance(self, latest_performance: PerformanceData,
                                        performance_history: list, avg_score: float, trend: str, performances: list) -> Dict[str, Any]:
        """Analyze performance across multiple games with AI"""

        # Check if AI is available
        if not self.ai_available or not self.client:
            print("🔄 AI not available, using rule-based multi-game adjustments")
            return create_multi_game_fallback_adjustments(latest_performance, avg_score, trend)

        history_summary = "\n".join([
            f"Game {p['game_number']}: Score {p['score']:.1f}, {'Won' if p['won'] else 'Lost'}, "
            f"{p['lives_remaining_pct']:.1f}% lives, {p['towers_used']} tower types"
            for p in performance_history
        ])

        # Analyze player's multi-game tower patterns
        multi_game_tower_analysis = self._analyze_multi_game_tower_patterns(
            performances)

        # Get difficulty progression for AI analysis
        difficulty_progression = self._extract_difficulty_progression(
            performances)
        difficulty_progression_str = " → ".join(map(
            str, difficulty_progression)) if difficulty_progression else "No progression data"

        # Extract current difficulty for context with multiple fallback methods
        current_difficulty = 50  # Default
        if latest_performance.previous_config:
            config = latest_performance.previous_config

            # Method 1: From generation metadata (preferred)
            if '_generation_metadata' in config and 'difficulty' in config['_generation_metadata']:
                current_difficulty = config['_generation_metadata']['difficulty']
            # Method 2: From adaptive metadata (AI-generated configs)
            elif '_adaptive_metadata' in config:
                ai_adj = config['_adaptive_metadata'].get('ai_adjustments', {})
                if 'difficulty_adjustment' in ai_adj and 'new_difficulty' in ai_adj['difficulty_adjustment']:
                    current_difficulty = ai_adj['difficulty_adjustment']['new_difficulty']
            # Method 3: Guess from economic indicators (test configs, etc.)
            else:
                starting_money = config.get(
                    'game_config', {}).get('starting_money', 500)
                starting_lives = config.get(
                    'game_config', {}).get('starting_lives', 20)

                # For test configs like the one with 100,000 money and 1,000 lives
                if starting_money >= 50000 or starting_lives >= 500:
                    current_difficulty = 1  # Test config - very easy
                elif starting_money <= 300:
                    current_difficulty = 80  # Very hard
                elif starting_money <= 400:
                    current_difficulty = 60  # Hard
                elif starting_money >= 600:
                    current_difficulty = 30  # Easy
                else:
                    current_difficulty = 50  # Medium

        prompt = f"""
You are an expert tower defense game designer. Analyze this player's performance across their last {len(performance_history)} games and suggest specific adjustments for the next level.

RECENT GAME HISTORY:
{history_summary}

PERFORMANCE SUMMARY:
- Average Score: {avg_score:.1f}/100
- Performance Trend: {trend}
- Games Analyzed: {len(performance_history)}

CURRENT DIFFICULTY LEVEL: {current_difficulty}/100

🎯 CRITICAL: DIFFICULTY MUST BE ADJUSTED IN EVERY CONFIG
The difficulty value will be stored in the generated config file as:
- _generation_metadata.difficulty = your_new_difficulty_number
- _adaptive_metadata.ai_adjustments.difficulty_adjustment.new_difficulty = your_new_difficulty_number

IMPORTANT: Make GRADUAL difficulty adjustments! Never jump more than 15 levels at once.
- Excellent multi-game performance (80%+ avg): Increase by 10-15 levels
- Good multi-game performance (60-79% avg): Increase by 5-10 levels  
- Average multi-game performance (40-59% avg): Increase by 0-5 levels
- Poor multi-game performance (20-39% avg): Decrease by 5-10 levels
- Very poor multi-game performance (<20% avg): Decrease by 10-15 levels

EXAMPLE: Current difficulty {current_difficulty}, Avg score 75% → New difficulty should be {current_difficulty + 8} (increase by 8)

DIFFICULTY PROGRESSION ANALYSIS:
- Recent Difficulty Progression: {difficulty_progression_str}
- This shows {'a gradual increase' if len(difficulty_progression) >= 3 and difficulty_progression[0] > difficulty_progression[-1] else 'a gradual decrease' if len(difficulty_progression) >= 3 and difficulty_progression[0] < difficulty_progression[-1] else 'stable difficulty'} over {len(difficulty_progression)} games
- Player has been challenged across difficulty range: {min(difficulty_progression) if difficulty_progression else 'unknown'}-{max(difficulty_progression) if difficulty_progression else 'unknown'}
- Use this to determine appropriate next difficulty level

MULTI-GAME TOWER PATTERN ANALYSIS:
{multi_game_tower_analysis}

LATEST GAME DETAILS:
{latest_performance.get_performance_summary()}

AVAILABLE ENEMIES (you MUST choose only from this list):
Basic Tier: BasicEnemy, FastEnemy, TankEnemy, FlyingEnemy, ShieldedEnemy
Immune Tier: ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy, FireElementalEnemy, ToxicEnemy, PhaseShiftEnemy, BlastProofEnemy
Advanced Tier: InvisibleEnemy, RegeneratingEnemy, TeleportingEnemy, SplittingEnemy, SpectralEnemy, CrystallineEnemy, ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy

TOWER COUNTER-STRATEGY GUIDE:
- vs BASIC/CANNON towers → Use FlyingEnemy (immune to ground splash) or ArmoredEnemy (immune to basic)
- vs SNIPER/LASER towers → Use FastEnemy, TeleportingEnemy, or PhaseShiftEnemy (hard to target)
- vs FREEZER/ICE towers → Use FireElementalEnemy (heals from ice damage)
- vs LIGHTNING towers → Use GroundedEnemy (immune to lightning)
- vs POISON towers → Use ToxicEnemy (immune to poison) or SpectralEnemy (phases through)
- vs SPLASH towers → Use SplittingEnemy (creates more targets) or TeleportingEnemy (escapes AOE)
- vs FLAME towers → Use FireElementalEnemy (heals from fire damage)
- vs LASER towers → Use EnergyShieldEnemy (resistant) or CrystallineEnemy (only laser works but reduced damage)

PERFORMANCE-BASED MULTI-GAME COUNTER-STRATEGY GUIDE:

★ FOR CONSISTENTLY STRUGGLING PLAYERS (Avg Score < 40%):
- DO NOT escalate harsh counter-enemies across multiple games
- Focus on economic help and consistent easier challenges
- Use only BasicEnemy, FastEnemy, TankEnemy for manageable challenges
- Goal: Build confidence over time, teach basics gradually
- Multi-game pattern: Supportive progression, not punishment

★ FOR AVERAGE MULTI-GAME PLAYERS (Avg Score 40-60%):
- Apply GRADUAL educational counters across games
- Light escalation of FlyingEnemy vs basic/cannon patterns
- Gentle increase of FastEnemy vs sniper/laser habits
- Minor FireElementalEnemy growth vs ice tower dependence
- Goal: Progressive strategic development across games

★ FOR CONSISTENTLY STRONG PLAYERS (Avg Score 60%+):
- Apply ESCALATING counter-strategies to break comfort zones
- Escalate FlyingEnemy frequency vs BASIC/CANNON tower habits
- Increase TeleportingEnemy/FastEnemy spawns vs SNIPER tower patterns
- Introduce more FireElementalEnemy vs FREEZER/ICE habits
- Use more GroundedEnemy vs LIGHTNING tower dependence
- Escalate SpectralEnemy/ToxicEnemy vs POISON tower patterns
- Introduce more SplittingEnemy vs SPLASH tower habits
- Add more ArmoredEnemy vs BASIC tower reliance

TERRAIN STRATEGY (SECONDARY SUPPORT):
- Use terrain to reinforce enemy counter-strategies
- Support enemy movement and positioning, not replace counter-strategy

Analyze the multi-game performance patterns and determine appropriate adjustments. ALWAYS include difficulty adjustment as a core part of your analysis. Provide recommendations in JSON format:

{{
    "level_name": "Creative level name reflecting the multi-game enemy counter-strategy",
    "difficulty_adjustment": {{"explanation": "REQUIRED: explain your difficulty change reasoning based on current {current_difficulty} and multi-game performance", "new_difficulty": 55}},
    "wave_adjustments": {{"total_waves_modifier": 0.2-1.25, "explanation": "your wave count decision based on multi-game patterns"}},
    "economic_adjustments": {{"starting_money_modifier": 0.8-1.5, "enemy_rewards_modifier": 0.8-1.3}},
    "enemy_composition_strategy": {{"primary_enemy_types": ["EnemyName1", "EnemyName2"], "multi_game_strategy": "how this builds on player's repeated patterns across games"}},
    "enemy_adjustments": {{"health_modifier": 0.7-1.4, "speed_modifier": 0.8-1.3, "count_modifier": 0.8-1.3}},
    "map_adjustments": {{"complexity_modifier": 0.8-1.2, "obstacle_density_modifier": 0.8-1.2, "terrain_strategy": "terrain to support enemy counter-strategy"}},
    "tower_adjustments": {{"cost_modifier": 0.8-1.3, "damage_modifier": 0.9-1.2}},
    "rock_removal_adjustments": {{"base_cost": 100-800, "wave_scaling_factor": 0.05-0.15, "explanation": "rock removal should be a hefty strategic investment that scales dramatically with wave progression for late-game balance"}},
    "special_mechanics": ["mechanics based on your multi-game tower pattern analysis"],
    "reasoning": "your comprehensive analysis of consistent tower patterns and escalated enemy counter-strategies"
}}

LEVEL NAME GUIDELINES FOR MULTI-GAME ANALYSIS:
- Reflect escalating counter-strategies (Advanced Flying Assault, Master Teleporter Trial, Expert Fire Walker Challenge)
- Consider pattern breaking themes (Strategy Breaker, Comfort Zone Challenger, Adaptation Test)
- Include consistency themes (Pattern Punisher, Habit Breaker, Strategy Evolution)
- Match player's strategic journey and needed growth

MULTI-GAME ANALYSIS FRAMEWORK:
- Base wave count scales progressively: Difficulty 1 = ~6 waves, Difficulty 50 = ~48 waves, Difficulty 100 = ~91 waves
- Modifier range is 0.2-1.25 (resulting in 1-115 waves total)
- Consider performance trends: improving, declining, or stable
- Analyze consistency patterns across multiple games
- Factor in win rates, average scores, and skill development
- Balance progressive challenge with player retention
- Difficulty 1 should result in 5-7 waves maximum
- Difficulty 50 should result in 9-60 waves maximum
- Difficulty 100 should result in 18-113 waves maximum

MULTI-GAME ENEMY STRATEGY:
- Consistent tower patterns → Escalate specific enemy counters across waves
- Repeated strategic weaknesses → Design enemy compositions to exploit these patterns
- Lack of adaptation → Force diversification through targeted enemy types
- Pattern of similar failures → Break comfort zone with challenging enemy combinations

Focus on creating enemy compositions that specifically target the player's consistent tower usage patterns across multiple games, forcing strategic evolution and adaptation.
"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are an expert game designer specializing in adaptive difficulty systems for tower defense games."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )

            ai_response = response.choices[0].message.content

            # Try to parse JSON from response
            try:
                start_idx = ai_response.find('{')
                end_idx = ai_response.rfind('}') + 1
                if start_idx != -1 and end_idx > start_idx:
                    json_str = ai_response[start_idx:end_idx]
                    return json.loads(json_str)
                else:
                    raise ValueError("No JSON found in response")
            except (json.JSONDecodeError, ValueError) as e:
                print(f"Warning: Could not parse AI response as JSON: {e}")
                return create_multi_game_fallback_adjustments(latest_performance, avg_score, trend)

        except Exception as e:
            print(f"Warning: OpenAI API call failed: {e}")
            return create_multi_game_fallback_adjustments(latest_performance, avg_score, trend)

    def _apply_adjustments(self, base_config: Dict[str, Any],
                           adjustments: Dict[str, Any],
                           performance: PerformanceData) -> Dict[str, Any]:
        """Apply AI-suggested adjustments to base config"""

        config = base_config.copy()

        # Add AI-generated level name if available
        if 'level_name' in adjustments:
            config['level_name'] = adjustments['level_name']

        # Economic adjustments
        econ_adj = adjustments.get('economic_adjustments', {})
        if 'game_config' in config:
            if 'starting_money_modifier' in econ_adj:
                config['game_config']['starting_money'] = int(
                    config['game_config']['starting_money'] *
                    econ_adj['starting_money_modifier']
                )

        # Wave adjustments (store total waves for UI access)
        wave_adj = adjustments.get('wave_adjustments', {})
        if 'wave_config' in config:
            # Ensure the total_waves field is available for UI
            if 'total_waves_modifier' in wave_adj:
                # The total waves should already be set by the base config generator
                # Just ensure it's accessible in wave_config for UI
                if 'total_waves' not in config['wave_config']:
                    # Extract from metadata or use default
                    if '_generation_metadata' in config and 'total_waves' in config['_generation_metadata']:
                        config['wave_config']['total_waves'] = config['_generation_metadata']['total_waves']
                    else:
                        # Fallback calculation if not available - use difficulty-appropriate base
                        difficulty = config.get(
                            '_generation_metadata', {}).get('difficulty', 50)
                        base_waves = self._calculate_base_waves_from_difficulty(
                            difficulty)
                        modifier = wave_adj['total_waves_modifier']
                        config['wave_config']['total_waves'] = max(
                            5, min(100, int(base_waves * modifier)))

        # Enemy composition strategy adjustments
        enemy_comp_adj = adjustments.get('enemy_composition_strategy', {})
        if enemy_comp_adj and 'wave_config' in config:
            # Store the AI's enemy strategy for later application to wave compositions
            config['_ai_enemy_strategy'] = {
                'primary_enemy_types': enemy_comp_adj.get('primary_enemy_types', []),
                'strategy_explanation': enemy_comp_adj.get('strategy_explanation', ''),
                'multi_game_strategy': enemy_comp_adj.get('multi_game_strategy', '')
            }

            # Modify wave compositions to emphasize the recommended enemy types
            if 'wave_compositions' in config['wave_config']:
                self._apply_enemy_composition_strategy(config['wave_config']['wave_compositions'],
                                                       enemy_comp_adj.get('primary_enemy_types', []))

        # Enemy adjustments
        enemy_adj = adjustments.get('enemy_adjustments', {})
        if 'wave_config' in config and 'enemy_scaling' in config['wave_config']:
            scaling = config['wave_config']['enemy_scaling']

            if 'health_modifier' in enemy_adj:
                scaling['health_per_wave'] *= enemy_adj['health_modifier']

            if 'speed_modifier' in enemy_adj:
                scaling['speed_per_wave'] *= enemy_adj['speed_modifier']

        # Apply enemy buffs (AI-generated health and speed modifiers)
        enemy_buffs = adjustments.get('enemy_buffs', {})
        if enemy_buffs:
            if 'wave_config' not in config:
                config['wave_config'] = {}

            # Add enemy buffs to wave config (selective usage - 70% of available)
        # Note: AI should focus on strategic subset of enemies and buffs rather than overwhelming variety
            config['wave_config']['enemy_buffs'] = {}

            if 'max_health_modifier' in enemy_buffs:
                config['wave_config']['enemy_buffs']['max_health_modifier'] = enemy_buffs['max_health_modifier']
                print(
                    f"   🩸 Applied enemy health modifier: {enemy_buffs['max_health_modifier']:.2f}x")

            if 'max_speed_modifier' in enemy_buffs:
                config['wave_config']['enemy_buffs']['max_speed_modifier'] = enemy_buffs['max_speed_modifier']
                print(
                    f"   🏃 Applied enemy speed modifier: {enemy_buffs['max_speed_modifier']:.2f}x")

        # Tower adjustments
        tower_adj = adjustments.get('tower_adjustments', {})
        if 'tower_config' in config and 'base_costs' in config['tower_config']:
            if 'cost_modifier' in tower_adj:
                costs = config['tower_config']['base_costs']
                for tower_type in costs:
                    costs[tower_type] = int(
                        costs[tower_type] * tower_adj['cost_modifier'])

        # Rock removal adjustments
        rock_adj = adjustments.get('rock_removal_adjustments', {})
        if rock_adj:
            config['rock_removal_config'] = {
                'base_cost': rock_adj.get('base_cost', 200),
                'wave_scaling_factor': rock_adj.get('wave_scaling_factor', 0.1),
                'explanation': rock_adj.get('explanation', 'AI-determined rock removal costs')
            }

        # Update difficulty in metadata - ENSURE IT'S ALWAYS STORED
        if '_generation_metadata' not in config:
            config['_generation_metadata'] = {}

        config['_generation_metadata']['difficulty'] = adjustments['difficulty_adjustment']['new_difficulty']
        config['_generation_metadata']['adapted_from_performance'] = True

        # Also store in adaptive metadata for consistency
        if '_adaptive_metadata' not in config:
            config['_adaptive_metadata'] = {}
        if 'ai_adjustments' not in config['_adaptive_metadata']:
            config['_adaptive_metadata']['ai_adjustments'] = {}

        config['_adaptive_metadata']['ai_adjustments']['difficulty_adjustment'] = {
            'new_difficulty': adjustments['difficulty_adjustment']['new_difficulty'],
            'explanation': adjustments['difficulty_adjustment'].get('explanation', 'AI-determined difficulty adjustment'),
            'previous_difficulty': performance.previous_config.get('_generation_metadata', {}).get('difficulty', 50) if performance.previous_config else 50
        }

        # Store wave adjustment information in metadata
        if 'wave_adjustments' in adjustments:
            config['_generation_metadata']['wave_adjustments'] = adjustments['wave_adjustments']

        # Store rock removal adjustment information in metadata
        if 'rock_removal_adjustments' in adjustments:
            config['_generation_metadata']['rock_removal_adjustments'] = adjustments['rock_removal_adjustments']

        return config

    def _apply_enemy_composition_strategy(self, wave_compositions: dict, priority_enemies: list):
        """Apply AGGRESSIVE enemy composition strategy to force strategic adaptation"""
        if not priority_enemies:
            return

        # PROTECTION: Never modify the standardized first 5 waves
        protected_ranges = ["1-5"]

        # Enemy type name mapping for flexibility
        enemy_name_mapping = {
            'flying': ['FlyingEnemy'],
            'fast': ['FastEnemy'],
            'teleporting': ['TeleportingEnemy'],
            'fire': ['FireElementalEnemy'],
            'fire elemental': ['FireElementalEnemy'],
            'grounded': ['GroundedEnemy'],
            'spectral': ['SpectralEnemy'],
            'phase': ['PhaseShiftEnemy'],
            'splitting': ['SplittingEnemy'],
            'blast proof': ['BlastProofEnemy'],
            'armored': ['ArmoredEnemy'],
            'poison resistant': ['SpectralEnemy', 'PhaseShiftEnemy'],
            'lightning immune': ['GroundedEnemy'],
            'heat immune': ['FireElementalEnemy']
        }

        # Convert priority enemies to actual enemy class names
        target_enemies = []
        for priority in priority_enemies:
            priority_lower = priority.lower()
            if priority in enemy_name_mapping:
                target_enemies.extend(enemy_name_mapping[priority])
            elif priority_lower in enemy_name_mapping:
                target_enemies.extend(enemy_name_mapping[priority_lower])
            else:
                # Try to match partial names
                for key, enemies in enemy_name_mapping.items():
                    if key in priority_lower or priority_lower in key:
                        target_enemies.extend(enemies)
                        break
                else:
                    # Assume it's already a valid enemy name
                    target_enemies.append(priority)

        if not target_enemies:
            return

        print(
            f"🎯 Applying AGGRESSIVE enemy composition strategy: Emphasizing {target_enemies}")

        # Apply strategy to each wave range
        for wave_range, compositions in wave_compositions.items():
            # PROTECTION: Skip standardized first 5 waves
            if wave_range in protected_ranges:
                print(
                    f"   🛡️ PROTECTED: Skipping waves {wave_range} (standardized configuration)")
                continue

            if not isinstance(compositions, list):
                continue

            # Convert compositions to a modifiable format
            enemy_weights = {}
            for enemy_name, weight in compositions:
                enemy_weights[enemy_name] = weight

            # ★ AGGRESSIVE STRATEGY: Make counter-enemies DOMINATE ★
            boosted_any = False
            for target_enemy in target_enemies:
                if target_enemy in enemy_weights:
                    # ★ MASSIVE BOOST: Set target enemies to 35-50% spawn rate ★
                    if len(target_enemies) == 1:
                        # Single counter-enemy: Give it 45% dominance
                        desired_weight = 0.45
                    elif len(target_enemies) == 2:
                        # Two counter-enemies: 30% each
                        desired_weight = 0.30
                    else:
                        # Multiple counter-enemies: 25% each for first 2
                        desired_weight = 0.25

                    enemy_weights[target_enemy] = desired_weight
                    boosted_any = True
                    print(
                        f"   • DOMINATED {target_enemy} in waves {wave_range} ({desired_weight*100:.0f}% spawn rate)")

            # If target enemies don't exist in this range, try to add them if appropriate
            if not boosted_any and self._should_add_enemies_to_range(wave_range, target_enemies):
                # Add up to 2 counter-enemies
                for i, target_enemy in enumerate(target_enemies[:2]):
                    if target_enemy not in enemy_weights:
                        # Add with aggressive weight
                        desired_weight = 0.35 if i == 0 else 0.25
                        enemy_weights[target_enemy] = desired_weight
                        print(
                            f"   • ADDED {target_enemy} to waves {wave_range} ({desired_weight*100:.0f}% spawn rate)")
                        boosted_any = True

            if boosted_any:
                # ★ AGGRESSIVE REBALANCING: Counter-enemies get priority, others get leftovers ★

                # Calculate total weight allocated to counter-enemies
                counter_weight = sum(enemy_weights.get(enemy, 0)
                                     for enemy in target_enemies)
                # At least 10% for other enemies
                remaining_weight = max(0.1, 1.0 - counter_weight)

                # Reduce all non-counter enemies to fit in remaining space
                other_enemies = {enemy: weight for enemy, weight in enemy_weights.items()
                                 if enemy not in target_enemies}

                if other_enemies:
                    other_total = sum(other_enemies.values())
                    if other_total > 0:
                        # Scale down other enemies proportionally
                        scale_factor = remaining_weight / other_total
                        for enemy in other_enemies:
                            enemy_weights[enemy] *= scale_factor

                # Ensure we don't exceed 1.0 total weight
                total_weight = sum(enemy_weights.values())
                if total_weight > 1.0:
                    for enemy in enemy_weights:
                        enemy_weights[enemy] /= total_weight

                # Convert back to list format
                normalized_weights = [(enemy, weight)
                                      for enemy, weight in enemy_weights.items()]

                # Update the original compositions
                wave_compositions[wave_range] = normalized_weights

                # Debug output for first wave range
                if wave_range == list(wave_compositions.keys())[0]:
                    print(f"   📊 Final composition for {wave_range}:")
                    for enemy, weight in sorted(normalized_weights, key=lambda x: x[1], reverse=True)[:5]:
                        print(f"      • {enemy}: {weight*100:.1f}%")

    def _should_add_enemies_to_range(self, wave_range: str, target_enemies: list) -> bool:
        """Determine if we should add new enemy types to a wave range"""
        # Parse wave range to get starting wave number
        if '-' in wave_range:
            start_wave = int(wave_range.split('-')[0])
        else:
            start_wave = int(wave_range)

        # Enemy introduction thresholds (when each enemy type becomes available)
        enemy_thresholds = {
            'FlyingEnemy': 11,
            'TeleportingEnemy': 31,
            'FastEnemy': 1,
            'FireElementalEnemy': 31,
            'GroundedEnemy': 21,
            'SpectralEnemy': 51,
            'PhaseShiftEnemy': 41,
            'SplittingEnemy': 41,
            'BlastProofEnemy': 41,
            'ArmoredEnemy': 16
        }

        # Only add enemies that should be available at this wave range
        for enemy in target_enemies:
            threshold = enemy_thresholds.get(enemy, 1)
            if start_wave >= threshold:
                return True

        return False

    def _extract_difficulty_progression(self, performances: List[PerformanceData]) -> List[int]:
        """Extract difficulty levels from recent game configurations"""
        difficulty_progression = []

        for performance in performances:
            if performance.previous_config:
                # Try multiple ways to extract difficulty
                config = performance.previous_config

                # Method 1: From generation metadata
                difficulty = config.get(
                    '_generation_metadata', {}).get('difficulty')
                if difficulty is not None:
                    difficulty_progression.append(int(difficulty))
                    continue

                # Method 2: From adaptive metadata
                difficulty = config.get('_adaptive_metadata', {}).get(
                    'ai_adjustments', {}).get('difficulty_adjustment', {}).get('new_difficulty')
                if difficulty is not None:
                    difficulty_progression.append(int(difficulty))
                    continue

                # Method 3: From legacy generation_metadata difficulty_factors
                difficulty = config.get('_generation_metadata', {}).get(
                    'difficulty_factors', {}).get('difficulty')
                if difficulty is not None:
                    difficulty_progression.append(int(difficulty))
                    continue

                # Method 4: Estimate from game_config difficulty indicators
                starting_money = config.get(
                    'game_config', {}).get('starting_money', 500)
                starting_lives = config.get(
                    'game_config', {}).get('starting_lives', 20)

                # Estimate difficulty based on economic parameters (reverse engineering)
                if starting_money <= 400 and starting_lives <= 18:
                    estimated_difficulty = 80  # Hard
                elif starting_money <= 450 and starting_lives <= 19:
                    estimated_difficulty = 60  # Medium-Hard
                elif starting_money >= 550 and starting_lives >= 22:
                    estimated_difficulty = 30  # Easy
                else:
                    estimated_difficulty = 50  # Medium

                difficulty_progression.append(estimated_difficulty)
            else:
                # No config available, assume default
                difficulty_progression.append(50)

        print(f"   📈 Difficulty Progression: {difficulty_progression}")
        return difficulty_progression

    def _analyze_strategic_patterns(self, performances: List[PerformanceData]) -> Dict[str, Any]:
        """Analyze strategic patterns across multiple games"""
        if not performances:
            return {}

        # Analyze tower usage patterns
        all_towers_used = {}
        tower_success_rates = {}
        preferred_strategies = []

        for performance in performances:
            if performance.towers_built:
                for tower_type, count in performance.towers_built.items():
                    if tower_type not in all_towers_used:
                        all_towers_used[tower_type] = []
                        tower_success_rates[tower_type] = []

                    all_towers_used[tower_type].append(count)
                    tower_success_rates[tower_type].append(
                        performance.win_flag)

            # Track most built tower type for strategy identification
            most_built = getattr(performance, 'most_built_tower_type', None)
            if most_built:
                preferred_strategies.append(most_built)

        # Calculate tower effectiveness
        tower_effectiveness = {}
        for tower_type, success_list in tower_success_rates.items():
            if success_list:
                win_rate = sum(success_list) / len(success_list) * 100
                avg_usage = sum(
                    all_towers_used[tower_type]) / len(all_towers_used[tower_type])
                tower_effectiveness[tower_type] = {
                    'win_rate': win_rate,
                    'avg_usage': avg_usage,
                    'games_used': len(success_list)
                }

        # Identify preferred strategy
        strategy_counts = {}
        for strategy in preferred_strategies:
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

        most_preferred_strategy = max(strategy_counts.items(), key=lambda x: x[1])[
            0] if strategy_counts else None

        # Analyze economic patterns
        economic_efficiency_scores = [getattr(
            p, 'economic_efficiency', 0.0) for p in performances if hasattr(p, 'economic_efficiency')]
        resource_management_scores = [getattr(p, 'resource_management_score', 0.0)
                                      for p in performances if hasattr(p, 'resource_management_score')]

        avg_economic_efficiency = sum(economic_efficiency_scores) / len(
            economic_efficiency_scores) if economic_efficiency_scores else 0.0
        avg_resource_management = sum(resource_management_scores) / len(
            resource_management_scores) if resource_management_scores else 0.0

        return {
            'tower_effectiveness': tower_effectiveness,
            'most_preferred_strategy': most_preferred_strategy,
            'strategy_consistency': max(strategy_counts.values()) / len(preferred_strategies) if preferred_strategies else 0.0,
            'economic_patterns': {
                'avg_economic_efficiency': avg_economic_efficiency,
                'avg_resource_management': avg_resource_management,
                'economic_trend': 'improving' if len(economic_efficiency_scores) >= 2 and economic_efficiency_scores[-1] > economic_efficiency_scores[0] else 'stable'
            },
            'tower_diversity_trend': [p.tower_diversity for p in performances],
            'games_analyzed': len(performances)
        }

    def _analyze_performance_trends(self, performances: List[PerformanceData]) -> Dict[str, Any]:
        """Analyze performance trends and identify patterns"""
        if not performances:
            return {}

        scores = [p.score for p in performances]
        win_flags = [p.win_flag for p in performances]
        wave_progression = [p.wave_reached / p.final_wave *
                            100 for p in performances if p.final_wave > 0]

        # Calculate trends
        score_trend = 'stable'
        if len(scores) >= 3:
            recent_avg = sum(scores[:2]) / 2
            older_avg = sum(scores[2:]) / len(scores[2:])
            if recent_avg > older_avg + 15:
                score_trend = 'strongly_improving'
            elif recent_avg > older_avg + 5:
                score_trend = 'improving'
            elif recent_avg < older_avg - 15:
                score_trend = 'strongly_declining'
            elif recent_avg < older_avg - 5:
                score_trend = 'declining'

        # Identify problem areas
        problem_areas = []
        if sum(win_flags) / len(win_flags) < 0.4:
            problem_areas.append('low_win_rate')
        if sum(scores) / len(scores) < 40:
            problem_areas.append('low_scores')
        if len(wave_progression) > 0 and sum(wave_progression) / len(wave_progression) < 50:
            problem_areas.append('early_defeats')

        # Identify strengths
        strengths = []
        if sum(win_flags) / len(win_flags) > 0.7:
            strengths.append('high_win_rate')
        if sum(scores) / len(scores) > 70:
            strengths.append('high_scores')
        if len(wave_progression) > 0 and sum(wave_progression) / len(wave_progression) > 80:
            strengths.append('strong_progression')

        return {
            'score_trend': score_trend,
            'win_rate': sum(win_flags) / len(win_flags) * 100,
            'avg_score': sum(scores) / len(scores),
            'avg_wave_progression': sum(wave_progression) / len(wave_progression) if wave_progression else 0,
            'problem_areas': problem_areas,
            'strengths': strengths,
            # Higher is more consistent
            'consistency': 1.0 - (max(scores) - min(scores)) / 100.0 if scores else 0.0,
            'recent_performance': {
                'last_3_scores': scores[:3],
                'last_3_wins': win_flags[:3],
                'improvement_rate': (scores[0] - scores[-1]) / len(scores) if len(scores) > 1 else 0
            }
        }


def main():
    """Interactive adaptive config generator"""
    print("=== AI-Powered Adaptive Configuration Generator (Azure OpenAI) ===")
    print("This system generates configurations based on your previous level performance.")

    # Check for direct API key parameter
    api_key = None
    if len(os.sys.argv) > 1:
        api_key = os.sys.argv[1]
        print(f"Using API key from command line parameter")

    # If no API key provided, prompt for it
    if not api_key and not os.getenv('AZURE_OPENAI_API_KEY') and not os.getenv('OPENAI_API_KEY'):
        print("\n🔑 No API key found in environment variables.")
        print("You can either:")
        print("1. Set AZURE_OPENAI_API_KEY environment variable")
        print("2. Enter key manually (it will use a hardcoded fallback if empty)")
        api_key = input(
            "\nEnter your Azure OpenAI API key (or press Enter to use fallback): ").strip()
        if not api_key:
            print("Using hardcoded fallback API key...")
            api_key = None  # Will trigger fallback in constructor
        return

    # Ask about AI mode preference first
    print("\n🤖 Choose AI Mode:")
    print("1. Standard AI Mode - AI analyzes performance and adjusts existing maps")
    print("2. Full AI Mode - AI designs complete custom maps from scratch")
    print("   (Full AI mode lets AI create entire terrain layouts, paths, and all config)")

    while True:
        ai_mode = input("\nSelect AI mode (1-2): ").strip()
        if ai_mode in ['1', '2']:
            break
        print("❌ Please select 1 or 2")

    use_full_ai = (ai_mode == '2')

    try:
        generator = AdaptiveConfigGenerator(api_key, use_full_ai=use_full_ai)

        mode_text = "Full AI" if use_full_ai else "Standard AI"
        print(f"\n✅ {mode_text} mode activated!")

        while True:
            print("\n============================================================")
            print("Choose analysis method:")
            print("1. Multi-game analysis - Analyze up to 5 recent games (recommended)")
            print("2. Single game analysis - Load from specific performance file")
            print("3. Manual input - Enter performance data manually")
            print("4. Switch AI mode")
            print("5. Quit")

            choice = input("\nSelect option (1-5): ").strip()

            if choice == '5' or choice.lower() in ['quit', 'q', 'exit']:
                print("Goodbye!")
                break

            elif choice == '4':
                print("\n🤖 Switch AI Mode:")
                print(
                    "1. Standard AI Mode - AI analyzes performance and adjusts existing maps")
                print("2. Full AI Mode - AI designs complete custom maps from scratch")

                while True:
                    new_mode = input("\nSelect new AI mode (1-2): ").strip()
                    if new_mode in ['1', '2']:
                        break
                    print("❌ Please select 1 or 2")

                new_use_full_ai = (new_mode == '2')
                if new_use_full_ai != use_full_ai:
                    use_full_ai = new_use_full_ai
                    generator = AdaptiveConfigGenerator(
                        api_key, use_full_ai=use_full_ai)
                    mode_text = "Full AI" if use_full_ai else "Standard AI"
                    print(f"\n✅ Switched to {mode_text} mode!")
                else:
                    print(
                        f"\n✅ Already using {'Full AI' if use_full_ai else 'Standard AI'} mode")
                continue

            # Generate output path with automatic sequential numbering
            config_dir = os.path.join(os.path.dirname(
                os.path.dirname(os.path.abspath(__file__))), 'config')
            os.makedirs(config_dir, exist_ok=True)
            output_path = None  # Let the generator decide

            if choice == '1':
                print("\n🎯 Multi-Game Analysis Mode")
                print(
                    "This analyzes your last 1-5 games to create an optimally balanced challenge.")

                mode_text = "[FULL AI]" if use_full_ai else "[STANDARD AI]"
                print(
                    f"\n🤖 {mode_text} Generating multi-game adaptive configuration...")
                adaptive_config = generator.generate_config_from_recent_games(
                    output_path)

                if adaptive_config:
                    print(
                        "\n✅ Multi-game adaptive configuration generated successfully!")
                    print(
                        f"   Saved to: {os.path.basename(adaptive_config.get('_saved_to', 'unknown'))}")
                    if '_adaptive_metadata' in adaptive_config:
                        meta = adaptive_config['_adaptive_metadata']
                        if 'recent_performance_summary' in meta:
                            summary = meta['recent_performance_summary']
                            print(
                                f"   Games Analyzed: {summary.get('games_analyzed', 'unknown')}")
                            print(
                                f"   Average Score: {summary.get('average_score', 0):.1f}/100")
                            print(
                                f"   Win Rate: {summary.get('win_rate', 0):.1f}%")
                            print(
                                f"   Trend: {summary.get('trend', 'unknown')}")
                        if 'ai_adjustments' in meta:
                            difficulty = meta['ai_adjustments'].get(
                                'difficulty_adjustment', {}).get('new_difficulty', 'unknown')
                            print(f"   New Difficulty: {difficulty}")
                            reasoning = meta['ai_adjustments'].get(
                                'reasoning', 'No reasoning provided')
                            print(f"   AI Reasoning: {reasoning}")

            elif choice == '2':
                print("\n📁 Single Game Analysis Mode")

                files = list_performance_files()
                if not files:
                    print("❌ No performance files found! Play some games first.")
                    continue

                print("Available performance files:")
                for i, filename in enumerate(files, 1):
                    print(f"   {i}. {filename}")

                while True:
                    try:
                        file_choice = int(
                            input(f"\nSelect file (1-{len(files)}): "))
                        if 1 <= file_choice <= len(files):
                            selected_file = files[file_choice - 1]
                            break
                        else:
                            print(
                                f"Please select a number between 1 and {len(files)}")
                    except ValueError:
                        print("Please enter a valid number")

                performance = load_performance_from_file(selected_file)
                if performance:
                    print(f"\n📈 Performance loaded from: {selected_file}")
                    print(performance.get_performance_summary())

                    mode_text = "[FULL AI]" if use_full_ai else "[STANDARD AI]"
                    print(
                        f"\n🤖 {mode_text} Generating single-game adaptive configuration...")
                    adaptive_config = generator.generate_adaptive_config(
                        performance, output_path)

                    if adaptive_config:
                        print(
                            "\n✅ Single-game adaptive configuration generated successfully!")
                else:
                    print("❌ Failed to load performance data")

            elif choice == '3':
                print("\n✏️ Manual Input Mode")
                performance = get_manual_performance_input()
                print("\n📋 Manual Performance Data:")
                print(performance.get_performance_summary())

                mode_text = "[FULL AI]" if use_full_ai else "[STANDARD AI]"
                print(
                    f"\n🤖 {mode_text} Generating manual-input adaptive configuration...")
                adaptive_config = generator.generate_adaptive_config(
                    performance, output_path)

                if adaptive_config:
                    print(
                        "\n✅ Manual-input adaptive configuration generated successfully!")

            else:
                print("❌ Invalid choice. Please select 1, 2, 3, 4, or 5.")
                continue

    except KeyboardInterrupt:
        print("\n\nGoodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")


if __name__ == "__main__":
    main()
