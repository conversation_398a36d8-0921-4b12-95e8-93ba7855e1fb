# Tower Defense Game Configuration

This directory contains the configuration system for the Tower Defense Game.

## Architecture Overview

The configuration system has been separated into **static** and **dynamic** components:

- **Static configs**: Game mechanics that never change (tower costs, counter strategies)
- **Dynamic configs**: Level-specific content generated by AI (waves, maps, difficulty)

## Configuration Files

### Static Configuration Files (Never Change)

#### `static_tower_config.json`
Contains tower costs, progression rules, and placement requirements:
- `base_costs`: Starting cost for each tower type
- `cost_progression`: How costs scale with wave progression 
- `dynamic_cost_increase`: Usage-based cost scaling
- `tower_descriptions`: Tower descriptions and mechanics
- `size_requirements`: Placement rules (single cell, 2x2, water-only, etc.)

#### `static_balance_config.json` 
Contains game balance mechanics and counter strategies:
- `currency`: Money generation rules
- `counter_system`: Tower vs enemy damage multipliers
- `immunity`: Enemy immunity system mechanics
- `freeze`: Freeze and slow effect rules
- `terrain_effects`: How terrain affects gameplay
- `special_mechanics`: Rock removal costs, boss scaling, etc.

### Dynamic Configuration Files (AI Generated)

#### `tower_defense_game.json` (and other AI-generated configs)
Contains level-specific content that changes between games:

#### Wave Configuration (`wave_config`)
Controls enemy spawning, wave progression, and enemy scaling:

- **spawn_config**: Base spawning parameters
  - `base_enemy_count`: Starting number of enemies per wave
  - `base_spawn_delay`: Initial delay between enemy spawns (frames)
  - `min_spawn_delay`: Minimum spawn delay allowed
  - `boss_enemy_count`: Number of boss enemies per boss wave

- **round_progression**: Wave difficulty scaling
  - `enemy_increase_per_round`: How many more enemies spawn per wave range
  - `spawn_delay_reduction_per_round`: How much faster enemies spawn per wave range
  - `special_rounds`: Special wave multipliers (waves 10, 20, 30, etc.)

- **wave_compositions**: Enemy type distributions by wave ranges
- **boss_waves**: Which waves spawn boss enemies
- **enemy_scaling**: How enemy stats scale with wave number
- **money_config**: Currency rewards for completing waves

#### Map Configuration (`map_config`)
Defines the game map layout:

- **default_map**: The main game map
  - `width`/`height`: Grid dimensions
  - `terrain`: 2D array defining terrain types (0=GRASS, 1=PATH, 2=ROCK, etc.)
  - `path`: Array of coordinates defining the enemy path

## Benefits of This Architecture

1. **AI Focus**: AI can focus on generating strategic content (enemy compositions, maps) without duplicating static mechanics
2. **Consistency**: Game mechanics stay consistent across all levels
3. **Maintainability**: Static mechanics can be balanced independently of AI-generated content
4. **File Size**: AI-generated configs are much smaller and cleaner
5. **Performance**: Static data is loaded once and cached

## How It Works

When the game loads:
1. `config/game_config.py` loads static data from `static_*.json` files
2. Dynamic data is loaded from the current AI-generated config
3. Systems like `get_tower_config()` and `get_balance_config()` return static data
4. Wave and map data comes from the dynamic config

## Legacy Compatibility

Older config files that contain static data will still work via fallback mechanisms in the config loader.
- **immunity**: Enemy immunity system parameters
- **freeze**: Freeze effect mechanics

### `game_config.py`
Python loader that reads the JSON configuration and provides access functions:

- `get_wave_config()`: Returns wave configuration
- `get_map_config()`: Returns all map configurations
- `get_tower_config()`: Returns tower configuration
- `get_balance_config()`: Returns balance configuration
- `get_available_maps()`: Returns list of available map names

## Usage

The configuration system is used throughout the game:

```python
from config.game_config import get_wave_config, get_tower_config

# Get wave settings
wave_config = get_wave_config()
spawn_delay = wave_config['spawn_config']['base_spawn_delay']

# Get tower costs
tower_config = get_tower_config()
basic_tower_cost = tower_config['base_costs']['BasicTower']
```

## Benefits

- **Human Readable**: JSON format is easy to edit without programming knowledge
- **Centralized**: All game parameters in one file
- **Version Control Friendly**: Changes are easy to track in git
- **Easy Balancing**: Modify difficulty by editing JSON values
- **Maintainable**: Clear separation of configuration from code

## Modifying the Game

To adjust game difficulty or behavior:

1. Edit `tower_defense_game.json` with your desired values
2. Save the file
3. Restart the game to apply changes

The configuration is loaded once at game startup and cached for performance. 