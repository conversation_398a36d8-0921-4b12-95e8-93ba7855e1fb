import random
import pygame
from typing import List, Tuple

class RockRemovalRewards:
    """Handles rewards and effects when rocks are removed - WAVE BASED SYSTEM"""
    
    def __init__(self):
        # Wave-based reward system
        self.rocks_removed_this_wave = 0
        self.total_rocks_removed = 0
        
        # Wave-based reward milestones (rocks removed per wave)
        self.money_bonus_milestone = 1     # Money bonus at 1 rock removed
        self.tower_boost_milestone = 2     # Tower boost at 2 rocks removed
        self.enemy_boost_milestone = 3     # Enemy boost at 3 rocks removed
        self.health_immunity_milestone = 4    # Health immunity at 4 rocks removed
        self.freeze_enemies_milestone = 5  # Freeze enemies at 5 rocks removed
        self.super_speed_milestone = 6     # Super speed at 6 rocks removed
        self.health_gain_milestone = 7    # Health gain at 7 rocks removed
        
        # Money bonus system - reduced by 25%
        self.money_bonus_amount = 15000  # Reduced from 20000 to 15000
        
        # Tower boost system - reduced effectiveness and duration
        self.tower_boost_active = False
        self.tower_boost_timer = 0
        self.tower_boost_duration = 2700  # Reduced from 3600 to 2700 (45 seconds)
        self.tower_boost_multiplier = 1.375  # Reduced from 1.5 to 1.375 (37.5% increase)
        
        # Enemy boost system - reduced effectiveness and duration
        self.enemy_boost_active = False
        self.enemy_boost_timer = 0
        self.enemy_boost_duration = 1350  # Reduced from 1800 to 1350 (22.5 seconds)
        self.enemy_boost_multiplier = 1.225  # Reduced from 1.3 to 1.225 (22.5% increase)
        
        # Player health immunity system - replaces tower shield boost
        self.health_immunity_active = False
        self.health_immunity_timer = 0
        self.health_immunity_duration = 675  # 11.25 seconds (same as old shield duration)
        
        # Freeze enemies system - reduced duration
        self.freeze_enemies_active = False
        self.freeze_enemies_timer = 0
        self.freeze_enemies_duration = 450  # Reduced from 600 to 450 (7.5 seconds)
        
        # Super speed system - reduced duration
        self.super_speed_active = False
        self.super_speed_timer = 0
        self.super_speed_duration = 900  # Reduced from 1200 to 900 (15 seconds)
        self.super_speed_multiplier = 2.0  # Keep same multiplier
        
        # Health gain system - reduced amount
        self.health_gain_amount = 2  # Reduced from 3 to 2 lives
        
        # Testing and debug features
        self.debug_mode = False
        self.test_mode = False
        self.force_reward_type = None  # 'money_bonus', 'tower_boost', 'enemy_boost', 'shield_boost', 'freeze_enemies', 'super_speed', 'health_gain'
        
        # In-game message system
        self.messages = []
        self.message_duration = 180  # 3 seconds at 60 FPS
        self.message_font = None
        self.message_y_offset = 0
    
    def enable_debug_mode(self):
        """Enable debug mode for testing"""
        self.debug_mode = True
        print("🔧 Rock Removal Rewards Debug Mode ENABLED")
        print("   Modified system:")
        print("   - 75% chance of nothing happening")
        print("   - When effects do trigger, they are 25% weaker/shorter")
        print("   - Money bonus: $15,000 (was $20,000)")
        print("   - Tower boost: 37.5% damage for 45s (was 50% for 60s)")
        print("   - Enemy boost: 22.5% speed for 22.5s (was 30% for 30s)")
        print("   - Health immunity: 11.25s duration (was tower shield for 15s)")
        print("   - Freeze enemies: 7.5s duration (was 10s)")
        print("   - Super speed: 15s duration (was 20s)")
        print("   - Health gain: 2 lives (was 3 lives)")
    
    def disable_debug_mode(self):
        """Disable debug mode"""
        self.debug_mode = False
        self.test_mode = False
        self.force_reward_type = None
        print("🔧 Rock Removal Rewards Debug Mode DISABLED")
    
    def force_reward(self, reward_type: str):
        """Force a specific reward type for testing"""
        if not self.debug_mode:
            return
        self.test_mode = True
        self.force_reward_type = reward_type
        print(f"🧪 Test Mode: Forcing {reward_type} reward")
    
    def disable_test_mode(self):
        """Disable test mode"""
        self.test_mode = False
        self.force_reward_type = None
        print("🧪 Test Mode DISABLED")
    
    def add_message(self, text: str, color: Tuple[int, int, int] = (255, 255, 255)):
        """Add a message to display on screen"""
        self.messages.append({
            'text': text,
            'color': color,
            'timer': self.message_duration,
            'y_offset': self.message_y_offset
        })
        self.message_y_offset += 30
        if self.message_y_offset > 150:
            self.message_y_offset = 0
    
    def increment_rock_removed(self, wave_manager, towers, enemies, game_instance=None):
        self.rocks_removed_this_wave += 1
        self.total_rocks_removed += 1
        self.add_message(f"💎 Rock {self.rocks_removed_this_wave} removed this wave", (150, 150, 150))
        # Try to trigger a random effect
        self.try_random_effect(wave_manager, towers, enemies, game_instance)
    
    def try_random_effect(self, wave_manager, towers, enemies, game_instance=None):
        # 75% chance of nothing happening
        if random.random() < 0.75:
            self.add_message("💎 Nothing happened this time", (128, 128, 128))
            return
        
        # List of possible effects and their weights (probabilities) - scaled down by 25%
        effects = [
            ('money_bonus', 0.20),
            ('tower_boost', 0.15),
            ('enemy_boost', 0.15),
            ('health_immunity', 0.15),
            ('freeze_enemies', 0.15),
            ('super_speed', 0.10),
            ('health_gain', 0.10)
        ]
        # Normalize weights
        total = sum(w for _, w in effects)
        choices, weights = zip(*effects)
        weights = [w / total for w in weights]
        chosen = random.choices(choices, weights)[0]
        # Activate the chosen effect
        self.check_wave_rewards(wave_manager, towers, enemies, game_instance, force_effect=chosen)
    
    def check_wave_rewards(self, wave_manager, towers: List, enemies: List, game_instance=None, force_effect=None) -> dict:
        rewards_activated = {
            'money_bonus': False,
            'tower_boost': False,
            'enemy_boost': False,
            'health_immunity': False,
            'freeze_enemies': False,
            'super_speed': False,
            'health_gain': False
        }
        # If force_effect is set, only try to activate that effect
        if force_effect:
            effect = force_effect
            if effect == 'money_bonus':
                money_gained = self.activate_money_bonus(game_instance)
                rewards_activated['money_bonus'] = True
                self.add_message(f"💰 MONEY BONUS! +${money_gained}", (255, 215, 0))
            elif effect == 'tower_boost':
                self.activate_tower_boost(towers)
                rewards_activated['tower_boost'] = True
                self.add_message(f"⚡ TOWER BOOST! +{int((self.tower_boost_multiplier - 1) * 100)}% damage for {self.tower_boost_duration // 60}s", (255, 255, 0))
            elif effect == 'enemy_boost':
                self.activate_enemy_boost(enemies)
                rewards_activated['enemy_boost'] = True
                self.add_message(f"🏃 ENEMY BOOST! +{int((self.enemy_boost_multiplier - 1) * 100)}% speed for {self.enemy_boost_duration // 60}s", (255, 100, 100))
            elif effect == 'health_immunity':
                self.activate_health_immunity(game_instance)
                rewards_activated['health_immunity'] = True
                self.add_message(f"🛡️ HEALTH IMMUNITY! Player immune to damage for {self.health_immunity_duration // 60}s", (0, 255, 255))
            elif effect == 'freeze_enemies':
                self.activate_freeze_enemies(enemies)
                rewards_activated['freeze_enemies'] = True
                self.add_message(f"❄️ FREEZE ENEMIES! Enemies frozen for {self.freeze_enemies_duration // 60}s", (173, 216, 230))
            elif effect == 'super_speed':
                self.activate_super_speed(game_instance)
                rewards_activated['super_speed'] = True
                self.add_message(f"🚀 SUPER SPEED! {int(self.super_speed_multiplier)}x speed for {self.super_speed_duration // 60}s", (255, 0, 255))
            elif effect == 'health_gain':
                gained = self.activate_health_gain(game_instance)
                rewards_activated['health_gain'] = True
                self.add_message(f"🧡 HEALTH GAIN! +{gained} lives", (255, 100, 200))
            return rewards_activated
        # If not forced, do nothing (no milestone logic)
        return rewards_activated
    
    def activate_money_bonus(self, game_instance) -> int:
        """Activate money bonus reward"""
        # if game_instance is not None:
        #     game_instance.trigger_screenshake(intensity=12, duration=18)
        if game_instance is None:
            return 0
        
        # Calculate money bonus based on current wave (scales with difficulty)
        wave_number = getattr(game_instance.wave_manager, 'wave_number', 1)
        money_bonus = self.money_bonus_amount + (wave_number * 10)  # Base + 10 per wave
        
        # Add money to game
        game_instance.money += money_bonus
        game_instance.total_money_earned += money_bonus
        
        print(f"MONEY BONUS ACTIVATED! +${money_bonus}")
        return money_bonus
    
    def activate_tower_boost(self, towers: List):
        """Activate tower damage boost for all towers"""
        # if hasattr(self, 'game_instance') and self.game_instance is not None:
        #     self.game_instance.trigger_screenshake(intensity=12, duration=18)
        self.tower_boost_active = True
        self.tower_boost_timer = self.tower_boost_duration
        for tower in towers:
            if hasattr(tower, 'apply_damage_boost'):
                tower.apply_damage_boost(self.tower_boost_multiplier)
        print(f"TOWER BOOST ACTIVATED! +{int((self.tower_boost_multiplier - 1) * 100)}% damage for {self.tower_boost_duration // 60} seconds")
    
    def activate_enemy_boost(self, enemies: List):
        """Activate enemy speed boost for all enemies"""
        # if hasattr(self, 'game_instance') and self.game_instance is not None:
        #     self.game_instance.trigger_screenshake(intensity=12, duration=18)
        self.enemy_boost_active = True
        self.enemy_boost_timer = self.enemy_boost_duration
        for enemy in enemies:
            if hasattr(enemy, 'apply_speed_boost'):
                enemy.apply_speed_boost(self.enemy_boost_multiplier)
        print(f"ENEMY BOOST ACTIVATED! +{int((self.enemy_boost_multiplier - 1) * 100)}% speed for {self.enemy_boost_duration // 60} seconds")
    
    def activate_health_immunity(self, game_instance):
        """Activate health immunity for the player"""
        # if game_instance is not None:
        #     game_instance.trigger_screenshake(intensity=12, duration=18)
        self.health_immunity_active = True
        self.health_immunity_timer = self.health_immunity_duration
        if game_instance and hasattr(game_instance, 'activate_health_immunity'):
            game_instance.activate_health_immunity(self.health_immunity_duration)
        print(f"HEALTH IMMUNITY ACTIVATED! Player immune to damage for {self.health_immunity_duration // 60} seconds")
    
    def activate_freeze_enemies(self, enemies: List):
        """Activate freeze enemies effect"""
        # if hasattr(self, 'game_instance') and self.game_instance is not None:
        #     self.game_instance.trigger_screenshake(intensity=12, duration=18)
        self.freeze_enemies_active = True
        self.freeze_enemies_timer = self.freeze_enemies_duration
        for enemy in enemies:
            if hasattr(enemy, 'apply_freeze'):
                enemy.apply_freeze(self.freeze_enemies_duration)
        print(f"FREEZE ENEMIES ACTIVATED! Enemies frozen for {self.freeze_enemies_duration // 60} seconds")
    
    def activate_super_speed(self, game_instance):
        """Activate super speed effect"""
        # if game_instance is not None:
        #     game_instance.trigger_screenshake(intensity=12, duration=18)
        self.super_speed_active = True
        self.super_speed_timer = self.super_speed_duration
        if game_instance and hasattr(game_instance, 'apply_super_speed'):
            game_instance.apply_super_speed(self.super_speed_multiplier)
        print(f"SUPER SPEED ACTIVATED! {int(self.super_speed_multiplier)}x speed for {self.super_speed_duration // 60} seconds")
    
    def activate_health_gain(self, game_instance) -> int:
        # if game_instance is not None:
        #     game_instance.trigger_screenshake(intensity=12, duration=18)
        if game_instance is None:
            return 0
        game_instance.lives += self.health_gain_amount
        print(f"HEALTH GAIN ACTIVATED! +{self.health_gain_amount} lives")
        return self.health_gain_amount
    
    def update_effects(self, game_speed: float, towers: List, enemies: List, game_instance=None):
        """Update rock removal effects and timers"""
        if self.tower_boost_active:
            self.tower_boost_timer -= game_speed
            if self.tower_boost_timer <= 0:
                self.deactivate_tower_boost(towers)
        
        if self.enemy_boost_active:
            self.enemy_boost_timer -= game_speed
            if self.enemy_boost_timer <= 0:
                self.deactivate_enemy_boost(enemies)
        
        if self.health_immunity_active:
            self.health_immunity_timer -= game_speed
            if self.health_immunity_timer <= 0:
                self.deactivate_health_immunity(game_instance)
        
        if self.freeze_enemies_active:
            self.freeze_enemies_timer -= game_speed
            if self.freeze_enemies_timer <= 0:
                self.deactivate_freeze_enemies(enemies)
        
        if self.super_speed_active:
            self.super_speed_timer -= game_speed
            if self.super_speed_timer <= 0:
                self.deactivate_super_speed(game_instance)
        
        for message in self.messages[:]:
            message['timer'] -= game_speed
            if message['timer'] <= 0:
                self.messages.remove(message)
    
    def deactivate_tower_boost(self, towers: List):
        """Deactivate tower damage boost"""
        self.tower_boost_active = False
        self.tower_boost_timer = 0
        for tower in towers:
            if hasattr(tower, 'remove_damage_boost'):
                tower.remove_damage_boost()
        print("Tower boost deactivated")
        self.add_message("⚡ Tower Boost ended", (200, 200, 200))
    
    def deactivate_enemy_boost(self, enemies: List):
        """Deactivate enemy speed boost"""
        self.enemy_boost_active = False
        self.enemy_boost_timer = 0
        for enemy in enemies:
            if hasattr(enemy, 'remove_speed_boost'):
                enemy.remove_speed_boost()
        print("Enemy boost deactivated")
        self.add_message("🏃 Enemy Boost ended", (200, 200, 200))
    
    def deactivate_health_immunity(self, game_instance):
        """Deactivate health immunity"""
        self.health_immunity_active = False
        self.health_immunity_timer = 0
        if game_instance and hasattr(game_instance, 'remove_health_immunity'):
            game_instance.remove_health_immunity()
        print("Health immunity deactivated")
        self.add_message("🛡️ Health immunity ended", (200, 200, 200))
    
    def deactivate_freeze_enemies(self, enemies: List):
        """Deactivate freeze enemies effect"""
        self.freeze_enemies_active = False
        self.freeze_enemies_timer = 0
        for enemy in enemies:
            if hasattr(enemy, 'remove_freeze'):
                enemy.remove_freeze()
        print("Freeze enemies deactivated")
        self.add_message("❄️ Freeze ended", (200, 200, 200))
    
    def deactivate_super_speed(self, game_instance):
        """Deactivate super speed effect"""
        self.super_speed_active = False
        self.super_speed_timer = 0
        if game_instance and hasattr(game_instance, 'remove_super_speed'):
            game_instance.remove_super_speed()
        print("Super speed deactivated")
        self.add_message("🚀 Super Speed ended", (200, 200, 200))
    
    def apply_boost_to_new_tower(self, tower):
        """Apply current boosts to a newly created tower"""
        if self.tower_boost_active and hasattr(tower, 'apply_damage_boost'):
            tower.apply_damage_boost(self.tower_boost_multiplier)
        # Health immunity is now player-based, not tower-based
    
    def apply_boost_to_new_enemy(self, enemy):
        """Apply current boosts to a newly spawned enemy"""
        if self.enemy_boost_active and hasattr(enemy, 'apply_speed_boost'):
            enemy.apply_speed_boost(self.enemy_boost_multiplier)
        if self.freeze_enemies_active and hasattr(enemy, 'apply_freeze'):
            enemy.apply_freeze(self.freeze_enemies_duration)
    
    def reset_wave_counter(self):
        """Reset wave-based counters when new wave starts"""
        self.rocks_removed_this_wave = 0
        print(f"🔄 New wave started - rock removal counters reset")
    
    def draw_effects(self, screen: pygame.Surface):
        """Draw visual indicators for rock removal effects"""
        if self.message_font is None:
            self.message_font = pygame.font.Font(None, 24)
        
        # Draw wave progress indicator
        progress_text = f"Rocks this wave: {self.rocks_removed_this_wave}"
        text = self.message_font.render(progress_text, True, (255, 255, 255))
        screen.blit(text, (10, 120))
        
        # Draw active boost indicators
        active_y_pos = 140 + 10
        
        if self.tower_boost_active:
            self.draw_boost_indicator(screen, "TOWER BOOST", (255, 255, 0), 10, active_y_pos)
            active_y_pos += 30
        
        if self.enemy_boost_active:
            self.draw_boost_indicator(screen, "ENEMY BOOST", (255, 100, 100), 10, active_y_pos)
            active_y_pos += 30
        
        if self.health_immunity_active:
            self.draw_boost_indicator(screen, "HEALTH IMMUNITY", (0, 255, 0), 10, active_y_pos)
            active_y_pos += 30
        
        if self.freeze_enemies_active:
            self.draw_boost_indicator(screen, "FREEZE ENEMIES", (173, 216, 230), 10, active_y_pos)
            active_y_pos += 30
        
        if self.super_speed_active:
            self.draw_boost_indicator(screen, "SUPER SPEED", (255, 0, 255), 10, active_y_pos)
            active_y_pos += 30
        
        # Draw debug mode indicator
        if self.debug_mode:
            debug_text = self.message_font.render("DEBUG MODE ACTIVE", True, (255, 0, 255))
            screen.blit(debug_text, (10, active_y_pos))
        
        self.draw_messages(screen)
    
    def draw_boost_indicator(self, screen: pygame.Surface, text: str, color: Tuple[int, int, int], x: int, y: int):
        """Draw a boost indicator with timer"""
        if self.message_font is None:
            self.message_font = pygame.font.Font(None, 24)
        
        # Get the appropriate timer based on the effect type
        if "TOWER" in text:
            timer = self.tower_boost_timer
        elif "ENEMY" in text:
            timer = self.enemy_boost_timer
        elif "HEALTH" in text:
            timer = self.health_immunity_timer
        elif "FREEZE" in text:
            timer = self.freeze_enemies_timer
        elif "SUPER" in text:
            timer = self.super_speed_timer
        else:
            timer = 0
        
        seconds = int(timer // 60)
        display_text = f"{text}: {seconds}s"
        text_surface = self.message_font.render(display_text, True, color)
        screen.blit(text_surface, (x, y))
    
    def draw_messages(self, screen: pygame.Surface):
        """Draw all active messages"""
        if self.message_font is None:
            self.message_font = pygame.font.Font(None, 24)
        
        for message in self.messages:
            alpha = min(255, int((message['timer'] / self.message_duration) * 255))
            text_surface = self.message_font.render(message['text'], True, message['color'])
            text_rect = text_surface.get_rect()
            text_rect.centerx = screen.get_width() // 2
            text_rect.bottom = screen.get_height() - 100 - message['y_offset']
            
            bg_rect = text_rect.inflate(20, 10)
            bg_surface = pygame.Surface((bg_rect.width, bg_rect.height))
            bg_surface.set_alpha(alpha // 2)
            bg_surface.fill((0, 0, 0))
            screen.blit(bg_surface, bg_rect)
            screen.blit(text_surface, text_rect)
    
    def get_stats(self) -> dict:
        """Get statistics about rock removal rewards"""
        return {
            'total_rocks_removed': self.total_rocks_removed,
            'rocks_removed_this_wave': self.rocks_removed_this_wave,
            'money_bonus_activated_this_wave': False,
            'tower_boost_active': self.tower_boost_active,
            'tower_boost_activated_this_wave': False,
            'enemy_boost_active': self.enemy_boost_active,
            'enemy_boost_activated_this_wave': False,
            'shield_boost_active': self.health_immunity_active,
            'shield_boost_activated_this_wave': False,
            'freeze_enemies_active': self.freeze_enemies_active,
            'freeze_enemies_activated_this_wave': False,
            'super_speed_active': self.super_speed_active,
            'super_speed_activated_this_wave': False,
            'health_gain_activated_this_wave': False,
            'debug_mode': self.debug_mode,
            'test_mode': self.test_mode
        }
    
    def get_debug_info(self) -> str:
        """Get debug information for testing"""
        stats = self.get_stats()
        return f"""
Rock Removal Rewards Debug Info (Wave-Based):
- Total rocks removed: {stats['total_rocks_removed']}
- Rocks this wave: {stats['rocks_removed_this_wave']}
- Money bonus used this wave: {stats['money_bonus_activated_this_wave']}
- Tower boost active: {stats['tower_boost_active']}
- Tower boost used this wave: {stats['tower_boost_activated_this_wave']}
- Enemy boost active: {stats['enemy_boost_active']}
- Enemy boost used this wave: {stats['enemy_boost_activated_this_wave']}
- Shield boost active: {stats['shield_boost_active']}
- Shield boost used this wave: {stats['shield_boost_activated_this_wave']}
- Freeze enemies active: {stats['freeze_enemies_active']}
- Freeze enemies used this wave: {stats['freeze_enemies_activated_this_wave']}
- Super speed active: {stats['super_speed_active']}
- Super speed used this wave: {stats['super_speed_activated_this_wave']}
- Health gain used this wave: {stats['health_gain_activated_this_wave']}
- Debug mode: {stats['debug_mode']}
- Test mode: {stats['test_mode']}
- Active messages: {len(self.messages)}
- Milestones: Money at {self.money_bonus_milestone}, Tower at {self.tower_boost_milestone}, Enemy at {self.enemy_boost_milestone}, Shield at {self.health_immunity_milestone}, Freeze at {self.freeze_enemies_milestone}, Speed at {self.super_speed_milestone}, Health at {self.health_gain_milestone}
        """.strip() 