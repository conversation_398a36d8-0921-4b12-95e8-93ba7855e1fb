"""
Level Variant Generator
Creates procedural variants of existing levels with different modifiers, restrictions, and challenges
"""

import json
import os
import random
import math
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime

class ModifierType(Enum):
    """Types of level modifiers"""
    TOWER_BAN = "tower_ban"
    TOWER_RESTRICTION = "tower_restriction"
    ECONOMIC = "economic"
    SPEED = "speed"
    ENEMY_MODIFIER = "enemy_modifier"
    TERRAIN = "terrain"
    SPECIAL_RULE = "special_rule"

class ModifierDifficulty(Enum):
    """Difficulty levels for modifiers"""
    EASY = "easy"           # 0.8x - 1.0x difficulty
    NORMAL = "normal"       # 1.0x - 1.2x difficulty
    HARD = "hard"          # 1.2x - 1.5x difficulty
    EXTREME = "extreme"    # 1.5x - 2.0x difficulty

@dataclass
class LevelModifier:
    """Represents a single level modifier"""
    id: str
    name: str
    description: str
    modifier_type: ModifierType
    difficulty: ModifierDifficulty
    difficulty_multiplier: float
    reward_multiplier: float
    fun_factor: int  # 1-10 scale of how fun/interesting this modifier is
    config_changes: Dict[str, Any]
    player_visible: bool = True
    prerequisites: Optional[List[str]] = None  # Other modifiers that must be present
    conflicts: Optional[List[str]] = None      # Modifiers that can't be used together

@dataclass
class LevelVariant:
    """Represents a complete level variant"""
    base_level_name: str
    variant_id: str
    variant_name: str
    description: str
    modifiers: List[LevelModifier]
    total_difficulty_multiplier: float
    total_reward_multiplier: float
    estimated_playtime: str
    config_path: str
    completed: bool = False
    best_score: Optional[float] = None
    play_count: int = 0

class LevelVariantGenerator:
    """Generates procedural variants of existing levels"""
    
    def __init__(self):
        self.modifiers = self._initialize_modifiers()
        self.completion_tracker = self._load_completion_tracker()
        
    def _initialize_modifiers(self) -> Dict[str, LevelModifier]:
        """Initialize all available modifiers that work relative to base level values"""
        modifiers = {}
        
        # === ECONOMIC MODIFIERS (relative to base level's starting money) ===
        modifiers["tight_budget"] = LevelModifier(
            id="tight_budget",
            name="Tight Budget",
            description="Start with 25% less money than usual",
            modifier_type=ModifierType.ECONOMIC,
            difficulty=ModifierDifficulty.NORMAL,
            difficulty_multiplier=1.2,
            reward_multiplier=1.3,
            fun_factor=8,
            config_changes={
                "starting_money_multiplier": 0.75
            }
        )
        
        modifiers["budget_crisis"] = LevelModifier(
            id="budget_crisis",
            name="Budget Crisis",
            description="Start with 50% less money than usual",
            modifier_type=ModifierType.ECONOMIC,
            difficulty=ModifierDifficulty.HARD,
            difficulty_multiplier=1.4,
            reward_multiplier=1.6,
            fun_factor=9,
            config_changes={
                "starting_money_multiplier": 0.5
            }
        )
        
        modifiers["extreme_poverty"] = LevelModifier(
            id="extreme_poverty",
            name="Extreme Poverty",
            description="Start with 75% less money than usual",
            modifier_type=ModifierType.ECONOMIC,
            difficulty=ModifierDifficulty.EXTREME,
            difficulty_multiplier=1.6,
            reward_multiplier=1.9,
            fun_factor=10,
            config_changes={
                "starting_money_multiplier": 0.25
            }
        )
        
        modifiers["expensive_towers"] = LevelModifier(
            id="expensive_towers",
            name="Inflation Crisis",
            description="All towers cost 50% more",
            modifier_type=ModifierType.ECONOMIC,
            difficulty=ModifierDifficulty.HARD,
            difficulty_multiplier=1.3,
            reward_multiplier=1.4,
            fun_factor=7,
            config_changes={
                "tower_cost_multiplier": 1.5
            }
        )
        
        # === LIFE CHALLENGES (relative to base level's starting lives) ===
        modifiers["fragile_defense"] = LevelModifier(
            id="fragile_defense",
            name="Fragile Defense",
            description="Start with 50% fewer lives than usual",
            modifier_type=ModifierType.SPECIAL_RULE,
            difficulty=ModifierDifficulty.NORMAL,
            difficulty_multiplier=1.3,
            reward_multiplier=1.4,
            fun_factor=8,
            config_changes={
                "starting_lives_multiplier": 0.5
            }
        )
        
        modifiers["careful_defense"] = LevelModifier(
            id="careful_defense",
            name="Careful Defense",
            description="Start with 75% fewer lives than usual",
            modifier_type=ModifierType.SPECIAL_RULE,
            difficulty=ModifierDifficulty.HARD,
            difficulty_multiplier=1.5,
            reward_multiplier=1.7,
            fun_factor=9,
            config_changes={
                "starting_lives_multiplier": 0.25
            }
        )
        
        modifiers["perfect_defense"] = LevelModifier(
            id="perfect_defense",
            name="Perfect Defense",
            description="Start with only 1 life - no mistakes allowed!",
            modifier_type=ModifierType.SPECIAL_RULE,
            difficulty=ModifierDifficulty.EXTREME,
            difficulty_multiplier=1.8,
            reward_multiplier=2.2,
            fun_factor=10,
            config_changes={
                "starting_lives_override": 1
            }
        )
        
        # === TOWER RESTRICTION MODIFIERS (relative to typical usage) ===
        modifiers["conservative_defense"] = LevelModifier(
            id="conservative_defense",
            name="Conservative Defense",
            description="Maximum 25 towers allowed (good for most levels)",
            modifier_type=ModifierType.TOWER_RESTRICTION,
            difficulty=ModifierDifficulty.EASY,
            difficulty_multiplier=1.1,
            reward_multiplier=1.2,
            fun_factor=6,
            config_changes={
                "max_towers": 25
            }
        )
        
        modifiers["limited_arsenal"] = LevelModifier(
            id="limited_arsenal",
            name="Limited Arsenal",
            description="Maximum 18 towers allowed",
            modifier_type=ModifierType.TOWER_RESTRICTION,
            difficulty=ModifierDifficulty.NORMAL,
            difficulty_multiplier=1.3,
            reward_multiplier=1.4,
            fun_factor=7,
            config_changes={
                "max_towers": 18
            }
        )
        
        modifiers["minimalist_defense"] = LevelModifier(
            id="minimalist_defense",
            name="Minimalist Defense",
            description="Maximum 12 towers allowed",
            modifier_type=ModifierType.TOWER_RESTRICTION,
            difficulty=ModifierDifficulty.HARD,
            difficulty_multiplier=1.5,
            reward_multiplier=1.7,
            fun_factor=8,
            config_changes={
                "max_towers": 12
            }
        )
        
        modifiers["ultra_minimalist"] = LevelModifier(
            id="ultra_minimalist",
            name="Ultra Minimalist",
            description="Maximum 8 towers allowed",
            modifier_type=ModifierType.TOWER_RESTRICTION,
            difficulty=ModifierDifficulty.EXTREME,
            difficulty_multiplier=1.7,
            reward_multiplier=2.0,
            fun_factor=9,
            config_changes={
                "max_towers": 8
            }
        )
        
        # === SPEED MODIFIERS (relative to base spawn delays) ===
        modifiers["quick_assault"] = LevelModifier(
            id="quick_assault",
            name="Quick Assault",
            description="Enemies spawn 25% faster than usual",
            modifier_type=ModifierType.SPEED,
            difficulty=ModifierDifficulty.NORMAL,
            difficulty_multiplier=1.2,
            reward_multiplier=1.3,
            fun_factor=7,
            config_changes={
                "spawn_speed_multiplier": 1.25
            }
        )
        
        modifiers["rapid_assault"] = LevelModifier(
            id="rapid_assault",
            name="Rapid Assault",
            description="Enemies spawn 50% faster than usual",
            modifier_type=ModifierType.SPEED,
            difficulty=ModifierDifficulty.HARD,
            difficulty_multiplier=1.4,
            reward_multiplier=1.5,
            fun_factor=8,
            config_changes={
                "spawn_speed_multiplier": 1.5
            }
        )
        
        modifiers["blitz_mode"] = LevelModifier(
            id="blitz_mode",
            name="Blitz Mode",
            description="Enemies spawn 75% faster than usual",
            modifier_type=ModifierType.SPEED,
            difficulty=ModifierDifficulty.EXTREME,
            difficulty_multiplier=1.6,
            reward_multiplier=1.8,
            fun_factor=9,
            config_changes={
                "spawn_speed_multiplier": 1.75
            }
        )
        
        # === ENEMY MODIFIERS (relative to base enemy stats) ===
        modifiers["sturdy_enemies"] = LevelModifier(
            id="sturdy_enemies",
            name="Sturdy Enemies",
            description="Enemies have 25% more health than usual",
            modifier_type=ModifierType.ENEMY_MODIFIER,
            difficulty=ModifierDifficulty.NORMAL,
            difficulty_multiplier=1.2,
            reward_multiplier=1.3,
            fun_factor=6,
            config_changes={
                "enemy_health_boost": 0.25
            }
        )
        
        modifiers["tough_enemies"] = LevelModifier(
            id="tough_enemies",
            name="Tough Enemies",
            description="Enemies have 50% more health than usual",
            modifier_type=ModifierType.ENEMY_MODIFIER,
            difficulty=ModifierDifficulty.HARD,
            difficulty_multiplier=1.4,
            reward_multiplier=1.5,
            fun_factor=7,
            config_changes={
                "enemy_health_boost": 0.5
            }
        )
        
        modifiers["swarm_tactics"] = LevelModifier(
            id="swarm_tactics",
            name="Swarm Tactics",
            description="25% more enemies per wave than usual",
            modifier_type=ModifierType.ENEMY_MODIFIER,
            difficulty=ModifierDifficulty.NORMAL,
            difficulty_multiplier=1.3,
            reward_multiplier=1.4,
            fun_factor=8,
            config_changes={
                "enemy_count_multiplier": 1.25
            }
        )
        
        modifiers["overwhelming_horde"] = LevelModifier(
            id="overwhelming_horde",
            name="Overwhelming Horde",
            description="50% more enemies per wave than usual",
            modifier_type=ModifierType.ENEMY_MODIFIER,
            difficulty=ModifierDifficulty.HARD,
            difficulty_multiplier=1.5,
            reward_multiplier=1.6,
            fun_factor=9,
            config_changes={
                "enemy_count_multiplier": 1.5
            }
        )
        
        # === SPECIAL WAVE MODIFIERS (relative to base level waves) ===
        modifiers["boss_gauntlet"] = LevelModifier(
            id="boss_gauntlet",
            name="Boss Gauntlet",
            description="50% more boss waves than usual",
            modifier_type=ModifierType.SPECIAL_RULE,
            difficulty=ModifierDifficulty.HARD,
            difficulty_multiplier=1.4,
            reward_multiplier=1.6,
            fun_factor=9,
            config_changes={
                "boss_wave_multiplier": 1.5
            }
        )
        
        modifiers["enhanced_specials"] = LevelModifier(
            id="enhanced_specials",
            name="Enhanced Specials",
            description="Special rounds have 50% more enemies than usual",
            modifier_type=ModifierType.SPECIAL_RULE,
            difficulty=ModifierDifficulty.NORMAL,
            difficulty_multiplier=1.3,
            reward_multiplier=1.4,
            fun_factor=7,
            config_changes={
                "special_round_boost": 0.5
            }
        )
        
        # === ENDURANCE CHALLENGES (relative to base wave count) ===
        modifiers["extended_siege"] = LevelModifier(
            id="extended_siege",
            name="Extended Siege",
            description="25% more waves to survive than usual",
            modifier_type=ModifierType.SPECIAL_RULE,
            difficulty=ModifierDifficulty.NORMAL,
            difficulty_multiplier=1.3,
            reward_multiplier=1.4,
            fun_factor=8,
            config_changes={
                "wave_count_multiplier": 1.25
            }
        )
        
        modifiers["marathon_defense"] = LevelModifier(
            id="marathon_defense",
            name="Marathon Defense",
            description="50% more waves to survive than usual",
            modifier_type=ModifierType.SPECIAL_RULE,
            difficulty=ModifierDifficulty.HARD,
            difficulty_multiplier=1.5,
            reward_multiplier=1.7,
            fun_factor=9,
            config_changes={
                "wave_count_multiplier": 1.5
            }
        )
        
        return modifiers
    
    def _load_completion_tracker(self) -> Dict[str, Dict[str, Any]]:
        """Load completion tracking data"""
        tracker_path = os.path.join("config", "variant_completion.json")
        if os.path.exists(tracker_path):
            try:
                with open(tracker_path, 'r') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def _save_completion_tracker(self):
        """Save completion tracking data"""
        tracker_path = os.path.join("config", "variant_completion.json")
        os.makedirs(os.path.dirname(tracker_path), exist_ok=True)
        with open(tracker_path, 'w') as f:
            json.dump(self.completion_tracker, f, indent=2)
    
    def get_available_modifiers(self, base_difficulty: int = 50) -> Dict[str, List[LevelModifier]]:
        """Get available modifiers grouped by type, filtered by appropriateness"""
        grouped = {}
        
        for modifier in self.modifiers.values():
            # Filter based on base difficulty
            if base_difficulty < 30 and modifier.difficulty == ModifierDifficulty.EXTREME:
                continue  # Don't offer extreme modifiers for easy levels
            
            modifier_type = modifier.modifier_type.value
            if modifier_type not in grouped:
                grouped[modifier_type] = []
            grouped[modifier_type].append(modifier)
        
        # Sort by fun factor and difficulty
        for modifier_list in grouped.values():
            modifier_list.sort(key=lambda m: (m.fun_factor, -m.difficulty_multiplier), reverse=True)
        
        return grouped
    
    def generate_variant_suggestions(self, base_level_path: str, count: int = 5) -> List[List[LevelModifier]]:
        """Generate suggested modifier combinations for a level"""
        suggestions = []
        
        # Load base config to understand difficulty
        base_difficulty = 50
        try:
            with open(base_level_path, 'r') as f:
                base_config = json.load(f)
            base_difficulty = self._extract_difficulty(base_config)
        except:
            pass
        
        available = list(self.modifiers.values())
        
        # Filter by appropriateness
        available = [m for m in available if self._is_modifier_appropriate(m, base_difficulty)]
        
        # Generate different types of combinations
        for i in range(count):
            if i == 0:
                # Single high-impact modifier
                combo = [random.choice([m for m in available if m.fun_factor >= 8])]
            elif i == 1:
                # Economic challenge
                econ_modifiers = [m for m in available if m.modifier_type == ModifierType.ECONOMIC]
                if econ_modifiers:
                    combo = [random.choice(econ_modifiers)]
                else:
                    combo = [random.choice(available)]
            elif i == 2:
                # Tower restriction combo
                restriction_modifiers = [m for m in available if m.modifier_type == ModifierType.TOWER_RESTRICTION]
                ban_modifiers = [m for m in available if m.modifier_type == ModifierType.TOWER_BAN]
                combo = []
                if restriction_modifiers:
                    combo.append(random.choice(restriction_modifiers))
                if ban_modifiers and len(combo) == 0:
                    combo.append(random.choice(ban_modifiers))
                if not combo:
                    combo = [random.choice(available)]
            else:
                # Random combination
                combo_size = random.choices([1, 2, 3], weights=[0.5, 0.3, 0.2])[0]
                combo = random.sample(available, min(combo_size, len(available)))
            
            # Validate combination doesn't conflict
            combo = self._resolve_conflicts(combo)
            if combo:
                suggestions.append(combo)
        
        return suggestions
    
    def _is_modifier_appropriate(self, modifier: LevelModifier, base_difficulty: int) -> bool:
        """Check if a modifier is appropriate for the base difficulty"""
        if base_difficulty < 20 and modifier.difficulty == ModifierDifficulty.EXTREME:
            return False
        if base_difficulty < 40 and modifier.difficulty_multiplier > 1.5:
            return False
        return True
    
    def _resolve_conflicts(self, modifiers: List[LevelModifier]) -> List[LevelModifier]:
        """Remove conflicting modifiers from a combination"""
        resolved = []
        used_types = set()
        
        for modifier in modifiers:
            # Check type conflicts
            if modifier.modifier_type in used_types:
                continue
            
            # Check explicit conflicts
            if modifier.conflicts:
                has_conflict = any(conf_id in [m.id for m in resolved] for conf_id in modifier.conflicts)
                if has_conflict:
                    continue
            
            resolved.append(modifier)
            used_types.add(modifier.modifier_type)
        
        return resolved
    
    def create_variant(self, base_config_path: str, modifiers: List[LevelModifier], 
                      custom_name: Optional[str] = None) -> LevelVariant:
        """Create a complete level variant with the specified modifiers"""
        
        # Load base config
        with open(base_config_path, 'r') as f:
            base_config = json.load(f)
        
        # Extract base level info
        base_name = self._extract_level_name(base_config, base_config_path)
        base_difficulty = self._extract_difficulty(base_config)
        
        # Generate variant ID and name
        modifier_ids = "_".join([m.id for m in modifiers])
        variant_id = f"{base_name}_{modifier_ids}"
        
        if custom_name:
            variant_name = custom_name
        else:
            variant_name = self._generate_variant_name(base_name, modifiers)
        
        # Calculate total multipliers
        total_difficulty_mult = 1.0
        total_reward_mult = 1.0
        
        for modifier in modifiers:
            total_difficulty_mult *= modifier.difficulty_multiplier
            total_reward_mult *= modifier.reward_multiplier
        
        # Apply modifiers to config
        modified_config = self._apply_modifiers_to_config(base_config, modifiers)
        
        # Update difficulty in config
        new_difficulty = min(100, int(base_difficulty * total_difficulty_mult))
        self._set_config_difficulty(modified_config, new_difficulty)
        
        # Add variant metadata (convert enums to strings for JSON serialization)
        modifier_dicts = []
        for m in modifiers:
            mod_dict = asdict(m)
            mod_dict['modifier_type'] = m.modifier_type.value
            mod_dict['difficulty'] = m.difficulty.value
            modifier_dicts.append(mod_dict)
        
        modified_config['_variant_metadata'] = {
            'base_level': base_name,
            'variant_id': variant_id,
            'variant_name': variant_name,
            'modifiers': modifier_dicts,
            'base_difficulty': base_difficulty,
            'final_difficulty': new_difficulty,
            'difficulty_multiplier': total_difficulty_mult,
            'reward_multiplier': total_reward_mult,
            'generation_timestamp': datetime.now().isoformat(),
            'estimated_playtime': self._estimate_playtime(base_config, modifiers)
        }
        
        # Save variant config to variants directory
        variant_filename = f"{variant_id}.json"
        variants_dir = os.path.join("config", "variants")
        os.makedirs(variants_dir, exist_ok=True)
        variant_path = os.path.join(variants_dir, variant_filename)
        
        with open(variant_path, 'w') as f:
            json.dump(modified_config, f, indent=2)
        
        # Create variant object
        variant = LevelVariant(
            base_level_name=base_name,
            variant_id=variant_id,
            variant_name=variant_name,
            description=self._generate_variant_description(modifiers),
            modifiers=modifiers,
            total_difficulty_multiplier=total_difficulty_mult,
            total_reward_multiplier=total_reward_mult,
            estimated_playtime=modified_config['_variant_metadata']['estimated_playtime'],
            config_path=variant_path,
            completed=self._is_variant_completed(variant_id),
            best_score=self._get_variant_best_score(variant_id),
            play_count=self._get_variant_play_count(variant_id)
        )
        
        return variant
    
    def _extract_level_name(self, config: Dict[str, Any], config_path: str) -> str:
        """Extract a clean level name from config or filename"""
        if 'level_name' in config:
            return config['level_name'].replace(' ', '_').lower()
        
        filename = os.path.basename(config_path).replace('.json', '')
        return filename.replace(' ', '_').lower()
    
    def _extract_difficulty(self, config: Dict[str, Any]) -> int:
        """Extract difficulty from config"""
        if '_generation_metadata' in config and 'difficulty' in config['_generation_metadata']:
            diff = config['_generation_metadata']['difficulty']
            return int(diff) if isinstance(diff, (int, float)) else 50
        elif 'calculated_difficulty' in config:
            calc_diff = config['calculated_difficulty']
            if isinstance(calc_diff, dict):
                diff = calc_diff.get('score', 50)
            else:
                diff = calc_diff
            return int(diff) if isinstance(diff, (int, float)) else 50
        elif 'difficulty' in config:
            diff = config['difficulty']
            if isinstance(diff, dict):
                diff = diff.get('score', diff.get('level', 50))
            return int(diff) if isinstance(diff, (int, float)) else 50
        return 50
    
    def _generate_variant_name(self, base_name: str, modifiers: List[LevelModifier]) -> str:
        """Generate a descriptive name for the variant"""
        if len(modifiers) == 1:
            return f"{base_name.title()}: {modifiers[0].name}"
        elif len(modifiers) == 2:
            return f"{base_name.title()}: {modifiers[0].name} + {modifiers[1].name}"
        else:
            return f"{base_name.title()}: {len(modifiers)} Challenge Combo"
    
    def _generate_variant_description(self, modifiers: List[LevelModifier]) -> str:
        """Generate a description for the variant"""
        descriptions = [m.description for m in modifiers]
        return " | ".join(descriptions)
    
    def _apply_modifiers_to_config(self, config: Dict[str, Any], modifiers: List[LevelModifier]) -> Dict[str, Any]:
        """Apply all modifiers to the base config"""
        modified_config = json.loads(json.dumps(config))  # Deep copy
        
        for modifier in modifiers:
            for key, value in modifier.config_changes.items():
                self._apply_config_change(modified_config, key, value)
        
        return modified_config
    
    def _apply_config_change(self, config: Dict[str, Any], key: str, value: Any):
        """Apply a config change that works relative to the base level's values"""
        
        # Ensure basic config structure exists
        if 'game_config' not in config:
            config['game_config'] = {}
        if 'wave_config' not in config:
            config['wave_config'] = {}
        
        # === RELATIVE MONEY MODIFIERS ===
        if key == "starting_money_multiplier":
            # Apply multiplier to base level's starting money
            base_money = config['game_config'].get('starting_money', 20)
            new_money = max(1, int(base_money * value))  # Ensure at least 1 money
            config['game_config']['starting_money'] = new_money
            
        elif key == "starting_money_override":
            # Override with specific value (used for extreme cases)
            config['game_config']['starting_money'] = value
            
        # === RELATIVE LIFE MODIFIERS ===
        elif key == "starting_lives_multiplier":
            # Apply multiplier to base level's starting lives
            base_lives = config['game_config'].get('starting_lives', 20)
            new_lives = max(1, int(base_lives * value))  # Ensure at least 1 life
            config['game_config']['starting_lives'] = new_lives
            
        elif key == "starting_lives_override":
            # Override with specific value (used for extreme cases like single life)
            config['game_config']['starting_lives'] = value
            
        # === TOWER COST MODIFIERS ===
        elif key == "tower_cost_multiplier":
            # Add tower cost multiplier to game config
            config['game_config']['tower_cost_multiplier'] = value
            
        # === TOWER RESTRICTION MODIFIERS ===
        elif key == "max_towers":
            # Set maximum tower limit
            config['game_config']['max_towers'] = value
            
        # === SPAWN SPEED MODIFIERS ===
        elif key == "spawn_speed_multiplier":
            # Apply multiplier to spawn delays (faster spawn = lower delay)
            if 'spawn_config' not in config['wave_config']:
                config['wave_config']['spawn_config'] = {}
            
            # Apply to base spawn delay
            base_delay = config['wave_config']['spawn_config'].get('base_spawn_delay', 120)
            new_delay = max(0.1, base_delay / value)  # Faster spawn = shorter delay
            config['wave_config']['spawn_config']['base_spawn_delay'] = new_delay
            
            # Apply to minimum spawn delay
            min_delay = config['wave_config']['spawn_config'].get('min_spawn_delay', 0.1)
            new_min_delay = max(0.05, min_delay / value)
            config['wave_config']['spawn_config']['min_spawn_delay'] = new_min_delay
            
        # === ENEMY HEALTH MODIFIERS ===
        elif key == "enemy_health_boost":
            # Add enemy health multiplier to game config
            config['game_config']['enemy_health_multiplier'] = 1 + value
            
        # === ENEMY COUNT MODIFIERS ===
        elif key == "enemy_count_multiplier":
            # Apply multiplier to base enemy count
            if 'spawn_config' not in config['wave_config']:
                config['wave_config']['spawn_config'] = {}
            
            base_count = config['wave_config']['spawn_config'].get('base_enemy_count', 5)
            new_count = max(1, int(base_count * value))
            config['wave_config']['spawn_config']['base_enemy_count'] = new_count
            
        # === BOSS WAVE MODIFIERS ===
        elif key == "boss_wave_multiplier":
            # Add boss wave multiplier to game config
            config['game_config']['boss_wave_multiplier'] = value
            
        # === SPECIAL ROUND MODIFIERS ===
        elif key == "special_round_boost":
            # Modify special rounds to have more enemies
            if 'round_progression' not in config['wave_config']:
                config['wave_config']['round_progression'] = {}
            if 'special_rounds' not in config['wave_config']['round_progression']:
                config['wave_config']['round_progression']['special_rounds'] = {}
            
            # Apply boost to all special rounds
            special_rounds = config['wave_config']['round_progression']['special_rounds']
            for round_num, round_config in special_rounds.items():
                if isinstance(round_config, dict) and 'enemy_multiplier' in round_config:
                    # Increase the enemy multiplier
                    round_config['enemy_multiplier'] *= (1 + value)
                    
        # === WAVE COUNT MODIFIERS ===
        elif key == "wave_count_multiplier":
            # Apply multiplier to total wave count
            base_waves = config['wave_config'].get('total_waves', 80)
            new_waves = max(1, int(base_waves * value))
            config['wave_config']['total_waves'] = new_waves
            
            # Extend wave compositions to cover new waves if needed
            if new_waves > base_waves and 'wave_compositions' in config['wave_config']:
                compositions = config['wave_config']['wave_compositions']
                
                # Find the last wave range and its composition
                last_range = None
                last_composition = None
                
                for wave_range_key, composition in compositions.items():
                    if isinstance(wave_range_key, str) and '-' in wave_range_key:
                        start, end = map(int, wave_range_key.split('-'))
                        if last_range is None or end > last_range[1]:
                            last_range = (start, end)
                            last_composition = composition
                
                # Extend with the last composition pattern
                if last_range and last_composition and last_range[1] < new_waves:
                    extended_range = f"{last_range[1] + 1}-{new_waves}"
                    compositions[extended_range] = last_composition
                    
        else:
            # Unknown config change - log it for debugging
            print(f"Warning: Unknown config change key: {key} = {value}")
            
        # Add variant metadata to track changes
        if '_variant_metadata' not in config:
            config['_variant_metadata'] = {
                'applied_changes': [],
                'difficulty_multiplier': 1.0,
                'reward_multiplier': 1.0
            }
        config['_variant_metadata']['applied_changes'].append(f"{key}: {value}")
    
    def _set_config_difficulty(self, config: Dict[str, Any], difficulty: int):
        """Set the difficulty in the config"""
        if '_variant_metadata' not in config:
            config['_variant_metadata'] = {}
        config['_variant_metadata']['final_difficulty'] = difficulty
    
    def _estimate_playtime(self, config: Dict[str, Any], modifiers: List[LevelModifier]) -> str:
        """Estimate playtime for the variant"""
        base_minutes = 30  # Base estimate
        
        # Adjust based on modifiers
        for modifier in modifiers:
            if modifier.modifier_type == ModifierType.SPEED:
                base_minutes *= 0.7  # Faster games
            elif modifier.modifier_type == ModifierType.TOWER_RESTRICTION:
                base_minutes *= 1.2  # Longer due to difficulty
            elif modifier.difficulty == ModifierDifficulty.EXTREME:
                base_minutes *= 1.3  # Extreme challenges take longer
        
        base_minutes = max(15, min(90, base_minutes))  # Clamp between 15-90 minutes
        
        return f"{int(base_minutes)}-{int(base_minutes * 1.3)} minutes"
    
    def _is_variant_completed(self, variant_id: str) -> bool:
        """Check if a variant has been completed"""
        return self.completion_tracker.get(variant_id, {}).get('completed', False)
    
    def _get_variant_best_score(self, variant_id: str) -> Optional[float]:
        """Get the best score for a variant"""
        return self.completion_tracker.get(variant_id, {}).get('best_score')
    
    def _get_variant_play_count(self, variant_id: str) -> int:
        """Get the play count for a variant"""
        return self.completion_tracker.get(variant_id, {}).get('play_count', 0)
    
    def mark_variant_completed(self, variant_id: str, score: float, reward_earned: int):
        """Mark a variant as completed and update tracking"""
        if variant_id not in self.completion_tracker:
            self.completion_tracker[variant_id] = {}
        
        variant_data = self.completion_tracker[variant_id]
        variant_data['completed'] = True
        variant_data['play_count'] = variant_data.get('play_count', 0) + 1
        variant_data['last_played'] = datetime.now().isoformat()
        variant_data['latest_score'] = score
        
        # Update best score
        if 'best_score' not in variant_data or score > variant_data['best_score']:
            variant_data['best_score'] = score
            variant_data['best_score_date'] = datetime.now().isoformat()
        
        # Track rewards (only awarded once)
        if 'reward_earned' not in variant_data:
            variant_data['reward_earned'] = reward_earned
            variant_data['reward_date'] = datetime.now().isoformat()
        
        self._save_completion_tracker()
    
    def get_reward_for_variant(self, variant: LevelVariant) -> int:
        """Calculate the reward for completing a variant"""
        # Check if already rewarded
        if self._is_variant_completed(variant.variant_id):
            return 0  # No reward for repeat completions
        
        # Base reward calculation
        base_reward = 100  # Base reward amount
        
        # Multiply by difficulty and reward multipliers
        final_reward = int(base_reward * variant.total_reward_multiplier)
        
        # Bonus for multiple modifiers
        if len(variant.modifiers) > 1:
            final_reward = int(final_reward * (1 + 0.2 * (len(variant.modifiers) - 1)))
        
        # Cap rewards
        final_reward = min(1000, max(50, final_reward))
        
        return final_reward
    
    def list_available_base_levels(self) -> List[str]:
        """List all available base levels that can have variants created"""
        config_dir = "config"
        base_levels = []
        
        # Exclude utility files and variants
        excluded_files = {
            'static_tower_config.json', 
            'static_balance_config.json', 
            'variant_completion.json',
            'tower_defense_game.json.backup'
        }
        
        for filename in os.listdir(config_dir):
            if filename.endswith('.json') and filename not in excluded_files:
                # Skip if it looks like a variant (has _metadata indicating it's a variant)
                config_path = os.path.join(config_dir, filename)
                try:
                    with open(config_path, 'r') as f:
                        config_data = json.load(f)
                    
                    # Skip if it's a variant or adaptive config
                    if '_variant_metadata' in config_data or '_adaptive_metadata' in config_data:
                        continue
                    
                    base_levels.append(config_path)
                except:
                    # If we can't read it, skip it
                    continue
        
        return base_levels
    
    def list_variants_for_level(self, base_level_name: str) -> List[LevelVariant]:
        """List all variants for a specific base level"""
        variants_dir = os.path.join("config", "variants")
        variants = []
        
        if os.path.exists(variants_dir):
            for filename in os.listdir(variants_dir):
                if filename.endswith('.json') and filename.startswith(f"{base_level_name}_"):
                    variant_path = os.path.join(variants_dir, filename)
                    try:
                        with open(variant_path, 'r') as f:
                            config = json.load(f)
                        
                        if '_variant_metadata' in config:
                            metadata = config['_variant_metadata']
                            
                            # Reconstruct modifiers
                            modifiers = []
                            for mod_data in metadata.get('modifiers', []):
                                # Convert string enums back to enum objects
                                mod_data_copy = mod_data.copy()
                                if 'modifier_type' in mod_data_copy and isinstance(mod_data_copy['modifier_type'], str):
                                    mod_data_copy['modifier_type'] = ModifierType(mod_data_copy['modifier_type'])
                                if 'difficulty' in mod_data_copy and isinstance(mod_data_copy['difficulty'], str):
                                    mod_data_copy['difficulty'] = ModifierDifficulty(mod_data_copy['difficulty'])
                                
                                # Convert back to LevelModifier object
                                modifier = LevelModifier(**mod_data_copy)
                                modifiers.append(modifier)
                            
                            variant = LevelVariant(
                                base_level_name=metadata['base_level'],
                                variant_id=metadata['variant_id'],
                                variant_name=metadata['variant_name'],
                                description=self._generate_variant_description(modifiers),
                                modifiers=modifiers,
                                total_difficulty_multiplier=metadata['difficulty_multiplier'],
                                total_reward_multiplier=metadata['reward_multiplier'],
                                estimated_playtime=metadata['estimated_playtime'],
                                config_path=variant_path,
                                completed=self._is_variant_completed(metadata['variant_id']),
                                best_score=self._get_variant_best_score(metadata['variant_id']),
                                play_count=self._get_variant_play_count(metadata['variant_id'])
                            )
                            variants.append(variant)
                    except Exception as e:
                        print(f"Error loading variant {filename}: {e}")
        
        return variants 