import pygame
from typing import Dict, List, Optional, Tuple
from .global_upgrade_system import GlobalUpgradeSystem, UpgradeType


class GlobalUpgradeUI:
    """UI system for the global upgrade menu"""

    def __init__(self, screen_width: int, screen_height: int, upgrade_system: GlobalUpgradeSystem):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.upgrade_system = upgrade_system

        # UI state
        self.is_open = False
        self.selected_tower_id = None
        self.mode = 'tower_list'  # 'tower_list' or 'tower_detail' or 'create_tower'
        self.scroll_offset = 0
        self.max_scroll = 0

        # Colors
        self.colors = {
            'background': (20, 20, 40),
            'panel': (40, 40, 60),
            'button': (60, 60, 80),
            'button_hover': (80, 80, 100),
            'button_disabled': (40, 40, 50),
            'text': (255, 255, 255),
            'text_secondary': (200, 200, 200),
            'accent': (100, 150, 255),
            'success': (100, 255, 100),
            'error': (255, 100, 100),
            'border': (80, 80, 120)
        }

        # Fonts
        self.fonts = {
            'title': pygame.font.Font(None, 36),
            'header': pygame.font.Font(None, 28),
            'normal': pygame.font.Font(None, 24),
            'small': pygame.font.Font(None, 20)
        }

        # Layout constants
        self.panel_width = 800
        self.panel_height = 600
        self.panel_x = (screen_width - self.panel_width) // 2
        self.panel_y = (screen_height - self.panel_height) // 2

        self.button_height = 40
        self.button_margin = 10
        self.upgrade_item_height = 80

    def open_menu(self):
        """Open the upgrade menu"""
        self.is_open = True
        self.selected_tower_id = None
        self.mode = 'tower_list'
        self.scroll_offset = 0
        self.calculate_max_scroll()

    def close_menu(self):
        """Close the upgrade menu"""
        self.is_open = False

    def calculate_max_scroll(self):
        """Calculate maximum scroll offset"""
        if self.mode == 'tower_list':
            all_towers = self.get_all_tower_types_with_upgrades()
            # Account for button height + spacing between towers
            total_height = len(all_towers) * (self.button_height + 5)
        else:
            total_height = 4 * self.upgrade_item_height  # 4 upgrade types

        visible_height = self.panel_height - 200  # Account for header and padding
        self.max_scroll = max(0, total_height - visible_height)

    def handle_click(self, mouse_pos: Tuple[int, int], button: int = 1) -> bool:
        """Handle mouse click in the upgrade menu, including scroll buttons
        Args:
            mouse_pos: Mouse position tuple
            button: Mouse button (1=left, 3=right)
        """
        if not self.is_open:
            return False

        # Handle right-click cancellation
        if button == 3:
            if self.mode == 'tower_detail':
                # Go back to tower list
                self.mode = 'tower_list'
                self.selected_tower_id = None
                self.calculate_max_scroll()
                return True
            else:
                # Close the menu
                self.close_menu()
                return True

        x, y = mouse_pos

        # Check if click is outside the panel
        if (x < self.panel_x or x > self.panel_x + self.panel_width or
                y < self.panel_y or y > self.panel_y + self.panel_height):
            self.close_menu()
            return True

        # Check close button
        close_button_rect = pygame.Rect(
            self.panel_x + self.panel_width - 50, self.panel_y + 10, 40, 30)
        if close_button_rect.collidepoint(x, y):
            self.close_menu()
            return True

        # Check back button (if in detail mode)
        if self.mode == 'tower_detail':
            back_button_rect = pygame.Rect(
                self.panel_x + 20, self.panel_y + 60, 80, 30)
            if back_button_rect.collidepoint(x, y):
                self.mode = 'tower_list'
                self.selected_tower_id = None
                self.calculate_max_scroll()
                return True

        if self.mode == 'tower_list':
            # Check scroll buttons first
            SCROLL_STEP = 15  # Increased scroll speed
            if self.max_scroll > 0:
                # Up button
                up_button_rect = pygame.Rect(
                    self.panel_x + self.panel_width - 60, self.panel_y + 140, 40, 30)
                if up_button_rect.collidepoint(x, y) and self.scroll_offset > 0:
                    self.scroll_offset = max(
                        0, self.scroll_offset - SCROLL_STEP)
                    return True
                # Down button
                down_button_rect = pygame.Rect(
                    self.panel_x + self.panel_width - 60, self.panel_y + self.panel_height - 80, 40, 30)
                if down_button_rect.collidepoint(x, y) and self.scroll_offset < self.max_scroll:
                    self.scroll_offset = min(
                        self.max_scroll, self.scroll_offset + SCROLL_STEP)
                    return True
            # Check tower selection
            all_towers = self.get_all_tower_types_with_upgrades()
            tower_y = self.panel_y + 140
            for tower_data in all_towers:
                tower_rect = pygame.Rect(
                    self.panel_x + 20, tower_y - self.scroll_offset, 400, self.button_height)
                if tower_rect.collidepoint(x, y):
                    self.selected_tower_id = tower_data['tower_id']
                    self.mode = 'tower_detail'
                    self.calculate_max_scroll()
                    return True
                tower_y += self.button_height + 5
        elif self.mode == 'tower_detail' and self.selected_tower_id:
            # Use selected_tower_id directly as tower_type since we simplified the logic
            tower_type = self.selected_tower_id

            # Check upgrade buttons
            upgrade_y = self.panel_y + 140
            for upgrade_type in UpgradeType:
                upgrade_rect = pygame.Rect(
                    self.panel_x + 200, upgrade_y, 450, self.upgrade_item_height)
                if upgrade_rect.collidepoint(x, y):
                    # Check if upgrade can be purchased (affordable and not at limit)
                    if self.upgrade_system.can_purchase_upgrade(tower_type, upgrade_type):
                        if self.upgrade_system.purchase_upgrade(tower_type, upgrade_type):
                            # Upgrade successful - refresh the display
                            pass
                    return True
                upgrade_y += self.upgrade_item_height + 10
        return True

    def handle_scroll(self, scroll_amount: int):
        """Handle mouse wheel scroll"""
        if not self.is_open:
            return

        self.scroll_offset = max(0, min(
            self.max_scroll, self.scroll_offset - scroll_amount * 40))  # Increased scroll speed

    def draw(self, screen: pygame.Surface):
        """Draw the upgrade menu"""
        if not self.is_open:
            return

        # Draw background overlay
        overlay = pygame.Surface((self.screen_width, self.screen_height))
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        screen.blit(overlay, (0, 0))

        # Draw main panel
        panel_rect = pygame.Rect(
            self.panel_x, self.panel_y, self.panel_width, self.panel_height)
        pygame.draw.rect(screen, self.colors['panel'], panel_rect)
        pygame.draw.rect(screen, self.colors['border'], panel_rect, 3)

        # Draw title
        title_text = self.fonts['title'].render(
            "Global Tower Upgrades", True, self.colors['text'])
        title_rect = title_text.get_rect(
            centerx=self.panel_x + self.panel_width // 2, y=self.panel_y + 20)
        screen.blit(title_text, title_rect)

        # Draw close button
        close_button_rect = pygame.Rect(
            self.panel_x + self.panel_width - 50, self.panel_y + 10, 40, 30)
        pygame.draw.rect(screen, self.colors['button'], close_button_rect)
        close_text = self.fonts['normal'].render(
            "X", True, self.colors['text'])
        close_text_rect = close_text.get_rect(center=close_button_rect.center)
        screen.blit(close_text, close_text_rect)

        # Draw points display
        points_text = self.fonts['header'].render(
            f"Points: {self.upgrade_system.points}", True, self.colors['accent'])
        screen.blit(points_text, (self.panel_x + 20, self.panel_y + 60))

        # Draw terrain currency display
        terrain_currency_text = self.fonts['header'].render(
            f"Terrain Currency: {self.upgrade_system.terrain_currency}", True, self.colors['accent'])
        screen.blit(terrain_currency_text,
                    (self.panel_x + 20, self.panel_y + 90))

        if self.mode in ('tower_list', 'create_tower'):
            self.draw_tower_list(screen)
        elif self.mode == 'tower_detail':
            self.draw_tower_detail(screen)

    def draw_tower_list(self, screen: pygame.Surface):
        """Draw list of all tower types available for upgrading, with scroll buttons"""
        # Draw header
        header_text = self.fonts['header'].render(
            "Tower Types:", True, self.colors['text'])
        screen.blit(header_text, (self.panel_x + 20, self.panel_y + 100))

        # Get all tower types and their upgrade data
        all_towers = self.get_all_tower_types_with_upgrades()
        tower_y = self.panel_y + 140

        for tower_data in all_towers:
            # Check if tower is visible (accounting for scroll) - more permissive bounds
            visible_y = tower_y - self.scroll_offset
            if visible_y < self.panel_y + 120 or visible_y > self.panel_y + self.panel_height - 20:
                tower_y += self.button_height + 5
                continue

            # Draw tower button
            button_rect = pygame.Rect(
                self.panel_x + 20, tower_y - self.scroll_offset, 400, self.button_height)
            pygame.draw.rect(screen, self.colors['button'], button_rect)
            pygame.draw.rect(screen, self.colors['border'], button_rect, 2)

            # Draw tower info
            tower_name = tower_data['tower_type'].replace('_', ' ').title()
            tower_text = self.fonts['normal'].render(
                tower_name, True, self.colors['text'])
            screen.blit(tower_text, (self.panel_x + 30,
                        tower_y - self.scroll_offset + 10))

            # Draw upgrade summary
            summary_text = self.fonts['small'].render(
                f"Total Level: {tower_data['total_level']}", True, self.colors['text_secondary'])
            screen.blit(summary_text, (self.panel_x + 30,
                        tower_y - self.scroll_offset + 25))

            tower_y += self.button_height + 5

        # Draw scroll buttons if scrolling is needed
        if self.max_scroll > 0:
            # Up button
            up_button_rect = pygame.Rect(
                self.panel_x + self.panel_width - 60, self.panel_y + 140, 40, 30)
            up_color = self.colors['button'] if self.scroll_offset > 0 else self.colors['button_disabled']
            pygame.draw.rect(screen, up_color, up_button_rect)
            pygame.draw.rect(screen, self.colors['border'], up_button_rect, 2)
            up_text = self.fonts['normal'].render(
                "↑", True, self.colors['text'])
            up_text_rect = up_text.get_rect(center=up_button_rect.center)
            screen.blit(up_text, up_text_rect)
            # Down button
            down_button_rect = pygame.Rect(
                self.panel_x + self.panel_width - 60, self.panel_y + self.panel_height - 80, 40, 30)
            down_color = self.colors['button'] if self.scroll_offset < self.max_scroll else self.colors['button_disabled']
            pygame.draw.rect(screen, down_color, down_button_rect)
            pygame.draw.rect(
                screen, self.colors['border'], down_button_rect, 2)
            down_text = self.fonts['normal'].render(
                "↓", True, self.colors['text'])
            down_text_rect = down_text.get_rect(center=down_button_rect.center)
            screen.blit(down_text, down_text_rect)

    def get_all_tower_types_with_upgrades(self):
        """Get all tower types with their upgrade data"""
        all_towers = []

        # Get all towers from the upgrade system
        for tower_type in self.upgrade_system.tower_types:
            upgrade_data = self.upgrade_system.upgrades[tower_type]
            tower_info = {
                'tower_type': tower_type,
                'tower_id': tower_type,  # Use tower_type as the ID
                'total_level': upgrade_data.get_total_level(),
                'has_upgrades': upgrade_data.get_total_level() > 0
            }
            all_towers.append(tower_info)

        return all_towers

    def draw_tower_detail(self, screen: pygame.Surface):
        """Draw upgrade details for selected tower"""
        if not self.selected_tower_id:
            return

        # Use selected_tower_id directly as tower_type since we simplified the logic
        tower_type = self.selected_tower_id

        upgrade_info = self.upgrade_system.get_tower_upgrade_info(tower_type)

        # Draw back button
        back_button_rect = pygame.Rect(
            self.panel_x + 20, self.panel_y + 60, 80, 30)
        pygame.draw.rect(screen, self.colors['button'], back_button_rect)
        pygame.draw.rect(screen, self.colors['border'], back_button_rect, 2)
        back_text = self.fonts['small'].render(
            "← Back", True, self.colors['text'])
        back_text_rect = back_text.get_rect(center=back_button_rect.center)
        screen.blit(back_text, back_text_rect)

        # Draw tower header
        tower_name = tower_type.replace('_', ' ').title()
        header_text = self.fonts['header'].render(
            f"{tower_name} Upgrades", True, self.colors['text'])
        screen.blit(header_text, (self.panel_x + 200, self.panel_y + 100))

        # Draw upgrade items
        upgrade_y = self.panel_y + 140
        for upgrade_type in UpgradeType:
            info = upgrade_info[upgrade_type.value]

            # Draw upgrade item background - make it look clickable
            item_rect = pygame.Rect(
                self.panel_x + 200, upgrade_y, 450, self.upgrade_item_height)

            # Choose color based on upgrade status
            if info['is_at_limit']:
                # Grayed out for maxed upgrades
                button_color = self.colors['button_disabled']
            elif info['can_purchase']:
                # Highlighted for purchasable upgrades
                button_color = self.colors['button_hover']
            else:
                # Grayed out for unaffordable upgrades
                button_color = self.colors['button_disabled']

            pygame.draw.rect(screen, button_color, item_rect)
            pygame.draw.rect(screen, self.colors['border'], item_rect, 2)

            # Draw upgrade name and description
            upgrade_name = upgrade_type.value.replace('_', ' ').title()
            name_text = self.fonts['normal'].render(
                upgrade_name, True, self.colors['text'])
            screen.blit(name_text, (self.panel_x + 210, upgrade_y + 10))

            desc_text = self.fonts['small'].render(
                info['description'], True, self.colors['text_secondary'])
            screen.blit(desc_text, (self.panel_x + 210, upgrade_y + 35))

            # Draw cost
            cost_text = self.fonts['normal'].render(
                f"Cost: {info['cost']} points", True, self.colors['text'])
            screen.blit(cost_text, (self.panel_x + 210, upgrade_y + 55))

            # Draw status message
            if info['is_at_limit']:
                status_text = self.fonts['small'].render(
                    "MAXED OUT", True, self.colors['error'])
                screen.blit(status_text, (self.panel_x + 350, upgrade_y + 55))
            elif info['can_purchase']:
                status_text = self.fonts['small'].render(
                    "Click to upgrade", True, self.colors['accent'])
                screen.blit(status_text, (self.panel_x + 350, upgrade_y + 55))
            else:
                status_text = self.fonts['small'].render(
                    "Not enough points", True, self.colors['text_secondary'])
                screen.blit(status_text, (self.panel_x + 350, upgrade_y + 55))

            upgrade_y += self.upgrade_item_height + 10

    def draw_victory_screen(self, screen: pygame.Surface, difficulty: int, points_earned: int):
        """Draw victory screen with points earned"""
        # Draw background overlay
        overlay = pygame.Surface((self.screen_width, self.screen_height))
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        screen.blit(overlay, (0, 0))

        # Draw victory panel
        panel_width = 600
        panel_height = 400
        panel_x = (self.screen_width - panel_width) // 2
        panel_y = (self.screen_height - panel_height) // 2

        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(screen, self.colors['panel'], panel_rect)
        pygame.draw.rect(screen, self.colors['border'], panel_rect, 3)

        # Draw victory title
        victory_text = self.fonts['title'].render(
            "VICTORY!", True, self.colors['success'])
        victory_rect = victory_text.get_rect(
            centerx=panel_x + panel_width // 2, y=panel_y + 40)
        screen.blit(victory_text, victory_rect)

        # Draw difficulty info
        difficulty_text = self.fonts['header'].render(
            f"Difficulty: {difficulty}", True, self.colors['text'])
        difficulty_rect = difficulty_text.get_rect(
            centerx=panel_x + panel_width // 2, y=panel_y + 100)
        screen.blit(difficulty_text, difficulty_rect)

        # Draw points earned
        points_text = self.fonts['header'].render(
            f"Points Earned: {points_earned}", True, self.colors['accent'])
        points_rect = points_text.get_rect(
            centerx=panel_x + panel_width // 2, y=panel_y + 140)
        screen.blit(points_text, points_rect)

        # Draw total points
        total_points_text = self.fonts['normal'].render(
            f"Total Points: {self.upgrade_system.points}", True, self.colors['text'])
        total_points_rect = total_points_text.get_rect(
            centerx=panel_x + panel_width // 2, y=panel_y + 180)
        screen.blit(total_points_text, total_points_rect)

        # Draw instructions
        instruction_text = self.fonts['normal'].render(
            "Press SPACE to continue", True, self.colors['text_secondary'])
        instruction_rect = instruction_text.get_rect(
            centerx=panel_x + panel_width // 2, y=panel_y + 220)
        screen.blit(instruction_text, instruction_rect)
