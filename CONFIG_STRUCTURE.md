# Configuration Structure Documentation

## Starting Money Configuration

The game configuration has two sections that contain starting money values. This document explains their purpose and how they should be synchronized.

### `game_config.starting_money`
- **Purpose**: The actual amount of money the player starts with in the game
- **Used by**: `game.py` when initializing the game state
- **Range**: Typically 20-100 for balanced gameplay
- **This is the authoritative value** that determines the player's starting resources

### `progression_config.starting_money`
- **Purpose**: Baseline value used by AI systems for economic scaling calculations
- **Used by**: AI generators (`ModularAIGenerator`, `AdaptiveConfigGenerator`, etc.) for economic adjustments
- **Range**: Should match `game_config.starting_money` for consistency
- **This value is used as a reference point** for AI economic modifications

## Synchronization Requirements

**IMPORTANT**: These two values must be kept synchronized to avoid confusion and inconsistent behavior.

### When AI systems modify starting money:
1. They calculate adjustments based on `progression_config.starting_money`
2. They apply the new value to both `progression_config.starting_money` AND `game_config.starting_money`
3. This ensures the player experience matches the AI's economic calculations

### Fixed in AI Generators:
- `ModularAIGenerator._apply_economic_adjustments()` - Now syncs both values
- `ModularAIGenerator._enforce_minimum_starting_money()` - Now syncs both values
- `FullAIGenerator._enforce_minimum_starting_money()` - Now syncs both values
- `AdaptiveConfigGenerator._enforce_minimum_starting_money()` - Now syncs both values
- `make_specific_config._enforce_minimum_starting_money()` - Now syncs both values

### Example of Correct Configuration:
```json
{
  "game_config": {
    "starting_money": 75,
    "starting_lives": 20
  },
  "progression_config": {
    "starting_money": 75,  // Same as game_config
    "starting_lives": 20,
    "economic_scaling": "automatic"
  }
}
```

### Example of Incorrect Configuration (Fixed):
```json
{
  "game_config": {
    "starting_money": 72,     // Player starts with $72
    "starting_lives": 23
  },
  "progression_config": {
    "starting_money": 200,    // AI thinks baseline is $200 - WRONG!
    "starting_lives": 23,
    "economic_scaling": "automatic"
  }
}
```

## Why This Matters

When these values are out of sync:
1. **Player confusion**: The player starts with one amount but the AI balances for another
2. **Inconsistent difficulty**: AI economic adjustments are based on wrong baseline
3. **Debugging difficulty**: Hard to understand why economic balance feels off

With synchronized values:
1. **Clear player experience**: Starting money matches AI expectations
2. **Consistent difficulty scaling**: AI adjustments are based on actual player starting money
3. **Easier debugging**: Single source of truth for starting money
