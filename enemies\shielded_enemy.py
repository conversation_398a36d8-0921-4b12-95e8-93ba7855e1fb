import pygame
from typing import List, <PERSON>ple
from .enemy import Enemy
from config.game_config import get_balance_config


class ShieldedEnemy(Enemy):
    """Enemy with regenerating shields"""

    def __init__(self, path: List[Tuple[int, int]], wave_number: int = 1):
        super().__init__(path, wave_number)
        self.max_health = 15
        self.health = self.max_health
        self.max_shield = 15
        self.shield = self.max_shield
        self.shield_regen_timer = 0
        self.shield_regen_delay = 60   # 1 second at 60 FPS (much faster)
        self.shield_regen_amount = 3   # Regenerate 3 shield points per cycle
        self.speed = 1.2
        self.reward = 10
        self.size = 10
        self.color = (0, 255, 255)  # <PERSON>an

    def update(self):
        """Update with shield regeneration"""
        super().update()
        self._update_shield_regeneration(1.0)

    def update_with_speed(self, speed_multiplier: float):
        """Update with speed multiplier for performance optimization"""
        super().update_with_speed(speed_multiplier)
        self._update_shield_regeneration(speed_multiplier)

    def _update_shield_regeneration(self, speed_multiplier: float):
        """Update shield regeneration with proper speed multiplier handling"""
        # Regenerate shield
        if self.shield < self.max_shield:
            self.shield_regen_timer += speed_multiplier

            if self.shield_regen_timer >= self.shield_regen_delay:
                # Regenerate multiple shield points, but don't exceed maximum
                regen_amount = min(self.shield_regen_amount,
                                   self.max_shield - self.shield)
                self.shield += regen_amount
                self.shield_regen_timer = 0

    def take_damage(self, damage: int, tower_type: str = 'basic'):
        """Damage goes to shield first, then health - integrates with base enemy damage system"""
        # Store original state
        original_health = self.health
        original_shield = self.shield
        original_total_hp = self.shield + self.health

        # Calculate modified damage using the base enemy damage system logic
        # (without actually applying it to health yet)
        config = get_balance_config()
        counter_config = config.get('counter_system', {})
        multipliers = counter_config.get('tower_enemy_multipliers', {})
        default_multiplier = counter_config.get('default_multiplier', 1.0)
        max_multiplier = counter_config.get('max_multiplier', 3.0)
        min_multiplier = counter_config.get('min_multiplier', 0.1)

        # Calculate damage multiplier
        damage_multiplier = default_multiplier
        enemy_class = self.__class__.__name__

        # Check for tower-specific multipliers
        if tower_type in multipliers:
            tower_multipliers = multipliers[tower_type]
            if enemy_class in tower_multipliers:
                damage_multiplier = tower_multipliers[enemy_class]
            elif tower_type == 'lightning' and self.wet and 'wet_enemies' in tower_multipliers:
                damage_multiplier = tower_multipliers['wet_enemies']

        # Clamp multiplier to acceptable range
        damage_multiplier = max(min_multiplier, min(
            max_multiplier, damage_multiplier))

        # Apply multiplier to damage
        modified_damage = int(damage * damage_multiplier)

        # Apply buff-based damage reductions if they exist
        if hasattr(self, 'damage_resistances'):
            resistance = self.damage_resistances.get(tower_type, 0)
            modified_damage = int(modified_damage * (1 - resistance))

        if hasattr(self, 'armor_reduction'):
            modified_damage = int(modified_damage * (1 - self.armor_reduction))

        # Now apply the modified damage to shield first, but don't let it leak to health
        remaining_damage = modified_damage

        # Apply damage to shield first
        if self.shield > 0 and remaining_damage > 0:
            shield_damage = min(self.shield, remaining_damage)
            self.shield -= shield_damage
            remaining_damage -= shield_damage
            self.shield_regen_timer = 0  # Reset regen timer when shield is hit

        # If shield absorbed all damage, don't apply any to health
        # Only apply damage to health if there was no shield to absorb it
        if original_shield == 0 and remaining_damage > 0:
            health_damage = min(self.health, remaining_damage)
            self.health -= health_damage

        # Calculate actual total damage dealt
        new_total_hp = max(0, self.shield + self.health)
        actual_damage = original_total_hp - new_total_hp

        # Ensure we don't return more damage than we actually had HP
        actual_damage = min(actual_damage, original_total_hp)

        return actual_damage

    def draw(self, screen: pygame.Surface):
        """Draw enemy with shield indicator"""
        super().draw(screen)

        # Draw shield bar (always show background to indicate shield capability)
        bar_width = self.size * 2
        bar_height = 4  # Made slightly taller for better visibility
        bar_x = int(self.x - bar_width // 2)
        bar_y = int(self.y - self.size - 18)  # Moved up slightly

        # Shield background (dark gray)
        pygame.draw.rect(screen, (60, 60, 60),
                         (bar_x, bar_y, bar_width, bar_height))

        # Shield amount (bright cyan)
        if self.shield > 0:
            shield_width = int((self.shield / self.max_shield) * bar_width)
            pygame.draw.rect(screen, (0, 255, 255),
                             (bar_x, bar_y, shield_width, bar_height))

        # Draw shield border for better visibility
        pygame.draw.rect(screen, (255, 255, 255),
                         (bar_x, bar_y, bar_width, bar_height), 1)

        # Add shield regeneration indicator (pulsing effect when regenerating)
        if self.shield < self.max_shield and self.shield_regen_timer > 60:  # Show after 1 second
            # Calculate regeneration progress (0.0 to 1.0)
            regen_progress = self.shield_regen_timer / self.shield_regen_delay

            # Pulsing green effect that gets brighter as regeneration approaches
            pulse_intensity = int(64 + 191 * regen_progress)  # 64 to 255
            pulse_alpha = int(
                128 + 127 * abs(((self.shield_regen_timer % 30) / 30.0) * 2 - 1))

            # Green overlay with pulsing effect
            regen_color = (0, pulse_intensity, 0, pulse_alpha)
            regen_surface = pygame.Surface(
                (bar_width, bar_height), pygame.SRCALPHA)
            regen_surface.fill(regen_color)
            screen.blit(regen_surface, (bar_x, bar_y))

            # Add a bright flash when very close to regeneration
            if self.shield_regen_timer >= self.shield_regen_delay - 10:
                flash_alpha = int(
                    255 * ((self.shield_regen_delay - self.shield_regen_timer) / 10.0))
                flash_surface = pygame.Surface(
                    (bar_width, bar_height), pygame.SRCALPHA)
                flash_surface.fill((255, 255, 255, flash_alpha))
                screen.blit(flash_surface, (bar_x, bar_y))
