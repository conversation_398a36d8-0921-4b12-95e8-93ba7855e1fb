from .enemy import Enemy
import pygame
import math


class SpeedBoss(Enemy):
    """Boss that becomes faster as it takes damage"""

    def __init__(self, path, wave_number=1):
        super().__init__(path, wave_number)
        self.health = 400
        self.max_health = 400
        self.base_speed = 1.4
        self.speed = self.base_speed
        self.reward = 180
        self.color = (255, 255, 0)  # Yellow
        self.size = 18
        self.damage = 2  # Speed Boss deals 2 damage to player

        # Speed boost mechanics
        self.speed_multiplier = 1.0
        self.max_speed_multiplier = 4.0
        self.trail_positions = []  # For speed trail effect
        self.dash_timer = 0
        self.dash_cooldown = 240  # 4 seconds
        self.is_dashing = False
        self.dash_duration = 30  # 0.5 seconds

        # BALANCE FIX: Override immunities to ensure freeze effects can work
        # Speed boss especially needs to be slowable
        self.immunities['freeze_immune'] = False

    def update(self):
        """Update with speed mechanics"""
        # Calculate speed multiplier based on health lost BEFORE movement
        health_percentage = self.health / self.max_health
        self.speed_multiplier = 1.0 + \
            (1.0 - health_percentage) * (self.max_speed_multiplier - 1.0)

        # Handle dash ability
        if not self.is_dashing:
            self.dash_timer += 1
            if self.dash_timer >= self.dash_cooldown:
                self.is_dashing = True
                self.dash_timer = 0
        else:
            # During dash, increment timer
            self.dash_timer += 1
            if self.dash_timer >= self.dash_duration:
                self.is_dashing = False
                self.dash_timer = 0  # Reset for next dash cooldown

        # Set speed based on current state
        if self.is_dashing:
            self.speed = self.base_speed * self.speed_multiplier * 3  # Triple speed during dash
        else:
            self.speed = self.base_speed * self.speed_multiplier

        # Now call parent update with correct speed
        super().update()

        # Update trail positions (add current position)
        self.trail_positions.append((self.x, self.y))
        if len(self.trail_positions) > 15:  # Longer trail for better visibility
            self.trail_positions.pop(0)

    def apply_terrain_speed_effects(self):
        """Override to preserve speed boss's custom speed calculations"""
        if not self.map_reference:
            return

        from game_systems.terrain_types import get_terrain_property, SAND
        from config.game_config import get_balance_config

        # Get terrain at current position
        terrain_type = self.map_reference.get_terrain_at_pixel(
            int(self.x), int(self.y))

        # Store our custom speed before terrain effects
        custom_speed = self.speed

        # Handle complete freeze (freezer tower) - completely stops movement
        if self.frozen and hasattr(self, 'complete_freeze') and self.complete_freeze:
            self.speed = 0.0  # Complete movement stop
            return

        # Apply terrain effects to our custom speed (not base_speed)
        if terrain_type == SAND:
            speed_multiplier = get_terrain_property(
                terrain_type, 'speed_multiplier')

            if self.frozen:
                config = get_balance_config()
                # Boss enemies get moderate freeze effect (40% slow)
                freeze_factor = 0.4
                self.speed = custom_speed * speed_multiplier * freeze_factor
            else:
                self.speed = custom_speed * speed_multiplier
        else:
            # Handle freeze on non-sand terrain
            if self.frozen:
                config = get_balance_config()
                # Boss enemies get moderate freeze effect (40% slow)
                freeze_factor = 0.4
                self.speed = custom_speed * freeze_factor
            # If not frozen, keep our custom speed as-is

    def update_with_speed(self, speed_multiplier: float):
        """Override to ensure speed boss mechanics work with game speed multiplier"""
        # First, do our custom speed calculations
        health_percentage = self.health / self.max_health
        self.speed_multiplier = 1.0 + \
            (1.0 - health_percentage) * (self.max_speed_multiplier - 1.0)

        # Handle dash ability
        if not self.is_dashing:
            self.dash_timer += speed_multiplier
            if self.dash_timer >= self.dash_cooldown:
                self.is_dashing = True
                self.dash_timer = 0
        else:
            # During dash, increment timer
            self.dash_timer += speed_multiplier
            if self.dash_timer >= self.dash_duration:
                self.is_dashing = False
                self.dash_timer = 0  # Reset for next dash cooldown

        # Set speed based on current state
        if self.is_dashing:
            self.speed = self.base_speed * self.speed_multiplier * 3  # Triple speed during dash
        else:
            self.speed = self.base_speed * self.speed_multiplier

        # Handle freeze effect - adjust timer based on speed
        if self.frozen:
            self.freeze_timer -= speed_multiplier
            if self.freeze_timer <= 0:
                self.frozen = False
                self.complete_freeze = False  # Reset complete freeze flag

        # Handle wet effect (if not immune) - adjust timer based on speed
        if self.wet and not self.is_immune_to('wet'):
            self.wet_timer -= speed_multiplier
            if self.wet_timer <= 0:
                self.wet = False
                self.lightning_damage_multiplier = 1.0
        elif self.is_immune_to('wet'):
            # Immune enemies can't be wet
            self.wet = False
            self.wet_timer = 0
            self.lightning_damage_multiplier = 1.0

        # Update buff system
        if self.buff_manager:
            self.buff_manager.update()

        # Apply terrain-based speed effects (uses our custom method)
        self.apply_terrain_speed_effects()

        # Move multiple times based on speed multiplier for smooth fast movement
        for _ in range(int(speed_multiplier)):
            if not self.reached_end:
                self.move_along_path()

        # Handle fractional speed
        if speed_multiplier % 1 > 0:
            # Store original speed
            original_speed = self.speed
            # Apply fractional multiplier to speed for this partial movement
            self.speed *= (speed_multiplier % 1)
            if not self.reached_end:
                self.move_along_path()
            # Restore original speed
            self.speed = original_speed

        # Update trail positions (add current position)
        self.trail_positions.append((self.x, self.y))
        if len(self.trail_positions) > 15:  # Longer trail for better visibility
            self.trail_positions.pop(0)

    def draw(self, screen):
        """Draw speed boss with trail effects"""
        # Draw speed trail (only if we have enough positions)
        if len(self.trail_positions) > 1:
            for i, (trail_x, trail_y) in enumerate(self.trail_positions[:-1]):
                # Calculate alpha and size based on position in trail
                trail_length = len(self.trail_positions)
                # Avoid division by zero
                alpha_factor = i / max(1, trail_length - 1)
                # Range from 50 to 200 for better visibility
                alpha = int(50 + 150 * alpha_factor)

                # Make trail more visible during dash
                if self.is_dashing:
                    alpha = min(255, alpha + 100)

                trail_color = (255, 255, 0, alpha)
                trail_size = max(
                    2, int(self.size * (0.2 + 0.6 * alpha_factor)))

                # Create surface for trail with alpha
                trail_surface = pygame.Surface(
                    (trail_size * 2, trail_size * 2), pygame.SRCALPHA)
                pygame.draw.circle(trail_surface, trail_color,
                                   (trail_size, trail_size), trail_size)
                screen.blit(trail_surface, (trail_x -
                            trail_size, trail_y - trail_size))

        # Draw main boss
        boss_color = self.color
        if self.is_dashing:
            boss_color = (255, 255, 255)  # White during dash

        pygame.draw.circle(screen, boss_color,
                           (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, (255, 0, 0),
                           (int(self.x), int(self.y)), self.size, 2)

        # Draw speed indicators (lightning bolts)
        speed_level = int(self.speed_multiplier)
        for i in range(min(speed_level, 4)):
            bolt_x = self.x - self.size + 5 + i * 8
            bolt_y = self.y - self.size + 5
            self.draw_lightning_bolt(screen, bolt_x, bolt_y, 6)

        # Draw health bar
        bar_width = self.size * 2.5
        bar_height = 6

        pygame.draw.rect(screen, (100, 0, 0),
                         (self.x - bar_width//2, self.y - self.size - 15, bar_width, bar_height))

        health_percentage = self.health / self.max_health
        pygame.draw.rect(screen, (255, 255, 0),
                         (self.x - bar_width//2, self.y - self.size - 15,
                          int(bar_width * health_percentage), bar_height))

        # Draw boss title
        font = pygame.font.Font(None, 20)
        title_text = font.render("SPEED BOSS", True, (255, 255, 255))
        title_rect = title_text.get_rect(
            center=(self.x, self.y - self.size - 25))
        screen.blit(title_text, title_rect)

    def draw_lightning_bolt(self, screen, x, y, size):
        """Draw a small lightning bolt indicator"""
        points = [
            (x, y),
            (x + size//2, y + size//3),
            (x + size//3, y + size//3),
            (x + size, y + size),
            (x + size//2, y + 2*size//3),
            (x + 2*size//3, y + 2*size//3),
        ]
        pygame.draw.polygon(screen, (255, 255, 255), points)
