"""
Daily Level Generator System

Handles time-based level generation that creates new levels twice daily (6 AM and 8 PM)
with the condition that the player has 2 or fewer unsolved base levels.
"""

import os
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass


@dataclass
class GenerationSchedule:
    """Represents a scheduled generation time"""
    hour: int  # 0-23
    minute: int = 0
    name: str = ""


class DailyLevelGenerator:
    """
    Manages time-based level generation with efficient scheduling and tracking.

    Features:
    - Generates levels twice daily at 6 AM and 8 PM
    - Only generates if player has ≤2 unsolved base levels
    - Tracks generation history and prevents duplicate generations
    - Efficient startup checking with minimal file I/O
    """

    def __init__(self):
        """Initialize the daily level generator"""
        self.tracker_path = os.path.join("config", "daily_level_tracker.json")
        self.completion_tracker_path = os.path.join(
            "config", "level_completion_tracker.json")

        # Generation schedule: 6 AM and 8 PM
        self.generation_schedule = [
            GenerationSchedule(hour=6, minute=0, name="morning"),
            GenerationSchedule(hour=20, minute=0, name="evening")  # 8 PM
        ]

        # Cache for efficiency
        self._last_check_time = None
        self._cached_tracker_data = None
        self._cached_unsolved_count = None
        self._cache_timestamp = None

        # Load or create tracker data
        self.tracker_data = self._load_tracker_data()

    def _load_tracker_data(self) -> Dict[str, Any]:
        """Load the daily level tracker data"""
        if os.path.exists(self.tracker_path):
            try:
                with open(self.tracker_path, 'r') as f:
                    data = json.load(f)

                # Ensure new format fields exist
                if 'last_startup_check' not in data:
                    data['last_startup_check'] = None
                if 'generation_times' not in data:
                    data['generation_times'] = {}
                if 'daily_generations' not in data:
                    data['daily_generations'] = {}

                return data
            except (json.JSONDecodeError, IOError) as e:
                print(f"Warning: Could not load daily level tracker: {e}")

        # Return default structure
        return {
            'last_generation_date': None,
            'last_startup_check': None,
            'levels_generated_today': 0,
            'total_levels_generated': 0,
            'generation_history': {},
            'generation_times': {},  # Track specific generation times
            'daily_generations': {},  # Track generations per day
            'current_difficulty': 50
        }

    def _save_tracker_data(self) -> None:
        """Save the tracker data to file"""
        try:
            os.makedirs(os.path.dirname(self.tracker_path), exist_ok=True)
            with open(self.tracker_path, 'w') as f:
                json.dump(self.tracker_data, f, indent=2)
        except IOError as e:
            print(f"Error saving daily level tracker: {e}")

    def _get_current_time_info(self) -> Tuple[datetime, str, str]:
        """Get current time information"""
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        time_str = now.strftime("%H:%M:%S")
        return now, date_str, time_str

    def _should_check_for_generation(self) -> bool:
        """
        Determine if we should check for generation opportunities.
        Uses efficient caching to avoid excessive checks.
        """
        current_time = time.time()

        # Only check every 5 minutes to be efficient
        if (self._last_check_time is not None and
                current_time - self._last_check_time < 300):  # 5 minutes
            return False

        self._last_check_time = current_time
        return True

    def count_unsolved_base_levels(self) -> int:
        """
        Count the number of unsolved base levels (excluding variants).
        Uses caching for efficiency.
        """
        current_time = time.time()

        # Use cached result if recent (within 1 minute)
        if (self._cached_unsolved_count is not None and
            self._cache_timestamp is not None and
                current_time - self._cache_timestamp < 60):
            return self._cached_unsolved_count

        try:
            # Import here to avoid circular imports
            from launcher.configuration_manager import ConfigurationManager

            config_manager = ConfigurationManager()
            config_manager.load_configurations()

            # Load completion tracker
            completed_levels = {}
            if os.path.exists(self.completion_tracker_path):
                try:
                    with open(self.completion_tracker_path, 'r') as f:
                        completed_levels = json.load(f)
                except (json.JSONDecodeError, IOError):
                    pass

            # Count unsolved base levels
            unsolved_count = 0
            for config in config_manager.configs:
                # Skip variants - only count base levels
                if config.get('is_variant', False):
                    continue

                # Check if this base level is completed
                filename = config.get('filename', '')
                if filename not in completed_levels:
                    unsolved_count += 1

            # Cache the result
            self._cached_unsolved_count = unsolved_count
            self._cache_timestamp = current_time

            return unsolved_count

        except Exception as e:
            print(f"Error counting unsolved base levels: {e}")
            # Return a safe default that won't trigger generation
            return 10

    def _get_next_generation_times(self, from_time: datetime) -> List[Tuple[datetime, GenerationSchedule]]:
        """Get the next scheduled generation times after the given time"""
        next_times = []

        for schedule in self.generation_schedule:
            # Try today first
            today_time = from_time.replace(
                hour=schedule.hour,
                minute=schedule.minute,
                second=0,
                microsecond=0
            )

            if today_time > from_time:
                next_times.append((today_time, schedule))
            else:
                # Try tomorrow
                tomorrow_time = today_time + timedelta(days=1)
                next_times.append((tomorrow_time, schedule))

        # Sort by time
        next_times.sort(key=lambda x: x[0])
        return next_times

    def _has_generated_at_time(self, date_str: str, schedule: GenerationSchedule) -> bool:
        """Check if we've already generated at this specific time today"""
        if date_str not in self.tracker_data['daily_generations']:
            return False

        daily_data = self.tracker_data['daily_generations'][date_str]
        return schedule.name in daily_data.get('generated_times', [])

    def _mark_generation_completed(self, date_str: str, schedule: GenerationSchedule, level_name: str) -> None:
        """Mark that generation has been completed for this time slot"""
        if date_str not in self.tracker_data['daily_generations']:
            self.tracker_data['daily_generations'][date_str] = {
                'generated_times': [],
                'levels_created': []
            }

        daily_data = self.tracker_data['daily_generations'][date_str]
        if schedule.name not in daily_data['generated_times']:
            daily_data['generated_times'].append(schedule.name)

        daily_data['levels_created'].append({
            'time': schedule.name,
            'level_name': level_name,
            'timestamp': datetime.now().isoformat()
        })

        # Update legacy fields for compatibility
        self.tracker_data['last_generation_date'] = date_str
        self.tracker_data['levels_generated_today'] = len(
            daily_data['levels_created'])
        self.tracker_data['total_levels_generated'] += 1

    def _generate_new_level(self) -> Optional[str]:
        """Generate a new level using the adaptive config generator"""
        try:
            print("🕐 Generating scheduled daily level...")

            from ai.adaptive_config_generator import AdaptiveConfigGenerator

            # Create adaptive config generator
            generator = AdaptiveConfigGenerator(
                use_modular_ai=True, use_full_ai=False)

            # Try to generate a new level
            config = generator.generate_config_from_recent_games()

            if config:
                level_name = config.get('level_name', 'Unknown Level')
                print(f"✅ Daily level generated successfully: {level_name}")
                return level_name
            else:
                print("❌ Failed to generate daily level")
                return None

        except Exception as e:
            print(f"Error generating daily level: {e}")
            return None

    def check_and_generate_if_needed(self, force_check: bool = False) -> bool:
        """
        Check if it's time to generate a new level and do so if conditions are met.

        Args:
            force_check: If True, bypass the efficiency check timer

        Returns:
            True if a level was generated, False otherwise
        """
        # Efficiency check (unless forced)
        if not force_check and not self._should_check_for_generation():
            return False

        now, date_str, time_str = self._get_current_time_info()

        # Check if we have too many unsolved base levels
        unsolved_count = self.count_unsolved_base_levels()
        if unsolved_count > 2:
            print(
                f"⏸️ Skipping daily generation: {unsolved_count} unsolved base levels (max: 2)")
            return False

        # Check each scheduled time
        for schedule in self.generation_schedule:
            # Check if it's time for this generation slot
            current_hour = now.hour
            current_minute = now.minute

            # Allow a 30-minute window after the scheduled time
            if (current_hour == schedule.hour and current_minute >= schedule.minute) or \
               (current_hour == schedule.hour + 1 and current_minute < 30):

                # Check if we've already generated for this time slot today
                if not self._has_generated_at_time(date_str, schedule):
                    print(
                        f"🎯 Time for {schedule.name} generation ({schedule.hour:02d}:{schedule.minute:02d})")
                    print(f"   Unsolved base levels: {unsolved_count}/2")

                    # Generate the level
                    level_name = self._generate_new_level()
                    if level_name:
                        self._mark_generation_completed(
                            date_str, schedule, level_name)
                        self._save_tracker_data()
                        return True

        return False

    def check_startup_generation(self) -> bool:
        """
        Check for missed generation opportunities since last startup.
        This is called when the game launcher starts up.

        Returns:
            True if any levels were generated, False otherwise
        """
        now, date_str, time_str = self._get_current_time_info()

        # Get last startup check time
        last_startup = self.tracker_data.get('last_startup_check')
        if last_startup:
            try:
                last_startup_time = datetime.fromisoformat(last_startup)
            except ValueError:
                last_startup_time = now - \
                    timedelta(days=1)  # Default to yesterday
        else:
            last_startup_time = now - timedelta(days=1)  # Default to yesterday

        print(
            f"🔍 Checking for missed daily generations since {last_startup_time.strftime('%Y-%m-%d %H:%M')}")

        # Update startup check time
        self.tracker_data['last_startup_check'] = now.isoformat()

        # Check if we have too many unsolved base levels
        unsolved_count = self.count_unsolved_base_levels()
        if unsolved_count > 2:
            print(
                f"⏸️ Skipping startup generation check: {unsolved_count} unsolved base levels (max: 2)")
            self._save_tracker_data()
            return False

        generated_any = False

        # Check each day between last startup and now
        current_check = last_startup_time.replace(
            hour=0, minute=0, second=0, microsecond=0)
        while current_check <= now:
            check_date_str = current_check.strftime("%Y-%m-%d")

            # Check each scheduled time for this day
            for schedule in self.generation_schedule:
                scheduled_time = current_check.replace(
                    hour=schedule.hour,
                    minute=schedule.minute
                )

                # Only check times that have passed and are after last startup
                if scheduled_time <= now and scheduled_time > last_startup_time:
                    # Check if we missed this generation
                    if not self._has_generated_at_time(check_date_str, schedule):
                        print(
                            f"🕐 Missed {schedule.name} generation on {check_date_str} at {schedule.hour:02d}:{schedule.minute:02d}")

                        # Generate the level
                        level_name = self._generate_new_level()
                        if level_name:
                            self._mark_generation_completed(
                                check_date_str, schedule, level_name)
                            generated_any = True
                            print(f"✅ Generated missed level: {level_name}")

            current_check += timedelta(days=1)

        if generated_any:
            self._save_tracker_data()
            print("🎉 Startup generation check completed with new levels!")
        else:
            self._save_tracker_data()
            print("✓ Startup generation check completed - no missed generations")

        return generated_any

    def get_status_info(self) -> Dict[str, Any]:
        """Get current status information for debugging/display"""
        now, date_str, time_str = self._get_current_time_info()
        unsolved_count = self.count_unsolved_base_levels()

        # Get next generation times
        next_times = self._get_next_generation_times(now)
        next_generation = None
        if next_times:
            next_time, next_schedule = next_times[0]
            next_generation = {
                'time': next_time.strftime("%Y-%m-%d %H:%M"),
                'schedule_name': next_schedule.name,
                'hours_until': (next_time - now).total_seconds() / 3600
            }

        # Get today's generation status
        today_status = {}
        if date_str in self.tracker_data['daily_generations']:
            daily_data = self.tracker_data['daily_generations'][date_str]
            today_status = {
                'generated_times': daily_data.get('generated_times', []),
                'levels_created': daily_data.get('levels_created', [])
            }

        return {
            'current_time': f"{date_str} {time_str}",
            'unsolved_base_levels': unsolved_count,
            'can_generate': unsolved_count <= 2,
            'next_generation': next_generation,
            'today_status': today_status,
            'total_generated': self.tracker_data.get('total_levels_generated', 0)
        }
