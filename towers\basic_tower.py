from .tower import Tower
import math


class BasicTower(Tower):
    """Basic tower with standard stats and targeting"""

    def __init__(self, x: int, y: int):
        super().__init__(x, y, 'basic')
        self.range = 85
        self.damage = 3
        self.fire_rate = 28  # Slightly slower for balance
        self.projectile_speed = 6
        self.size = 12
        self.color = (0, 200, 0)  # Green

        # Targeting capabilities - ground only
        self.can_target_flying = False
        self.can_target_invisible = False
        self.can_target_ground = True

        # Finalize initialization to update base stats
        self.finalize_initialization()

    def acquire_target(self, enemies):
        """Find target using targeting restrictions"""
        valid_targets = []

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.range and self.can_target_enemy(enemy):
                valid_targets.append((enemy, distance))

        if not valid_targets:
            self.target = None
            return

        # Target closest to end of path
        self.target = max(
            valid_targets, key=lambda x: x[0].get_distance_from_start())[0]

        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def acquire_target_optimized(self, enemies, spatial_grid=None):
        """Optimized targeting with targeting restrictions and spatial partitioning"""
        if not enemies:
            self.target = None
            return

        # Use spatial partitioning if available
        if spatial_grid:
            nearby_enemies = spatial_grid.get_enemies_near_tower(
                self.x, self.y, self.range)
            if not nearby_enemies:
                self.target = None
                return
            enemies_to_check = nearby_enemies
        else:
            enemies_to_check = enemies

        range_squared = self.range * self.range
        valid_targets = []

        # Use squared distance for initial filtering (avoids sqrt)
        for enemy in enemies_to_check:
            dx = enemy.x - self.x
            dy = enemy.y - self.y
            distance_squared = dx * dx + dy * dy

            if distance_squared <= range_squared and self.can_target_enemy(enemy):
                # Store squared distance to avoid sqrt until needed
                valid_targets.append((enemy, distance_squared))

                # Early termination for performance
                if len(valid_targets) >= 8:
                    break

        if not valid_targets:
            self.target = None
            return

        # Find target closest to end of path (only calculate sqrt for the winner)
        best_target = max(
            valid_targets, key=lambda x: x[0].get_distance_from_start())
        self.target = best_target[0]

        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Update with speed multiplier and targeting restrictions"""
        self.acquire_target_optimized(enemies)

        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate

        # Check for time distortion effects from Timelord Boss
        time_distortion_multiplier = self.get_time_distortion_multiplier(
            enemies)

        # Decrease fire timer based on speed multiplier and time distortion
        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier * time_distortion_multiplier
