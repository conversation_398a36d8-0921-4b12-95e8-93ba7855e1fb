"""
Config Naming Utilities for Tower Defense Adaptive AI

Contains functions for generating creative filenames from level names.
"""

import os
import re
from typing import Dict, Any


def create_creative_filename(level_name: str, config_data: Dict[str, Any]) -> str:
    """Convert level name to a safe, creative filename"""
    if not level_name:
        # Fallback: use terrain strategy or difficulty as name
        if '_generation_metadata' in config_data:
            difficulty = config_data['_generation_metadata'].get('difficulty', 50)
            if difficulty >= 80:
                level_name = "Master Challenge"
            elif difficulty >= 60:
                level_name = "Advanced Trial"
            elif difficulty >= 40:
                level_name = "Standard Test"
            else:
                level_name = "Novice Training"
        else:
            level_name = "Adaptive Challenge"
    
    # Convert to safe filename
    # Remove special characters and replace spaces with underscores
    safe_name = re.sub(r'[<>:"/\\|?*]', '', level_name)  # Remove unsafe characters
    safe_name = re.sub(r'\s+', '_', safe_name)  # Replace spaces with underscores
    safe_name = re.sub(r'[^\w\-_]', '', safe_name)  # Keep only alphanumeric, hyphens, underscores
    safe_name = safe_name.strip('_-')  # Remove leading/trailing underscores or hyphens
    
    # Ensure it's not empty and not too long
    if not safe_name:
        safe_name = "Adaptive_Level"
    elif len(safe_name) > 50:
        safe_name = safe_name[:50].rstrip('_-')
    
    return safe_name


def get_unique_creative_filename(base_name: str, config_dir: str) -> str:
    """Get a unique filename by appending numbers if needed"""
    safe_base = create_creative_filename(base_name, {})
    counter = 1
    
    # First try without number
    filename = f"{safe_base}.json"
    filepath = os.path.join(config_dir, filename)
    
    if not os.path.exists(filepath):
        return filename
    
    # If it exists, try with numbers
    while True:
        filename = f"{safe_base}_{counter}.json"
        filepath = os.path.join(config_dir, filename)
        if not os.path.exists(filepath):
            return filename
        counter += 1


def generate_fallback_level_name(difficulty: int, is_multi_game: bool = False, trend: str = "stable") -> str:
    """Generate a level name when AI generation fails or is unavailable"""
    
    # Difficulty-based base names
    if difficulty >= 90:
        base_names = ["Nightmare Challenge", "Impossible Trial", "Master's Crucible", "Elite Gauntlet"]
    elif difficulty >= 75:
        base_names = ["Expert Challenge", "Advanced Trial", "Veteran Test", "Hard Mode"]
    elif difficulty >= 50:
        base_names = ["Standard Challenge", "Balanced Trial", "Moderate Test", "Normal Mode"]
    elif difficulty >= 25:
        base_names = ["Novice Challenge", "Beginner Trial", "Learning Test", "Easy Mode"]
    else:
        base_names = ["Training Grounds", "Tutorial Mode", "Practice Run", "Starter Challenge"]
    
    # Multi-game specific adjustments
    if is_multi_game:
        if trend == "improving":
            base_names = [name.replace("Challenge", "Progression").replace("Trial", "Growth") for name in base_names]
        elif trend == "declining":
            base_names = [name.replace("Challenge", "Support").replace("Trial", "Recovery") for name in base_names]
        else:
            base_names = [name.replace("Challenge", "Consistency").replace("Trial", "Stability") for name in base_names]
    
    # Pick the first one for consistency
    return base_names[0]


def extract_level_theme(adjustments: Dict[str, Any]) -> str:
    """Extract thematic elements from AI adjustments for level naming"""
    themes = []
    
    # Check terrain strategy for themes
    terrain_strategy = adjustments.get('map_adjustments', {}).get('terrain_strategy', '')
    if 'forest' in terrain_strategy.lower():
        themes.append('Forest')
    if 'water' in terrain_strategy.lower():
        themes.append('Lake')
    if 'sand' in terrain_strategy.lower():
        themes.append('Desert')
    if 'rock' in terrain_strategy.lower():
        themes.append('Mountain')
    
    # Check difficulty level for intensity
    difficulty = adjustments.get('difficulty_adjustment', {}).get('new_difficulty', 50)
    if difficulty >= 80:
        themes.append('Master')
    elif difficulty >= 60:
        themes.append('Advanced')
    elif difficulty <= 30:
        themes.append('Training')
    
    # Check wave adjustments for duration themes
    wave_modifier = adjustments.get('wave_adjustments', {}).get('total_waves_modifier', 1.0)
    if wave_modifier > 1.1:
        themes.append('Marathon')
    elif wave_modifier < 0.7:
        themes.append('Sprint')
    
    return ' '.join(themes) if themes else 'Challenge' 