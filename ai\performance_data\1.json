{"win_flag": false, "lives_remaining": 0, "starting_lives": 17, "towers_built": {"laser": 3, "ice": 5, "detector": 2, "poison": 2, "antiair": 7, "flame": 1, "splash": 2, "lightning": 9, "explosive": 2}, "tower_diversity": 9, "wave_reached": 16, "final_wave": 16, "survival_rate": 0.0, "score": 42.0, "difficulty_adjusted_score": 42.0, "previous_config_details": null, "starting_money": 20, "money_remaining": 2100, "total_money_spent": 3837, "total_money_earned": 5636, "game_duration_seconds": 343.86546206474304, "average_fps": 60, "most_built_tower_type": "lightning", "tower_build_order": ["basic", "laser", "ice", "detector", "detector", "poison", "antiair", "ice", "flame", "antiair", "poison", "splash", "splash", "lightning", "lightning", "lightning", "lightning", "lightning", "lightning", "laser", "laser", "antiair", "lightning", "antiair", "antiair", "antiair", "explosive", "explosive", "antiair", "lightning", "lightning", "lightning", "lightning", "lightning", "lightning", "lightning", "ice", "ice", "ice"], "economic_efficiency": 1.4688558769872295, "resource_management_score": 40.840872804683336, "defeat_cause": "mid_game_scaling", "tower_effectiveness_analysis": {"laser": {"count": 3, "usage_percentage": 9.090909090909092, "effectiveness_rating": "low"}, "ice": {"count": 5, "usage_percentage": 15.151515151515152, "effectiveness_rating": "medium"}, "detector": {"count": 2, "usage_percentage": 6.0606060606060606, "effectiveness_rating": "low"}, "poison": {"count": 2, "usage_percentage": 6.0606060606060606, "effectiveness_rating": "low"}, "antiair": {"count": 7, "usage_percentage": 21.21212121212121, "effectiveness_rating": "medium"}, "flame": {"count": 1, "usage_percentage": 3.0303030303030303, "effectiveness_rating": "low"}, "splash": {"count": 2, "usage_percentage": 6.0606060606060606, "effectiveness_rating": "low"}, "lightning": {"count": 9, "usage_percentage": 27.27272727272727, "effectiveness_rating": "medium"}, "explosive": {"count": 2, "usage_percentage": 6.0606060606060606, "effectiveness_rating": "low"}}, "critical_decision_points": [{"phase": "early_game", "wave_range": "1-5", "decision": "initial_tower_selection", "outcome": "successful"}], "adaptation_patterns": {"tower_diversity": 10, "build_pattern": "diverse", "strategy_changes": 19, "adaptability": "high"}, "enemy_type_struggles": null, "strategic_mistakes": ["unused_resources"], "successful_strategies": [], "config_difficulty_score": 70, "config_difficulty_description": "Hard - Challenging configuration requiring strategy", "config_difficulty_components": {"starting_resources": 25.0, "enemy_scaling": 22.5, "spawn_progression": 10.3, "special_rounds": 8.6, "tower_economics": 4.0}, "level_metadata": {"name": "Infernal Gauntlet", "description": "A brutal 80-wave challenge that pushes even experienced defenders to their limits. Face relentless swarms of enemies with minimal starting resources, where every decision counts and strategic tower placement is crucial for survival. Only the most skilled commanders will emerge victorious from this hellish battlefield.", "difficulty_rating": "Hard", "estimated_duration": "45-60 minutes", "recommended_for": "experienced players", "special_features": ["80 challenging waves", "Low starting resources (20 money, 20 lives)", "High enemy scaling and boss density", "Advanced enemy types with special abilities", "Dynamic enemy buff system"], "victory_rewards": {"victory_points": 11, "victory_points_description": "<PERSON>arn 11 Victory Points for completing this challenging level", "unlock_requirements": [], "completion_bonuses": ["Unlock advanced tower upgrades", "Access to elite enemy strategies", "Prestigious completion achievement"]}, "tips": ["Prioritize economy early with basic towers", "Save money for critical anti-air defenses", "Use detector towers to counter invisible enemies", "Plan for multiple boss encounters"]}, "waves_completed_successfully": 16, "total_waves_in_config": 80, "progression_percentage": 20.0, "buff_encounters": null, "buff_combinations_seen": null, "most_challenging_buffs": null, "tower_counter_effectiveness": null, "buff_adaptation_score": 0.0, "enemy_buff_intensity": "none", "buff_related_deaths": 0, "effective_counter_strategies": null, "config_file_path": "c:\\Users\\<USER>\\Documents\\GitHub\\tower_defence_game\\config/base\\tower_defense_game.json"}