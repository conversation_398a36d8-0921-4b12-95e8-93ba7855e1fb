import pygame
import math
from typing import List
from .projectile import Projectile

class FreezeProjectile(Projectile):
    """Projectile that completely freezes a single targeted enemy"""
    def __init__(self, start_x: float, start_y: float, target_x: float, target_y: float,
                 speed: float, damage: int, tower_type: str, freeze_duration: int, complete_freeze: bool = False):
        super().__init__(start_x, start_y, target_x, target_y, speed, damage, tower_type)
        self.freeze_duration = freeze_duration
        self.complete_freeze = complete_freeze
        self.size = 8
        self.color = (100, 200, 255)  # Light blue, different from ice projectile
    
    def check_collision(self, enemies: List) -> dict:
        """Apply complete freeze effect to single targeted enemy after 50 hits"""
        for enemy in enemies:
            distance = math.sqrt((self.x - enemy.x)**2 + (self.y - enemy.y)**2)
            if distance < (self.size + enemy.size):
                # Increment freeze hit counter instead of freezing immediately
                if hasattr(enemy, 'register_freeze_hit'):
                    enemy.register_freeze_hit(self.freeze_duration, self.complete_freeze)
                # Deal damage if any
                actual_damage = 0
                if self.damage > 0:
                    actual_damage = enemy.take_damage(self.damage, self.tower_type)
                self.should_remove = True
                return {'hit': True, 'damage': actual_damage, 'tower_id': self.source_tower_id}
        
        # Check if projectile reached target area (for missed shots)
        target_distance = math.sqrt((self.x - self.target_x)**2 + (self.y - self.target_y)**2)
        if target_distance < 10:  # Close enough to target
            self.should_remove = True
            return {'hit': False, 'damage': 0, 'tower_id': None}
        
        return {'hit': False, 'damage': 0, 'tower_id': None}
    
    def draw(self, screen: pygame.Surface):
        """Draw freeze projectile with special effect"""
        # Draw main projectile
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), self.size)
        # Draw inner glow
        pygame.draw.circle(screen, (255, 255, 255), (int(self.x), int(self.y)), self.size - 3)
        
        # Draw freeze trail
        trail_length = 5
        for i in range(trail_length):
            trail_x = self.x - self.velocity_x * i * 0.1
            trail_y = self.y - self.velocity_y * i * 0.1
            trail_alpha = (trail_length - i) / trail_length
            trail_color = (int(self.color[0] * trail_alpha), 
                          int(self.color[1] * trail_alpha), 
                          int(self.color[2] * trail_alpha))
            pygame.draw.circle(screen, trail_color, (int(trail_x), int(trail_y)), 3) 