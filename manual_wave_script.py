# Manual Wave Start Implementation

# Read the wave manager file
with open('game_systems/wave_manager.py', 'r') as f:
    content = f.read()

# 1. Add manual wave variables
content = content.replace(
    '        self.wave_complete = False',
    '''        self.wave_complete = False
        
        # Manual wave start system
        self.waiting_for_next_wave = False
        self.next_wave_ready = False
        self.wave_in_progress = True  # First wave starts immediately'''
)

# 2. Add manual wave methods
methods = '''
    def can_start_next_wave(self) -> bool:
        \"\"\"Check if player can start the next wave\"\"\"
        return self.waiting_for_next_wave and not self.has_active_introduction()
    
    def start_next_wave_manual(self) -> dict:
        \"\"\"Manually start the next wave when player is ready\"\"\"
        if not self.can_start_next_wave():
            return None
        self.waiting_for_next_wave = False
        self.next_wave_ready = False
        self.wave_in_progress = True
        return self.start_next_wave()
    
    def set_waiting_for_next_wave(self):
        \"\"\"Set state to wait for player to start next wave\"\"\"
        self.waiting_for_next_wave = True
        self.next_wave_ready = True
        self.wave_in_progress = False

'''

content = content.replace(
    '    def draw_introduction(self, screen):',
    methods + '    def draw_introduction(self, screen):'
)

# 3. Modify wave completion
content = content.replace(
    '                    # Start next wave after a brief delay\n                    # This will be handled by the game loop\n                    pass',
    '                    # Wait for manual wave start\n                    self.set_waiting_for_next_wave()'
)

# Write the updated file
with open('game_systems/wave_manager.py', 'w') as f:
    f.write(content)


