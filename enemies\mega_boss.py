from .enemy import Enemy
import pygame
import math
import random


class MegaBoss(Enemy):
    """Massive boss enemy with multiple phases and abilities"""

    def __init__(self, path, wave_number=1):
        super().__init__(path, wave_number)
        self.health = 500  # Reduced from 1500 to 1/3
        self.max_health = 500
        self.speed = 0.5
        self.reward = 400
        self.color = (128, 0, 128)  # Purple
        self.size = 25
        self.damage = 2  # MegaBoss deals 2 damage to player

        # Boss-specific properties
        self.phase = 1
        self.max_phases = 3
        self.damage_reduction = 0.4
        self.ability_timer = 0
        self.ability_cooldown = 300  # 5 seconds
        self.minion_spawn_timer = 0
        self.minion_spawn_cooldown = 600  # 10 seconds

        # BALANCE FIX: Override immunities to ensure freeze effects can work
        # Boss<PERSON> should never be completely immune to crowd control
        self.immunities['freeze_immune'] = False

        # Visual effects
        self.pulse_timer = 0
        self.aura_radius = 30

    def update(self):
        """Update boss with special abilities"""
        # Update phase based on health BEFORE movement
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            self.phase = 1
        elif health_percentage > 0.33:
            self.phase = 2
        else:
            self.phase = 3

        # Phase-based speed increase (calculate BEFORE movement)
        self.speed = 0.5 + (self.phase - 1) * 0.2

        # Now call parent update with correct speed
        super().update()

        # Update ability timers
        self.ability_timer += 1
        self.minion_spawn_timer += 1
        self.pulse_timer += 0.1

    def update_with_speed(self, speed_multiplier: float):
        """Update with speed multiplier for performance optimization"""
        # Update phase based on health BEFORE movement
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            self.phase = 1
        elif health_percentage > 0.33:
            self.phase = 2
        else:
            self.phase = 3

        # Phase-based speed increase (calculate BEFORE movement)
        self.speed = 0.5 + (self.phase - 1) * 0.2

        # Call parent update_with_speed with correct speed
        super().update_with_speed(speed_multiplier)

        # Update ability timers with speed multiplier
        self.ability_timer += speed_multiplier
        self.minion_spawn_timer += speed_multiplier
        self.pulse_timer += 0.1 * speed_multiplier

    def take_damage(self, damage, tower_type: str = 'basic'):
        """Take reduced damage"""
        # Apply 3x damage multiplier for frozen enemies
        if self.frozen:
            damage = int(damage * 3.0)

        reduced_damage = damage * (1 - self.damage_reduction)
        # Can't deal more than remaining health
        actual_damage = min(reduced_damage, self.health)
        self.health -= reduced_damage
        return actual_damage

    def should_spawn_minions(self):
        """Check if boss should spawn minions - returns boolean"""
        if self.minion_spawn_timer >= self.minion_spawn_cooldown:
            self.minion_spawn_timer = 0
            return True
        return False

    def spawn_minions(self):
        """Spawn minions on the path like splitting enemy does - spawns from ALL enemy types except bosses"""
        import random
        print(
            f"MegaBoss spawning minions at path_index {self.path_index}, pos ({self.x:.1f}, {self.y:.1f})")

        # Start with basic enemies that we know work
        try:
            from . import (BasicEnemy, FastEnemy, TankEnemy, ShieldedEnemy,
                           InvisibleEnemy, FlyingEnemy, RegeneratingEnemy,
                           SplittingEnemy, TeleportingEnemy)
            basic_minions = [BasicEnemy, FastEnemy, TankEnemy, ShieldedEnemy,
                             InvisibleEnemy, FlyingEnemy, RegeneratingEnemy,
                             SplittingEnemy, TeleportingEnemy]
        except ImportError:
            # Fallback to just basic enemies if others fail
            from . import BasicEnemy, FastEnemy, TankEnemy
            basic_minions = [BasicEnemy, FastEnemy, TankEnemy]

        # Try to add advanced enemies
        try:
            from . import (ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy,
                           FireElementalEnemy, ToxicEnemy, PhaseShiftEnemy,
                           BlastProofEnemy, SpectralEnemy, CrystallineEnemy,
                           ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy)
            advanced_minions = [ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy,
                                FireElementalEnemy, ToxicEnemy, PhaseShiftEnemy,
                                BlastProofEnemy, SpectralEnemy, CrystallineEnemy,
                                ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy]
            available_minions = basic_minions + advanced_minions
        except ImportError:
            # Use only basic enemies if advanced ones fail
            available_minions = basic_minions

        spawned_minions = []
        minion_count = self.get_minion_count()

        for i in range(minion_count):
            minion_class = random.choice(available_minions)
            new_minion = minion_class(self.path, self.wave_number)

            # CRITICAL: Position minion at boss location on the path with front/back variation
            # Start with boss's path position
            new_minion.path_index = self.path_index
            new_minion.distance_traveled = self.distance_traveled

            # Randomly place minions slightly ahead or behind the boss along the path
            # Can be 30 pixels ahead or behind
            path_offset = random.uniform(-30, 30)
            new_minion.distance_traveled += path_offset

            # Also add small perpendicular offset for visual variety
            offset_x = random.uniform(-12, 12)
            offset_y = random.uniform(-12, 12)
            new_minion.x = self.x + offset_x
            new_minion.y = self.y + offset_y

            # If we have a valid path, try to position along it more accurately
            if len(self.path) > 1 and self.path_index < len(self.path) - 1:
                # Calculate position along path based on distance_traveled offset
                if path_offset > 0:  # Ahead of boss
                    # Try to move forward along path
                    target_distance = self.distance_traveled + abs(path_offset)
                    # Simple approximation: move toward next waypoint
                    next_point = self.path[min(
                        self.path_index + 1, len(self.path) - 1)]
                    # Assume ~25 pixels per segment
                    progress = min(abs(path_offset) / 25.0, 1.0)
                    new_minion.x = self.x + \
                        (next_point[0] - self.x) * progress + offset_x
                    new_minion.y = self.y + \
                        (next_point[1] - self.y) * progress + offset_y
                elif path_offset < 0:  # Behind boss
                    # Try to move backward along path
                    if self.path_index > 0:
                        prev_point = self.path[self.path_index - 1]
                        progress = min(abs(path_offset) / 25.0, 1.0)
                        new_minion.x = self.x + \
                            (prev_point[0] - self.x) * progress + offset_x
                        new_minion.y = self.y + \
                            (prev_point[1] - self.y) * progress + offset_y

            # Make minions slightly weaker than normal (like splitting enemy)
            # Same as splitting enemy
            new_minion.health = max(1, int(new_minion.health * 0.75))
            new_minion.max_health = new_minion.health
            new_minion.reward = int(new_minion.reward * 0.4)

            # Ensure they inherit path state properly (like splitting enemy)
            new_minion.reached_end = False

            # Visual indicator that this is a boss minion
            new_minion.color = (150, 50, 150)  # Purple tint

            spawned_minions.append(new_minion)

        return spawned_minions

    def get_minion_count(self):
        """Get number of minions to spawn based on phase"""
        return self.phase * 2

    def draw(self, screen):
        """Draw the mega boss with phase-based effects"""
        # Draw pulsing aura
        aura_size = self.aura_radius + math.sin(self.pulse_timer) * 5
        aura_color = [128, 0, 128, 30]  # Semi-transparent purple

        # Phase-based aura color
        if self.phase == 2:
            aura_color = [255, 128, 0, 40]  # Orange
        elif self.phase == 3:
            aura_color = [255, 0, 0, 50]  # Red

        # Draw main boss body
        boss_color = self.color
        if self.phase == 2:
            boss_color = (255, 128, 0)  # Orange
        elif self.phase == 3:
            boss_color = (255, 0, 0)  # Red

        pygame.draw.circle(screen, boss_color,
                           (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, (255, 255, 255),
                           (int(self.x), int(self.y)), self.size, 3)

        # Draw phase indicators
        for i in range(self.phase):
            angle = (i * 120) * math.pi / 180
            indicator_x = self.x + math.cos(angle) * (self.size - 5)
            indicator_y = self.y + math.sin(angle) * (self.size - 5)
            pygame.draw.circle(screen, (255, 255, 0),
                               (int(indicator_x), int(indicator_y)), 4)

        # Draw health bar (larger for boss)
        bar_width = self.size * 3
        bar_height = 8

        # Background
        pygame.draw.rect(screen, (100, 0, 0),
                         (self.x - bar_width//2, self.y - self.size - 20, bar_width, bar_height))

        # Health
        health_percentage = self.health / self.max_health
        pygame.draw.rect(screen, (255, 0, 0),
                         (self.x - bar_width//2, self.y - self.size - 20,
                          int(bar_width * health_percentage), bar_height))

        # Phase markers on health bar
        for i in range(1, self.max_phases):
            marker_x = self.x - bar_width//2 + \
                (bar_width * (self.max_phases - i) / self.max_phases)
            pygame.draw.line(screen, (255, 255, 255),
                             (marker_x, self.y - self.size - 20),
                             (marker_x, self.y - self.size - 12), 2)

        # Draw boss title
        font = pygame.font.Font(None, 24)
        title_text = font.render("MEGA BOSS", True, (255, 255, 255))
        title_rect = title_text.get_rect(
            center=(self.x, self.y - self.size - 35))
        screen.blit(title_text, title_rect)
