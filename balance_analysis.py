"""
Mathematical Balance Analysis and Formulas for Tower Defense Game
================================================================

This file contains the mathematical formulas and analysis used to balance
towers and enemies in the tower defense game.

BALANCE PRINCIPLES:
==================

1. Cost-Effectiveness Balance:
   - All towers should have similar cost/DPS ratios within their tier
   - Specialized towers can have higher costs for unique abilities
   - Support towers are valued by utility, not DPS

2. Enemy Progression:
   - Health should scale exponentially with difficulty
   - Speed should vary to create tactical diversity
   - Rewards should scale with difficulty but not linearly

3. Tower Tiers:
   - Tier 1 (Basic, Freezer, Detector): Cost 15-30, Basic functionality
   - Tier 2 (<PERSON><PERSON><PERSON>, <PERSON>ison, AntiAir, Lightning, Flame): Cost 35-55, Specialized
   - Tier 3 (<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>): Cost 60-100, Advanced
   - Tier 4 (Explosive, Splash, Destroyer): Cost 100+, Elite

MATHEMATICAL FORMULAS:
=====================

Tower Cost Formula:
cost = base_tier_cost * (1 + range_factor + damage_factor + special_factor)

Where:
- base_tier_cost: 20, 40, 70, 110 for tiers 1-4
- range_factor = (range - 100) / 500  # Normalized range bonus
- damage_factor = damage / 30  # Normalized damage bonus  
- special_factor = 0.2-0.8 depending on special abilities

Tower DPS Formula:
dps = damage * (60 / fire_rate)  # 60 FPS base

Target Cost/DPS Ratios:
- Tier 1: 3.0-4.0
- Tier 2: 2.0-3.0  
- Tier 3: 1.5-2.5
- Tier 4: 1.0-2.0

Enemy Health Formula:
health = base_health * (1.3 ^ difficulty_tier) * special_multiplier

Where:
- base_health = 2 for basic enemies
- difficulty_tier = 0-10 scale
- special_multiplier = 0.5-3.0 based on abilities

Enemy Speed Formula:
speed = base_speed * speed_multiplier * (1 - armor_penalty)

Where:
- base_speed = 1.0
- speed_multiplier = 0.3-3.0 based on enemy type
- armor_penalty = 0-0.7 for heavily armored enemies

Enemy Reward Formula:
reward = (health * speed * 2) + special_bonus

CURRENT IMBALANCES IDENTIFIED:
=============================

1. Lightning Tower: Extremely poor cost/DPS (14.29) - needs major buff
2. Cannon Tower: Too cost-effective (1.33) - slight nerf needed
3. Sniper Tower: Very cost-effective (1.71) - slight cost increase
4. Basic enemies: Too weak for progression
5. Tank enemies: Armor not strong enough
6. Boss health scaling: Inconsistent

PROPOSED CHANGES:
================

Tower Adjustments:
- Lightning: Reduce fire_rate to 80, increase damage to 10
- Cannon: Increase cost to 75
- Sniper: Increase cost to 50
- Explosive: Reduce cost to 85
- Missile: Reduce cost to 90

Enemy Adjustments:
- Basic: Health 1→2, Reward 4→5
- Fast: Health 1→2, Speed 2.5→2.2, Reward 6→7
- Tank: Health 5→8, Armor 20%→30%, Reward 12→15
- Flying: Health 10→12, Reward 10→12
- Armored: Health 18→25, Reward 15→20

Boss Adjustments:
- SpeedBoss: Health 600→400 (more manageable)
- CrystalOverlord: Health 1167→800 (more reasonable)
"""

import math

def calculate_tower_dps(damage, fire_rate):
    """Calculate DPS for a tower"""
    return damage * (60 / fire_rate) if fire_rate > 0 else 0

def calculate_cost_effectiveness(cost, damage, fire_rate):
    """Calculate cost per DPS"""
    dps = calculate_tower_dps(damage, fire_rate)
    return cost / dps if dps > 0 else float('inf')

def calculate_balanced_cost(damage, range_val, fire_rate, tier, special_factor=0.0):
    """Calculate balanced cost for a tower"""
    base_costs = {1: 20, 2: 40, 3: 70, 4: 110}
    base_cost = base_costs.get(tier, 40)
    
    range_factor = (range_val - 100) / 500
    damage_factor = damage / 30
    
    return int(base_cost * (1 + range_factor + damage_factor + special_factor))

def calculate_enemy_health(base_health, difficulty_tier, special_multiplier=1.0):
    """Calculate balanced enemy health"""
    return int(base_health * (1.3 ** difficulty_tier) * special_multiplier)

def calculate_enemy_reward(health, speed, special_bonus=0):
    """Calculate balanced enemy reward"""
    return int((health * speed * 2) + special_bonus)

# Example calculations for verification
if __name__ == "__main__":
    print("=== TOWER BALANCE ANALYSIS ===")
    
    # Current tower stats
    towers = [
        ("Basic", 20, 2, 80, 25),
        ("Sniper", 40, 35, 250, 90),
        ("Lightning", 50, 7, 120, 120),
        ("Cannon", 60, 45, 250, 60),
    ]
    
    for name, cost, damage, range_val, fire_rate in towers:
        dps = calculate_tower_dps(damage, fire_rate)
        cost_eff = calculate_cost_effectiveness(cost, damage, fire_rate)
        print(f"{name}: DPS={dps:.1f}, Cost/DPS={cost_eff:.2f}")
