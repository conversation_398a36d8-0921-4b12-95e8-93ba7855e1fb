# Rock Removal Rewards System

## Overview

The Rock Removal Rewards System adds exciting **wave-based** rewards when players remove rocks from the map. This system provides strategic depth and additional benefits for investing in terrain clearing, with rewards triggered at specific milestones per wave.

## Features

### 🎁 Wave-Based Reward System

The system tracks rocks removed per wave and triggers rewards at specific milestones:

1. **Tower Boost (2 rocks removed)**
   - Increases all tower damage by 50% for 60 seconds
   - Applies to existing towers and any new towers built during the boost
   - **Message**: "⚡ TOWER BOOST! (2 rocks removed) +50% damage for 60s"
   - Can only be activated once per wave

2. **Enemy Boost (3 rocks removed)**
   - Increases all enemy speed by 30% for 30 seconds
   - Applies to existing enemies and any new enemies spawned during the boost
   - **Message**: "🏃 ENEMY BOOST! (3 rocks removed) +30% speed for 30s"
   - Can only be activated once per wave

4. **Progress Tracking**
   - Shows current rock count for the wave
   - Displays upcoming milestones
   - **Message**: "💎 Rock X removed this wave"

### 🎮 In-Game Messages

- **Visual Feedback**: All rewards display colorful messages on screen
- **Fade Effect**: Messages fade out over 3 seconds
- **Stacking**: Multiple messages can appear simultaneously
- **Centered Display**: Messages appear at the bottom center of the screen

### 🔧 Testing & Debug Features

#### Debug Mode Controls
- **F2**: Toggle debug mode on/off
- **R**: Force a random reward (debug mode only)
- **1**: Force tower boost (debug mode only)
- **2**: Force enemy boost (debug mode only)
- **T**: Test rewards system (debug mode only)
- **0**: Disable test mode (debug mode only)

#### Debug Mode Features
- **Visual Indicator**: "DEBUG MODE ACTIVE" appears on screen
- **Console Output**: Detailed information printed to console
- **Statistics Tracking**: Real-time stats about rock removals and rewards
- **Force Testing**: Ability to test specific reward types

## Technical Implementation

### Core Classes

#### `RockRemovalRewards`
- **Location**: `game_systems/rock_removal_rewards.py`
- **Purpose**: Central management of all rewards and effects
- **Key Methods**:
  - `apply_rewards()`: Main reward application logic
  - `update_effects()`: Update timers and effects
  - `draw_effects()`: Render visual indicators and messages
  - `enable_debug_mode()`: Enable testing features

#### Tower Integration
- **Location**: `towers/tower.py`
- **Methods Added**:
  - `apply_damage_boost(multiplier)`: Apply damage boost
  - `remove_damage_boost()`: Remove boost and restore normal damage

#### Enemy Integration
- **Location**: `enemies/enemy.py`
- **Methods Added**:
  - `apply_speed_boost(multiplier)`: Apply speed boost
  - `remove_speed_boost()`: Remove boost and restore normal speed

### Game Integration

#### Main Game Loop
- **Location**: `game.py`
- **Integration Points**:
  - Rock removal attempts trigger reward system
  - New towers/enemies automatically get current boosts
  - Visual effects rendered in main draw loop
  - Debug controls handled in keyboard input

## Testing

### Standalone Test Script
```bash
python test_rock_removal_rewards.py
```

The test script demonstrates:
- Normal reward application (random)
- Forced reward testing
- Visual message system
- Statistics tracking
- Debug information

### In-Game Testing
1. Start the game
2. Press **F2** to enable debug mode
3. Use debug controls to test specific features:
   - **R**: Test random rewards
   - **1/2/3**: Test specific reward types
   - **T**: Run comprehensive test
4. Watch console output for detailed information

## Configuration

### Wave-Based Milestones
```python
self.tower_boost_milestone = 2    # Tower boost at 2 rocks removed
self.enemy_boost_milestone = 3    # Enemy boost at 3 rocks removed
```

### Duration Settings
```python
self.tower_boost_duration = 3600  # 60 seconds at 60 FPS
self.enemy_boost_duration = 1800  # 30 seconds at 60 FPS
```

### Boost Multipliers
```python
self.tower_boost_multiplier = 1.5  # 50% damage increase
self.enemy_boost_multiplier = 1.3  # 30% speed increase
```

## Visual Effects

### On-Screen Indicators
- **Tower Boost**: Yellow "TOWER BOOST: Xs" with countdown
- **Enemy Boost**: Red "ENEMY BOOST: Xs" with countdown
- **Debug Mode**: Purple "DEBUG MODE ACTIVE"

### Message System
- **Position**: Bottom center of screen
- **Background**: Semi-transparent black for readability
- **Fade**: Messages fade out over 3 seconds
- **Stacking**: Multiple messages stack vertically

## Performance Considerations

- **Minimal Overhead**: Lightweight implementation with minimal performance impact
- **Efficient Updates**: Only active effects are processed
- **Memory Management**: Messages are automatically cleaned up
- **Optimized Rendering**: Font initialization is cached

## Future Enhancements

### Potential Additions
- **More Reward Types**: Currency bonuses, tower range boosts, temporary shields, etc.
- **Combo System**: Multiple rock removals in quick succession
- **Achievement System**: Track total rocks removed and rewards earned
- **Visual Effects**: Particle effects for reward activations
- **Sound Effects**: Audio feedback for rewards

### Configuration Options
- **Difficulty Scaling**: Adjust probabilities based on game difficulty
- **Wave Progression**: Change rewards based on current wave
- **Player Choice**: Allow players to choose reward types
- **Custom Messages**: Player-configurable message text

## Troubleshooting

### Common Issues
1. **Debug mode not working**: Ensure F2 is pressed to enable debug mode
2. **Messages not appearing**: Check that the game is not paused or in menu
3. **Boosts not applying**: Verify towers/enemies have the required methods
4. **Console spam**: Disable debug mode with F2

### Debug Information
Use the `get_debug_info()` method to get comprehensive system status:
```python
debug_info = rewards.get_debug_info()
print(debug_info)
```

## Contributing

When adding new reward types:
1. Add the reward logic to `apply_rewards()`
2. Create appropriate activation/deactivation methods
3. Add visual indicators to `draw_effects()`
4. Update the test script to include new rewards
5. Document the new feature in this README 