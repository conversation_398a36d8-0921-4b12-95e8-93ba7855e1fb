"""
Event Handler for Tower Defense Game Launcher
Handles all pygame events and user input
"""

import pygame
import time
from typing import Dict, List, Any, Optional, Callable


class EventHandler:
    """Handles all pygame events and user input for the launcher"""
    
    def __init__(self, screen_width: int, screen_height: int):
        """Initialize the event handler"""
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # Visual feedback state tracking
        self.pressed_button = None  # Track which button is currently pressed
        self.pressed_card = None    # Track which card is currently pressed
        self.button_press_time = 0  # Time when button was pressed
        self.card_press_time = 0    # Time when card was pressed
        
        # Button coordinates for click detection
        self.button_coords = {}
        
        # Callback functions (to be set by the main launcher)
        self.on_generate_config = None
        self.on_toggle_stats = None
        self.on_toggle_upgrades = None
        self.on_create_variant = None
        self.on_launch_game = None
        self.on_navigate_to_main = None
        self.on_navigate_to_variants = None
        self.on_navigate_to_level_options = None
        self.on_open_level_preview = None
        self.on_close_level_preview = None
        self.on_variant_creation = None
        self.on_dismiss_status = None
    
    def set_button_coords(self, coords: Dict):
        """Set button coordinates for click detection"""
        self.button_coords = coords
    
    def handle_events(self, navigation_manager, config_manager, variant_ui=None, global_upgrade_ui=None):
        """Handle launcher events"""
        events_handled = []
        
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                events_handled.append(('quit',))
            
            elif event.type == pygame.KEYDOWN:
                self._handle_keydown(event, navigation_manager, config_manager)
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    self._handle_mouse_click(event.pos, navigation_manager, config_manager, variant_ui, global_upgrade_ui, button=1)
                elif event.button == 3:  # Right click
                    self._handle_mouse_click(event.pos, navigation_manager, config_manager, variant_ui, global_upgrade_ui, button=3)
                elif event.button == 4:  # Mouse wheel up
                    self._handle_scroll(-1, navigation_manager, variant_ui, global_upgrade_ui)
                elif event.button == 5:  # Mouse wheel down
                    self._handle_scroll(1, navigation_manager, variant_ui, global_upgrade_ui)
            
            elif event.type == pygame.MOUSEWHEEL:
                self._handle_scroll(event.y, navigation_manager, variant_ui, global_upgrade_ui)
        
        return events_handled
    
    def _handle_keydown(self, event, navigation_manager, config_manager):
        """Handle keyboard input"""
        if event.key == pygame.K_UP:
            if navigation_manager.show_level_preview:
                navigation_manager.preview_scroll_offset = max(0, navigation_manager.preview_scroll_offset - 1)
            elif navigation_manager.selected_config is None:
                navigation_manager.selected_config = 0
            else:
                navigation_manager.selected_config = max(0, navigation_manager.selected_config - 1)
            navigation_manager.update_scroll()
            
        elif event.key == pygame.K_DOWN:
            if navigation_manager.show_level_preview:
                navigation_manager.preview_scroll_offset = min(navigation_manager.preview_max_scroll, navigation_manager.preview_scroll_offset + 1)
            elif navigation_manager.selected_config is None:
                navigation_manager.selected_config = 0
            else:
                current_configs = config_manager.get_current_config_list(navigation_manager)
                navigation_manager.selected_config = min(len(current_configs) - 1, navigation_manager.selected_config + 1)
            navigation_manager.update_scroll()
            
        elif event.key == pygame.K_RETURN or event.key == pygame.K_SPACE:
            if navigation_manager.show_level_preview and navigation_manager.preview_config:
                if self.on_launch_game:
                    self.on_launch_game(navigation_manager.preview_config)
            elif navigation_manager.current_view == "level_options" and navigation_manager.level_options_config:
                # In level options view, enter/space triggers "Play Original"
                navigation_manager.open_level_preview(navigation_manager.level_options_config)
            else:
                current_configs = config_manager.get_current_config_list(navigation_manager)
                if navigation_manager.selected_config is not None and 0 <= navigation_manager.selected_config < len(current_configs):
                    config = current_configs[navigation_manager.selected_config]
                    if navigation_manager.current_view == "main":
                        # Main menu card selected - open level options
                        if self.on_navigate_to_level_options:
                            self.on_navigate_to_level_options(config)
                    else:
                        # Variant in variants view - show preview
                        navigation_manager.open_level_preview(config)
                        
        elif event.key == pygame.K_ESCAPE:
            if navigation_manager.show_variant_selector:
                navigation_manager.close_variant_selector()
            elif navigation_manager.show_level_preview:
                navigation_manager.close_level_preview()
            elif navigation_manager.show_upgrade_menu:
                navigation_manager.show_upgrade_menu = False
            elif navigation_manager.current_view == "level_options":
                # Go back to main view from level options
                if self.on_navigate_to_main:
                    self.on_navigate_to_main()
            elif navigation_manager.current_view == "variants":
                # Go back to main view from variants
                if self.on_navigate_to_main:
                    self.on_navigate_to_main()
            else:
                return [('quit',)]
                
        elif event.key == pygame.K_g:
            if not navigation_manager.show_level_preview and not navigation_manager.show_upgrade_menu:
                # Generate new adaptive config from recent games (multi-game analysis)
                if self.on_generate_config:
                    self.on_generate_config()
                    
        elif event.key == pygame.K_p:
            if not navigation_manager.show_level_preview and not navigation_manager.show_upgrade_menu and not navigation_manager.show_variant_selector:
                # Toggle performance panel
                navigation_manager.toggle_performance_panel()
                
        elif event.key == pygame.K_v:
            if not navigation_manager.show_level_preview and not navigation_manager.show_upgrade_menu and not navigation_manager.show_variant_selector:
                # Open variant selector for selected level
                if navigation_manager.selected_config is not None and 0 <= navigation_manager.selected_config < len(config_manager.configs):
                    config_info = config_manager.configs[navigation_manager.selected_config]
                    navigation_manager.open_variant_selector()
                    # Note: variant_ui.open_for_level would be called by the main launcher
        
        return []
    
    def _handle_scroll(self, direction, navigation_manager, variant_ui=None, global_upgrade_ui=None):
        """Handle scroll events"""
        if navigation_manager.show_variant_selector and variant_ui:
            variant_ui.handle_scroll(direction)
        elif navigation_manager.show_level_preview:
            if direction > 0:
                navigation_manager.preview_scroll_offset = max(0, navigation_manager.preview_scroll_offset - 1)
            else:
                navigation_manager.preview_scroll_offset = min(navigation_manager.preview_max_scroll, navigation_manager.preview_scroll_offset + 1)
        elif navigation_manager.show_upgrade_menu and global_upgrade_ui:
            global_upgrade_ui.handle_scroll(direction)
        else:
            if direction > 0:
                navigation_manager.scroll_offset = max(0, navigation_manager.scroll_offset - 1)
            else:
                navigation_manager.scroll_offset = min(navigation_manager.max_scroll, navigation_manager.scroll_offset + 1)
    
    def _handle_mouse_click(self, pos, navigation_manager, config_manager, variant_ui=None, global_upgrade_ui=None, button=1):
        """Handle mouse clicks
        Args:
            pos: Mouse position tuple
            navigation_manager: Navigation manager instance
            config_manager: Config manager instance
            variant_ui: Variant UI instance (optional)
            global_upgrade_ui: Global upgrade UI instance (optional)
            button: Mouse button (1=left, 3=right)
        """
        x, y = pos
        
        # Check if status message is showing and dismiss it
        if navigation_manager.show_generation_status and navigation_manager.generation_message:
            if self.on_dismiss_status:
                self.on_dismiss_status()
            return
        
        # Handle different UI overlays
        if navigation_manager.show_variant_selector and variant_ui:
            action = variant_ui.handle_click(pos, button)
            if action == "create_variant":
                if self.on_variant_creation:
                    self.on_variant_creation()
            elif action == "close":
                navigation_manager.close_variant_selector()
                
        elif navigation_manager.show_level_preview:
            self._handle_preview_click(pos, navigation_manager)
            
        elif navigation_manager.show_upgrade_menu and global_upgrade_ui:
            global_upgrade_ui.handle_click(pos)
            
        else:
            # Handle main UI clicks
            self._handle_main_ui_click(pos, navigation_manager, config_manager)
    
    def _handle_preview_click(self, pos, navigation_manager):
        """Handle mouse clicks in the level preview screen"""
        x, y = pos
        
        # Check for back button click (top-left)
        if 50 <= x <= 150 and 50 <= y <= 80:
            navigation_manager.close_level_preview()
            return
        
        # Check for play button click (center-bottom) - match the drawing coordinates exactly
        play_btn_width = 200
        play_btn_height = 50
        play_btn_x = self.screen_width // 2 - play_btn_width // 2
        play_btn_y = self.screen_height - 120
        if play_btn_x <= x <= play_btn_x + play_btn_width and play_btn_y <= y <= play_btn_y + play_btn_height:
            if navigation_manager.preview_config and self.on_launch_game:
                self.on_launch_game(navigation_manager.preview_config)
            return

    def _handle_main_ui_click(self, pos, navigation_manager, config_manager):
        """Handle clicks on the main UI"""
        x, y = pos

        # Check button clicks first (should work in all views since buttons are always visible)
        if self.button_coords:
            # Generate button
            if 'generate' in self.button_coords:
                btn_x, btn_y, btn_w, btn_h = self.button_coords['generate']
                if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                    self.pressed_button = 'generate'
                    self.button_press_time = time.time()

                    if self.on_generate_config:
                        self.on_generate_config()
                    return

            # Stats button
            if 'stats' in self.button_coords:
                btn_x, btn_y, btn_w, btn_h = self.button_coords['stats']
                if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                    self.pressed_button = 'stats'
                    self.button_press_time = time.time()

                    if self.on_toggle_stats:
                        self.on_toggle_stats()
                    return

            # Upgrade button
            if 'upgrades' in self.button_coords:
                btn_x, btn_y, btn_w, btn_h = self.button_coords['upgrades']
                if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
                    self.pressed_button = 'upgrades'
                    self.button_press_time = time.time()

                    if self.on_toggle_upgrades:
                        self.on_toggle_upgrades()
                    return

        # Handle navigation buttons for different views
        if navigation_manager.current_view == "variants":
            # Check for back button click in variants view
            back_btn_x, back_btn_y = 50, 50
            back_btn_width, back_btn_height = 100, 35
            if back_btn_x <= x <= back_btn_x + back_btn_width and back_btn_y <= y <= back_btn_y + back_btn_height:
                if self.on_navigate_to_main:
                    self.on_navigate_to_main()
                return

        elif navigation_manager.current_view == "level_options":
            # Handle level options view clicks
            self._handle_level_options_click(pos, navigation_manager, config_manager)
            return

        # Check card clicks (this should happen for main view and variants view)
        self._handle_card_click(pos, navigation_manager, config_manager)

    def _handle_level_options_click(self, pos, navigation_manager, config_manager):
        """Handle clicks in the level options view"""
        x, y = pos
        if not navigation_manager.level_options_config:
            return

        # Back button
        back_btn_x, back_btn_y = 50, 50
        back_btn_width, back_btn_height = 100, 35
        if back_btn_x <= x <= back_btn_x + back_btn_width and back_btn_y <= y <= back_btn_y + back_btn_height:
            if self.on_navigate_to_main:
                self.on_navigate_to_main()
            return

        # Calculate button positions - MUST MATCH draw_level_options() coordinates!
        button_width = 200
        button_height = 50
        button_spacing = 30
        center_x = self.screen_width // 2

        # Level info card coordinates (from draw_level_options)
        card_height = 200
        card_y = 180
        buttons_y = card_y + card_height + 50

        # "Play Original" button - MATCHES drawing coordinates
        play_btn_x = center_x - button_width // 2
        play_btn_y = buttons_y
        if (play_btn_x <= x <= play_btn_x + button_width and
            play_btn_y <= y <= play_btn_y + button_height):
            # Launch the original level
            navigation_manager.open_level_preview(navigation_manager.level_options_config)
            return

        # "View Variants" button (only show if variants exist)
        base_level = config_manager.get_base_level_for_config(navigation_manager.level_options_config)
        has_variants = base_level in config_manager.variants and len(config_manager.variants[base_level]) > 0

        current_button_y = buttons_y + button_height + button_spacing

        if has_variants:
            variants_btn_x = center_x - button_width // 2
            variants_btn_y = current_button_y
            if (variants_btn_x <= x <= variants_btn_x + button_width and
                variants_btn_y <= y <= variants_btn_y + button_height):
                # Navigate to variants view
                if self.on_navigate_to_variants:
                    self.on_navigate_to_variants(base_level)
                return

            current_button_y += button_height + button_spacing

        # "Create Variant" button
        create_variant_btn_x = center_x - button_width // 2
        create_variant_btn_y = current_button_y
        if (create_variant_btn_x <= x <= create_variant_btn_x + button_width and
            create_variant_btn_y <= y <= create_variant_btn_y + button_height):
            # Open variant creation UI
            navigation_manager.open_variant_selector()
            # Note: variant_ui.open_for_level would be called by the main launcher
            return

    def _handle_card_click(self, pos, navigation_manager, config_manager):
        """Handle clicks on configuration cards"""
        x, y = pos

        current_configs = config_manager.get_current_config_list(navigation_manager)
        if not current_configs:
            return

        card_width = 250
        card_height = 120
        padding = 20
        margin = 40
        header_height = 100
        content_y = header_height + 20

        # In variants view, leave space for back button
        if navigation_manager.current_view == "variants":
            content_y += 50

        # Calculate grid layout
        cards_per_row = (self.screen_width - 2 * margin) // (card_width + padding)

        # Check if click is in the card area
        if y >= content_y:
            # Calculate which card was clicked
            relative_x = x - margin
            relative_y = y - content_y

            if relative_x >= 0 and relative_y >= 0:
                col = relative_x // (card_width + padding)
                row = relative_y // (card_height + padding)

                # Check if click is within a card boundary
                card_x = col * (card_width + padding)
                card_y = row * (card_height + padding)

                if (card_x <= relative_x <= card_x + card_width and
                    card_y <= relative_y <= card_y + card_height):

                    clicked_index = row * cards_per_row + col + navigation_manager.scroll_offset

                    if 0 <= clicked_index < len(current_configs):
                        config = current_configs[clicked_index]

                        # Track card press for visual feedback
                        self.pressed_card = clicked_index
                        self.card_press_time = time.time()

                        # Check if it's a play button click (only in variants view)
                        if navigation_manager.current_view == "variants":
                            play_btn_x = card_x + card_width - 60
                            play_btn_y = card_y + card_height - 35

                            if (play_btn_x <= relative_x <= play_btn_x + 45 and
                                play_btn_y <= relative_y <= play_btn_y + 25):
                                # Play button clicked - show preview
                                navigation_manager.open_level_preview(config)
                                return

                        # Card clicked (not play button)
                        if navigation_manager.current_view == "main":
                            # Main menu card clicked - open level options
                            if self.on_navigate_to_level_options:
                                self.on_navigate_to_level_options(config)
                        elif navigation_manager.current_view == "variants":
                            # Variant card clicked (not play button) - show preview directly
                            navigation_manager.open_level_preview(config)
                        else:
                            # Other views - just select
                            navigation_manager.selected_config = clicked_index

    def clear_pressed_states(self):
        """Clear visual feedback states after brief duration"""
        current_time = time.time()
        if self.pressed_button and current_time - self.button_press_time > 0.3:
            self.pressed_button = None
        if self.pressed_card and current_time - self.card_press_time > 0.3:
            self.pressed_card = None
