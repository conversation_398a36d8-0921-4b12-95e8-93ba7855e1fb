# Enemy Buff System Documentation

## Overview

The Enemy Buff System provides a flexible, stackable, and configurable way to enhance enemies with special abilities. Buffs can be combined for diverse enemy variations and are configured through the game's static balance config.

## 🔧 Core Components

### 1. **EnemyBuff Classes** (`enemies/enemy_buffs.py`)
- **Base Class**: `EnemyBuff` - Abstract base for all buff types
- **Manager**: `EnemyBuffManager` - Manages all buffs on an enemy
- **Types**: 20+ different buff types via `BuffType` enum

### 2. **Configuration** (`config/static_balance_config.json`)
- **enemy_buffs**: Individual buff definitions with properties
- **enemy_buff_combinations**: Predefined buff combinations
- **buff_spawn_chances**: Wave-based probability and availability

### 3. **Integration** (`enemies/enemy.py`)
- Dynamic buff initialization for all enemies
- Random buff application based on wave progression
- Buff updates and visual effects during gameplay

## 🎯 Available Buff Types

### Combat Buffs
- **🛡️ Anti-Explosive**: Resistance to explosive damage (stackable up to 3x)
- **⚔️ Armor**: General damage reduction (stackable up to 5x)
- **🔮 Spell Resistance**: Resistance to magical towers (stackable up to 3x)

### Movement & Behavior
- **👻 Invisibility**: Makes enemy invisible to most towers
- **🪶 Flying**: Makes enemy airborne, only targetable by specific towers
- **💨 Speed Boost**: Increases movement speed (stackable up to 3x)
- **🌀 Phase Shift**: Chance to dodge attacks

### Utility & Special
- **💚 Regeneration**: Heals over time (stackable up to 4x)
- **😡 Berserker**: Gets stronger when damaged
- **❄️ Freeze Immunity**: Cannot be slowed or frozen
- **🔥 Fire Immunity**: Immune to fire damage
- **☠️ Poison Immunity**: Immune to poison effects

## 📋 Predefined Combinations

### Early Game (Wave 15+)
- **Stealth Assassin**: Invisibility + Speed Boost

### Mid Game (Wave 20-30+)
- **Flying Fortress**: Flying + Armor + Anti-Explosive
- **Regenerating Tank**: Regeneration + Armor + Spell Resistance

### Late Game (Wave 30-50+)
- **Berserker Speedster**: Berserker + Speed Boost + Fire Immunity
- **Phase Wraith**: Phase Shift + Invisibility + Freeze Immunity
- **Ultimate Defender**: Armor + Anti-Explosive + Spell Resistance + Regeneration

### End Game (Wave 50+)
- **Chaos Demon**: Flying + Berserker + Phase Shift + Fire Immunity + Poison Immunity

## ⚙️ Configuration Structure

### Individual Buff Configuration
```json
"speed_boost": {
  "name": "Speed Boost",
  "description": "Increases movement speed significantly",
  "icon": "SPEED",
  "stackable": true,
  "max_stacks": 3,
  "duration": -1,
  "color": [255, 255, 0],
  "glow_radius": 12,
  "speed_multiplier": 1.4
}
```

### Wave-Based Spawn Chances
```json
"wave_ranges": {
  "1-10": {
    "base_chance": 0.05,
    "max_buffs": 1,
    "allowed_buffs": ["speed_boost", "armor"]
  },
  "51+": {
    "base_chance": 0.60,
    "max_buffs": 7,
    "allowed_buffs": ["all available buffs"]
  }
}
```

## 💻 Programming Interface

### Creating Buffed Enemies
```python
from enemies.enemy_buffs import create_buffed_enemy, BuffType
from enemies.basic_enemy import BasicEnemy

# Create enemy with individual buffs
buffed_enemy = create_buffed_enemy(
    BasicEnemy, path, wave_number=25,
    individual_buffs=['speed_boost', 'armor', 'invisibility']
)

# Create enemy with buff combinations
combo_enemy = create_buffed_enemy(
    BasicEnemy, path, wave_number=30,
    buff_combinations=['stealth_assassin', 'flying_fortress']
)

# Apply buffs manually
enemy.buff_manager.add_buff(BuffType.ARMOR)
enemy.buff_manager.add_buff(BuffType.SPEED_BOOST)
```

### Querying Buff Availability
```python
from enemies.enemy_buffs import get_available_buffs_for_wave, get_available_combinations_for_wave

# Get buffs available for wave 25
available_buffs = get_available_buffs_for_wave(25)
# Returns: ['speed_boost', 'armor', 'invisibility', 'regeneration', 'flying', 'anti_explosive']

# Get combinations available for wave 35
available_combos = get_available_combinations_for_wave(35)
# Returns: ['stealth_assassin', 'flying_fortress', 'regenerating_tank', 'berserker_speedster']
```

## 🎮 Gameplay Impact

### Wave Progression
- **Waves 1-10**: Basic buffs (5% chance, max 1 buff)
- **Waves 11-20**: Intermediate buffs (15% chance, max 2 buffs)
- **Waves 21-30**: Advanced buffs (25% chance, max 3 buffs)
- **Waves 31-40**: Elite buffs (35% chance, max 4 buffs)
- **Waves 41-50**: Master buffs (45% chance, max 5 buffs)
- **Waves 51+**: Ultimate buffs (60% chance, max 7 buffs)

### Boss Multipliers
- **Mini-Boss** (every 5th wave): 2x buff chance
- **Boss** (every 10th wave): 3x buff chance
- **Super-Boss** (high damage reduction): 4x buff chance

### Damage Resistance Examples
- **Anti-Explosive (3 stacks)**: 120% reduction vs explosive towers = immunity
- **Armor (5 stacks)**: 90% damage reduction (capped)
- **Spell Resistance (3 stacks)**: 105% reduction vs magical towers = immunity

## 🔄 Dynamic Integration

### Enemy Initialization
All enemies dynamically:
1. Initialize buff manager
2. Apply random buffs based on wave progression
3. Respect wave requirements for combinations
4. Display buff effects and indicators

### Tower Interaction
Buffs dynamically affect:
1. **Damage calculation** in `take_damage()` method
2. **Targeting eligibility** (invisibility, flying)
3. **Movement speed** and behavioral changes
4. **Visual effects** and status indicators

## 🎨 Visual Features

### Buff Effects
- **Glow Effects**: Animated glowing auras around buffed enemies
- **Buff Icons**: Small indicators showing active buffs with stack counts
- **Color Changes**: Enemies change color based on active buffs
- **Animation**: Hover effects for flying enemies, regeneration particles

### UI Integration
- Buff icons display next to enemies
- Stack counts shown for stackable buffs
- Glow effects with configurable radius and colors
- Health bar integration remains intact

## 🚀 Benefits

### For Game Balance
- **Scalable Difficulty**: Buffs scale naturally with wave progression
- **Strategic Depth**: Players must adapt strategies for buffed enemies
- **Configurable**: Easy to balance through JSON configuration

### For Gameplay Variety
- **Diverse Encounters**: 20+ buff types create thousands of combinations
- **Emergent Strategies**: Players discover new tower combinations
- **Replayability**: Random buff application ensures different experiences

### For Development
- **Modular Design**: Easy to add new buff types
- **Data-Driven**: All balance changes via configuration
- **Extensible**: System supports temporary buffs, conditional effects

## 📈 Future Expansion Possibilities

### Additional Buff Types
- **Splitting**: Enemies split when killed
- **Teleportation**: Random position changes
- **Energy Shield**: Temporary damage immunity
- **Damage Reflection**: Reflects damage back to towers
- **Toxicity**: Damages nearby towers

### Advanced Features
- **Conditional Buffs**: Activate under specific conditions
- **Temporary Buffs**: Duration-based effects
- **Debuffs**: Negative effects that can be applied
- **Buff Interactions**: Synergies between different buff types

## 🔧 Technical Notes

### Performance
- Lightweight buff updates each frame
- Efficient rendering with batch operations
- Minimal memory overhead per enemy

### Compatibility
- Integrates seamlessly with existing enemy system
- Maintains backward compatibility
- No impact on non-buffed enemies

### Error Handling
- Graceful degradation if buff system fails
- Warning messages for invalid configurations
- Fallback to basic enemy behavior

The Enemy Buff System transforms the tower defense experience by providing endless variety in enemy encounters while maintaining strategic depth and balance flexibility. 