from .tower import Tower
import pygame
import math
from projectiles.projectile import Projectile


class CannonTower(Tower):
    """Heavy cannon tower with splash damage and long range"""

    def __init__(self, x, y):
        super().__init__(x, y, 'cannon')
        self.damage = 40  # Slightly reduced damage for balance
        self.range = 240  # Slightly reduced range
        self.fire_rate = 65  # Slightly slower firing for balance
        self.projectile_speed = 5.0  # Faster projectiles
        # Medium size for 1.5x1.5 tower (increased by 0.5 blocks)
        self.size = 15
        self.color = (139, 69, 19)  # Brown

        # Cannon properties - SIGNIFICANTLY BOOSTED
        self.splash_radius = 70  # Much larger explosion (was 40)
        self.splash_damage = 25  # High splash damage (was 15)

        # POWERFUL SIEGE WEAPON - can target both ground and air
        self.can_target_flying = True  # Now targets flying enemies too!
        self.can_target_invisible = False

        # Targeting capabilities - ground only
        self.can_target_flying = False
        self.can_target_invisible = False
        self.can_target_ground = True

        # Finalize initialization to update base stats
        self.finalize_initialization()

    def acquire_target(self, enemies):
        """Find target - prioritize groups of enemies"""
        valid_targets = []

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.range and self.can_target_enemy(enemy):
                # Count nearby enemies for splash potential
                nearby_count = 0
                for other_enemy in enemies:
                    if self.can_target_enemy(other_enemy):
                        splash_distance = math.sqrt(
                            (enemy.x - other_enemy.x)**2 + (enemy.y - other_enemy.y)**2)
                        if splash_distance <= self.splash_radius:
                            nearby_count += 1

                valid_targets.append((enemy, distance, nearby_count))

        if valid_targets:
            # Target enemy with most nearby enemies (best splash potential)
            valid_targets.sort(key=lambda x: x[2], reverse=True)
            self.target = valid_targets[0][0]

            # Calculate angle to target
            if self.target:
                dx = self.target.x - self.x
                dy = self.target.y - self.y
                self.angle = math.atan2(dy, dx)
        else:
            self.target = None

    def shoot(self, projectiles):
        """Fire explosive cannonball"""
        if self.target:
            projectile = ExplosiveCannonball(
                self.x, self.y, self.target.x, self.target.y,
                self.projectile_speed, self.damage, self.splash_radius, self.splash_damage
            )
            projectile.source_tower_id = self.tower_id
            projectiles.append(projectile)

            # Generate currency immediately when firing
            self.generate_firing_currency()

    def draw(self, screen, selected: bool = False):
        """Draw cannon tower with sprite support"""
        # Check if sprite manager is available
        sprite_manager = getattr(self, '_sprite_manager', None)

        # Draw range circle only when selected
        if selected:
            pygame.draw.circle(screen, (200, 200, 200),
                               (int(self.x), int(self.y)), int(self.range), 1)

        # Try to draw with sprite first
        if sprite_manager and sprite_manager.has_tower_sprites():
            sprite = sprite_manager.get_tower_sprite(
                self.tower_type, self.angle)
            if sprite:
                # Center the sprite on the tower position
                sprite_rect = sprite.get_rect()
                sprite_rect.center = (int(self.x), int(self.y))
                screen.blit(sprite, sprite_rect)

                # Draw upgrade available indicator
                self.draw_upgrade_indicator(screen)
                return

        # Fallback to custom drawing
        # Draw base
        pygame.draw.circle(screen, self.color,
                           (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, (0, 0, 0),
                           (int(self.x), int(self.y)), self.size, 2)

        # Draw larger, more powerful cannon barrel
        if self.target:
            barrel_length = self.size + 18  # Longer barrel
            barrel_width = 6  # Thicker barrel
            end_x = self.x + math.cos(self.angle) * barrel_length
            end_y = self.y + math.sin(self.angle) * barrel_length

            # Draw thick barrel
            pygame.draw.line(screen, (60, 60, 60), (int(self.x), int(
                self.y)), (int(end_x), int(end_y)), barrel_width)

            # Draw larger muzzle
            pygame.draw.circle(screen, (40, 40, 40),
                               (int(end_x), int(end_y)), 5)
            pygame.draw.circle(screen, (255, 165, 0),
                               (int(end_x), int(end_y)), 2)  # Orange glow

        # Draw cannon wheels
        wheel_y = self.y + self.size - 3
        pygame.draw.circle(screen, (100, 50, 0),
                           (int(self.x - 8), int(wheel_y)), 4)
        pygame.draw.circle(screen, (100, 50, 0),
                           (int(self.x + 8), int(wheel_y)), 4)

        # Draw upgrade available indicator
        self.draw_upgrade_indicator(screen)

    def acquire_target_optimized(self, enemies, spatial_grid=None):
        """Optimized targeting with restrictions and spatial partitioning"""
        if not enemies:
            self.target = None
            return

        # Use spatial partitioning if available
        if spatial_grid:
            nearby_enemies = spatial_grid.get_enemies_near_tower(
                self.x, self.y, self.range)
            if not nearby_enemies:
                self.target = None
                return
            enemies_to_check = nearby_enemies
        else:
            enemies_to_check = enemies

        range_squared = self.range * self.range
        valid_targets = []

        for enemy in enemies_to_check:
            dx = enemy.x - self.x
            dy = enemy.y - self.y
            distance_squared = dx * dx + dy * dy

            if distance_squared <= range_squared and self.can_target_enemy(enemy):
                actual_distance = math.sqrt(distance_squared)
                # Count nearby enemies for splash potential
                nearby_count = 0
                for other_enemy in enemies_to_check:
                    if self.can_target_enemy(other_enemy):
                        splash_distance = math.sqrt(
                            (enemy.x - other_enemy.x)**2 + (enemy.y - other_enemy.y)**2)
                        if splash_distance <= self.splash_radius:
                            nearby_count += 1

                valid_targets.append((enemy, actual_distance, nearby_count))

        if valid_targets:
            # Target enemy with most nearby enemies (best splash potential)
            valid_targets.sort(key=lambda x: x[2], reverse=True)
            self.target = valid_targets[0][0]

            # Calculate angle to target
            if self.target:
                dx = self.target.x - self.x
                dy = self.target.y - self.y
                self.angle = math.atan2(dy, dx)
        else:
            self.target = None

    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Update with speed multiplier and targeting restrictions"""
        self.acquire_target_optimized(enemies)

        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate

        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier


class ExplosiveCannonball(Projectile):
    """Explosive cannonball projectile with AOE damage"""

    def __init__(self, x, y, target_x, target_y, speed, damage, splash_radius, splash_damage):
        super().__init__(x, y, target_x, target_y, speed, damage, 'cannon')
        self.splash_radius = splash_radius
        self.splash_damage = splash_damage
        self.color = (139, 69, 19)  # Brown
        self.size = 8  # Bigger cannonball

        self.active = True
        self.should_remove = False

    def update(self):
        """Update projectile position"""
        super().update()
        # Remove if off screen
        if self.x < 0 or self.x > 1200 or self.y < 0 or self.y > 800:
            self.active = False
            self.should_remove = True

    def draw(self, screen):
        """Draw larger, more powerful cannonball"""
        if self.active:
            pygame.draw.circle(screen, self.color,
                               (int(self.x), int(self.y)), self.size)
            pygame.draw.circle(screen, (0, 0, 0), (int(self.x), int(
                self.y)), self.size, 2)  # Thicker outline
            pygame.draw.circle(screen, (255, 165, 0), (int(
                self.x), int(self.y)), 3)  # Orange glow center

    def check_collision(self, enemies):
        """Check collision with enemies and create explosion"""
        if not self.active:
            return {'hit': False, 'damage': 0, 'tower_id': None}

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= 12:  # Larger collision detection
                total_damage = 0
                # Damage all enemies in splash radius
                for enemy_in_range in enemies:
                    explosion_distance = math.sqrt(
                        (enemy_in_range.x - self.x)**2 + (enemy_in_range.y - self.y)**2)
                    if explosion_distance <= self.splash_radius:
                        if explosion_distance < 25:  # Larger direct hit radius
                            damage_dealt = enemy_in_range.take_damage(
                                self.damage, 'cannon')
                        else:  # Splash damage
                            damage_dealt = enemy_in_range.take_damage(
                                self.splash_damage, 'cannon')
                        total_damage += damage_dealt

                self.active = False
                self.should_remove = True
                return {'hit': True, 'damage': total_damage, 'tower_id': getattr(self, 'source_tower_id', None)}

        return {'hit': False, 'damage': 0, 'tower_id': None}

    def explode(self, enemies):
        """Create explosion and damage nearby enemies"""
        # Damage all enemies in splash radius
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.splash_radius:
                # Direct hit gets full damage, splash gets reduced damage
                if distance < 15:  # Direct hit
                    enemy.take_damage(self.damage, 'cannon')
                else:  # Splash damage
                    enemy.take_damage(self.splash_damage, 'cannon')

        self.active = False
        self.should_remove = True
