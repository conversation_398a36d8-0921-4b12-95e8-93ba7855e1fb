#!/usr/bin/env python3
"""
Debug script to trace shield regeneration step by step
"""

# Mock the necessary imports to avoid dependencies
class MockEnemy:
    def __init__(self):
        self.path = [(0, 0), (100, 100)]
        self.wave_number = 1
        self.x = 0
        self.y = 0
        self.path_index = 0
        self.distance_traveled = 0
        self.frozen = False
        self.wet = False
        self.wet_timer = 0
        self.lightning_damage_multiplier = 1.0
        self.buff_manager = None
        self.base_speed = 1.0
        self.speed = 1.0
        
    def update(self):
        # Simplified base update
        pass
        
    def move_along_path(self):
        pass
        
    def apply_terrain_speed_effects(self):
        pass

# Mock the config
def mock_get_balance_config():
    return {
        'counter_system': {
            'tower_enemy_multipliers': {},
            'default_multiplier': 1.0,
            'max_multiplier': 3.0,
            'min_multiplier': 0.1
        }
    }

# Create a simplified ShieldedEnemy for testing
class TestShieldedEnemy(MockEnemy):
    def __init__(self):
        super().__init__()
        self.max_health = 15
        self.health = self.max_health
        self.max_shield = 15
        self.shield = self.max_shield
        self.shield_regen_timer = 0
        self.shield_regen_delay = 180  # 3 seconds at 60 FPS
        self.speed = 1.2
        self.reward = 10
        self.size = 10
        self.color = (0, 255, 255)  # Cyan
    
    def update(self):
        """Update with shield regeneration"""
        super().update()
        
        print(f"  Update called: shield={self.shield}/{self.max_shield}, timer={self.shield_regen_timer}")
        
        # Regenerate shield
        if self.shield < self.max_shield:
            self.shield_regen_timer += 1
            print(f"    Timer incremented to: {self.shield_regen_timer}")
            if self.shield_regen_timer >= self.shield_regen_delay:
                self.shield += 1
                self.shield_regen_timer = 0
                print(f"    SHIELD REGENERATED! New shield: {self.shield}")
        else:
            print(f"    Shield at max, no regeneration needed")
    
    def take_damage(self, damage, tower_type='basic'):
        """Simplified damage logic"""
        print(f"  Taking {damage} damage...")
        
        # Store original state
        original_total_hp = self.shield + self.health
        
        # Apply damage to shield first
        if self.shield > 0 and damage > 0:
            shield_damage = min(self.shield, damage)
            self.shield -= shield_damage
            damage -= shield_damage
            self.shield_regen_timer = 0  # Reset regen timer when shield is hit
            print(f"    Shield took {shield_damage} damage, shield now {self.shield}, timer reset to 0")
        
        # Apply remaining damage to health
        if damage > 0:
            health_damage = min(self.health, damage)
            self.health -= health_damage
            print(f"    Health took {health_damage} damage, health now {self.health}")
        
        # Calculate actual total damage dealt
        new_total_hp = max(0, self.shield + self.health)
        actual_damage = original_total_hp - new_total_hp
        return actual_damage

def test_shield_regeneration():
    print("=== Testing Shield Regeneration Step by Step ===\n")
    
    enemy = TestShieldedEnemy()
    
    print(f"Initial state: shield={enemy.shield}/{enemy.max_shield}, timer={enemy.shield_regen_timer}")
    
    # Damage the shield
    print(f"\n1. Damaging shield with 5 damage:")
    enemy.take_damage(5)
    
    print(f"\n2. Testing regeneration over time:")
    for frame in range(185):
        if frame % 30 == 0 or frame >= 175:  # Print every 30 frames, and the last few
            print(f"\nFrame {frame}:")
        enemy.update()
        
        if frame == 179:
            print(f"  Frame 179 (should NOT regenerate yet)")
        elif frame == 180:
            print(f"  Frame 180 (should regenerate now)")
    
    print(f"\nFinal state: shield={enemy.shield}/{enemy.max_shield}, timer={enemy.shield_regen_timer}")

if __name__ == "__main__":
    test_shield_regeneration()
