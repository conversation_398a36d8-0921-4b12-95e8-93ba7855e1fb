"""
Intelligent Enemy and Buff Selection System
AI-driven selection of enemy types and buff combinations based on difficulty and player performance
"""

import random
import math
from typing import List, Dict, Any, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

@dataclass
class EnemyData:
    """Data about an enemy type"""
    name: str
    difficulty_rating: int  # 1-10 scale
    wave_introduction: int  # Wave number when this enemy is first available
    special_abilities: List[str]
    counters: List[str]  # Tower types that are effective against this enemy
    weaknesses: List[str]  # What this enemy is weak to
    synergies: List[str]  # Enemy types this works well with

@dataclass
class BuffData:
    """Data about a buff type"""
    name: str
    difficulty_rating: int  # 1-10 scale
    wave_introduction: int  # Wave number when this buff is first available
    effect_type: str  # defensive, offensive, utility, immunity
    power_level: int  # 1-5 scale of how powerful this buff is
    synergies: List[str]  # Other buffs this works well with
    conflicts: List[str]  # Buffs that don't work well together

class SelectionStrategy(Enum):
    """Different strategies for enemy/buff selection"""
    BALANCED = "balanced"
    AGGRESSIVE = "aggressive"
    DEFENSIVE = "defensive"
    CHAOS = "chaos"
    SPECIALIZED = "specialized"

class IntelligentEnemyBuffSelector:
    """AI system for selecting appropriate enemies and buffs based on difficulty and strategy"""
    
    def __init__(self):
        self.enemy_database = self._initialize_enemy_database()
        self.buff_database = self._initialize_buff_database()
        
    def _initialize_enemy_database(self) -> Dict[str, EnemyData]:
        """Initialize the database of enemy types with their characteristics"""
        return {
            "BasicEnemy": EnemyData(
                name="BasicEnemy",
                difficulty_rating=1,
                wave_introduction=1,
                special_abilities=[],
                counters=["basic", "poison"],
                weaknesses=["any"],
                synergies=["FastEnemy", "TankEnemy"]
            ),
            "FastEnemy": EnemyData(
                name="FastEnemy",
                difficulty_rating=2,
                wave_introduction=2,
                special_abilities=["speed"],
                counters=["sniper", "ice", "freezer"],
                weaknesses=["freeze", "slow"],
                synergies=["BasicEnemy", "InvisibleEnemy"]
            ),
            "TankEnemy": EnemyData(
                name="TankEnemy",
                difficulty_rating=3,
                wave_introduction=3,
                special_abilities=["high_health"],
                counters=["cannon", "laser"],
                weaknesses=["armor_piercing"],
                synergies=["BasicEnemy", "ArmoredEnemy"]
            ),
            "FlyingEnemy": EnemyData(
                name="FlyingEnemy",
                difficulty_rating=4,
                wave_introduction=5,
                special_abilities=["flying"],
                counters=["antiair", "missile"],
                weaknesses=["anti_air"],
                synergies=["InvisibleEnemy", "FastEnemy"]
            ),
            "ShieldedEnemy": EnemyData(
                name="ShieldedEnemy",
                difficulty_rating=5,
                wave_introduction=8,
                special_abilities=["energy_shield"],
                counters=["cannon", "laser"],
                weaknesses=["kinetic_damage"],
                synergies=["ArmoredEnemy", "RegeneratingEnemy"]
            ),
            "InvisibleEnemy": EnemyData(
                name="InvisibleEnemy",
                difficulty_rating=6,
                wave_introduction=10,
                special_abilities=["invisibility"],
                counters=["detector", "antiair"],
                weaknesses=["detection"],
                synergies=["FastEnemy", "FlyingEnemy"]
            ),
            "ArmoredEnemy": EnemyData(
                name="ArmoredEnemy",
                difficulty_rating=4,
                wave_introduction=12,
                special_abilities=["armor"],
                counters=["cannon", "flame"],
                weaknesses=["armor_piercing", "flame"],
                synergies=["TankEnemy", "ShieldedEnemy"]
            ),
            "RegeneratingEnemy": EnemyData(
                name="RegeneratingEnemy",
                difficulty_rating=5,
                wave_introduction=15,
                special_abilities=["regeneration"],
                counters=["poison", "burst_damage"],
                weaknesses=["poison", "high_dps"],
                synergies=["ShieldedEnemy", "ArmoredEnemy"]
            ),
            "TeleportingEnemy": EnemyData(
                name="TeleportingEnemy",
                difficulty_rating=6,
                wave_introduction=18,
                special_abilities=["teleportation"],
                counters=["sniper", "explosive"],
                weaknesses=["area_damage"],
                synergies=["PhaseShiftEnemy", "InvisibleEnemy"]
            ),
            "SplittingEnemy": EnemyData(
                name="SplittingEnemy",
                difficulty_rating=7,
                wave_introduction=20,
                special_abilities=["split_on_death"],
                counters=["splash", "missile"],
                weaknesses=["area_damage"],
                synergies=["RegeneratingEnemy", "ToxicEnemy"]
            ),
            "FireElementalEnemy": EnemyData(
                name="FireElementalEnemy",
                difficulty_rating=6,
                wave_introduction=22,
                special_abilities=["fire_immunity", "burn"],
                counters=["ice", "lightning"],
                weaknesses=["ice", "water"],
                synergies=["ToxicEnemy", "PhaseShiftEnemy"]
            ),
            "ToxicEnemy": EnemyData(
                name="ToxicEnemy",
                difficulty_rating=5,
                wave_introduction=25,
                special_abilities=["poison_aura"],
                counters=["poison"],
                weaknesses=["ranged"],
                synergies=["SplittingEnemy", "RegeneratingEnemy"]
            ),
            "PhaseShiftEnemy": EnemyData(
                name="PhaseShiftEnemy",
                difficulty_rating=7,
                wave_introduction=28,
                special_abilities=["phase_shift"],
                counters=["flame", "sniper"],
                weaknesses=["sustained_damage"],
                synergies=["TeleportingEnemy", "InvisibleEnemy"]
            ),
            "BlastProofEnemy": EnemyData(
                name="BlastProofEnemy",
                difficulty_rating=6,
                wave_introduction=30,
                special_abilities=["explosive_immunity"],
                counters=["laser", "lightning"],
                weaknesses=["energy_damage"],
                synergies=["ArmoredEnemy", "ShieldedEnemy"]
            ),
            "SpectralEnemy": EnemyData(
                name="SpectralEnemy",
                difficulty_rating=8,
                wave_introduction=35,
                special_abilities=["magic_resistance"],
                counters=["lightning", "poison"],
                weaknesses=["lightning"],
                synergies=["PhaseShiftEnemy", "VoidEnemy"]
            ),
            "CrystallineEnemy": EnemyData(
                name="CrystallineEnemy",
                difficulty_rating=8,
                wave_introduction=38,
                special_abilities=["laser_vulnerability", "crystal_armor"],
                counters=["laser", "explosive"],
                weaknesses=["laser"],
                synergies=["BlastProofEnemy", "SpectralEnemy"]
            ),
            "ToxicMutantEnemy": EnemyData(
                name="ToxicMutantEnemy",
                difficulty_rating=7,
                wave_introduction=40,
                special_abilities=["poison_immunity", "toxic_aura"],
                counters=["flame", "ice"],
                weaknesses=["flame"],
                synergies=["ToxicEnemy", "RegeneratingEnemy"]
            ),
            "VoidEnemy": EnemyData(
                name="VoidEnemy",
                difficulty_rating=8,
                wave_introduction=42,
                special_abilities=["void_immunity"],
                counters=["missile", "explosive"],
                weaknesses=["explosive"],
                synergies=["SpectralEnemy", "AdaptiveEnemy"]
            ),
            "AdaptiveEnemy": EnemyData(
                name="AdaptiveEnemy",
                difficulty_rating=9,
                wave_introduction=45,
                special_abilities=["damage_adaptation"],
                counters=["sniper", "ice"],
                weaknesses=["burst_damage", "varied_damage"],
                synergies=["VoidEnemy", "SpectralEnemy"]
            )
        }
    
    def _initialize_buff_database(self) -> Dict[str, BuffData]:
        """Initialize the database of buff types with their characteristics"""
        return {
            "speed_boost": BuffData(
                name="speed_boost",
                difficulty_rating=2,
                wave_introduction=1,
                effect_type="offensive",
                power_level=2,
                synergies=["invisibility", "flying"],
                conflicts=["freeze_immunity"]
            ),
            "armor": BuffData(
                name="armor",
                difficulty_rating=3,
                wave_introduction=1,
                effect_type="defensive",
                power_level=3,
                synergies=["regeneration", "spell_resistance"],
                conflicts=[]
            ),
            "invisibility": BuffData(
                name="invisibility",
                difficulty_rating=5,
                wave_introduction=8,
                effect_type="utility",
                power_level=4,
                synergies=["speed_boost", "phase_shift"],
                conflicts=["flying"]
            ),
            "regeneration": BuffData(
                name="regeneration",
                difficulty_rating=4,
                wave_introduction=10,
                effect_type="defensive",
                power_level=3,
                synergies=["armor", "energy_shield"],
                conflicts=[]
            ),
            "flying": BuffData(
                name="flying",
                difficulty_rating=5,
                wave_introduction=12,
                effect_type="utility",
                power_level=4,
                synergies=["speed_boost", "anti_explosive"],
                conflicts=["invisibility"]
            ),
            "anti_explosive": BuffData(
                name="anti_explosive",
                difficulty_rating=4,
                wave_introduction=15,
                effect_type="immunity",
                power_level=3,
                synergies=["flying", "armor"],
                conflicts=[]
            ),
            "spell_resistance": BuffData(
                name="spell_resistance",
                difficulty_rating=5,
                wave_introduction=20,
                effect_type="immunity",
                power_level=4,
                synergies=["armor", "fire_immunity"],
                conflicts=[]
            ),
            "berserker": BuffData(
                name="berserker",
                difficulty_rating=6,
                wave_introduction=25,
                effect_type="offensive",
                power_level=4,
                synergies=["speed_boost", "damage_reflection"],
                conflicts=["armor"]
            ),
            "damage_reflection": BuffData(
                name="damage_reflection",
                difficulty_rating=6,
                wave_introduction=30,
                effect_type="offensive",
                power_level=4,
                synergies=["berserker", "energy_shield"],
                conflicts=[]
            ),
            "teleportation": BuffData(
                name="teleportation",
                difficulty_rating=7,
                wave_introduction=35,
                effect_type="utility",
                power_level=5,
                synergies=["phase_shift", "invisibility"],
                conflicts=["flying"]
            ),
            "split_on_death": BuffData(
                name="split_on_death",
                difficulty_rating=7,
                wave_introduction=35,
                effect_type="offensive",
                power_level=4,
                synergies=["regeneration", "toxicity"],
                conflicts=[]
            ),
            "toxicity": BuffData(
                name="toxicity",
                difficulty_rating=6,
                wave_introduction=40,
                effect_type="offensive",
                power_level=4,
                synergies=["split_on_death", "poison_immunity"],
                conflicts=[]
            ),
            "energy_shield": BuffData(
                name="energy_shield",
                difficulty_rating=7,
                wave_introduction=40,
                effect_type="defensive",
                power_level=5,
                synergies=["regeneration", "damage_reflection"],
                conflicts=[]
            ),
            "fire_immunity": BuffData(
                name="fire_immunity",
                difficulty_rating=7,
                wave_introduction=45,
                effect_type="immunity",
                power_level=4,
                synergies=["spell_resistance", "freeze_immunity"],
                conflicts=[]
            ),
            "freeze_immunity": BuffData(
                name="freeze_immunity",
                difficulty_rating=7,
                wave_introduction=45,
                effect_type="immunity",
                power_level=4,
                synergies=["fire_immunity", "poison_immunity"],
                conflicts=["speed_boost"]
            ),
            "poison_immunity": BuffData(
                name="poison_immunity",
                difficulty_rating=6,
                wave_introduction=48,
                effect_type="immunity",
                power_level=3,
                synergies=["freeze_immunity", "toxicity"],
                conflicts=[]
            ),
            "ground_immunity": BuffData(
                name="ground_immunity",
                difficulty_rating=5,
                wave_introduction=50,
                effect_type="immunity",
                power_level=3,
                synergies=["flying", "phase_shift"],
                conflicts=[]
            ),
            "camouflage": BuffData(
                name="camouflage",
                difficulty_rating=6,
                wave_introduction=50,
                effect_type="utility",
                power_level=4,
                synergies=["invisibility", "phase_shift"],
                conflicts=[]
            ),
            "phase_shift": BuffData(
                name="phase_shift",
                difficulty_rating=8,
                wave_introduction=55,
                effect_type="utility",
                power_level=5,
                synergies=["teleportation", "invisibility"],
                conflicts=[]
            ),
            "adaptive_resistance": BuffData(
                name="adaptive_resistance",
                difficulty_rating=9,
                wave_introduction=60,
                effect_type="defensive",
                power_level=5,
                synergies=["spell_resistance", "energy_shield"],
                conflicts=[]
            )
        }
    
    def select_enemies_for_difficulty(self, difficulty_score: int, max_wave: int, 
                                    strategy: SelectionStrategy = SelectionStrategy.BALANCED) -> Dict[str, Any]:
        """Select appropriate enemy types and compositions based on difficulty"""
        
        # Calculate how many enemy types to use (90% at difficulty 100)
        max_enemies = len(self.enemy_database)
        enemy_usage_ratio = min(0.9, 0.3 + (difficulty_score / 100) * 0.6)
        target_enemy_count = max(3, int(max_enemies * enemy_usage_ratio))
        
        # Filter enemies based on wave availability and difficulty
        available_enemies = []
        for enemy_name, enemy_data in self.enemy_database.items():
            if enemy_data.wave_introduction <= max_wave:
                # Weight by difficulty appropriateness
                difficulty_match = 1.0 - abs(enemy_data.difficulty_rating - (difficulty_score / 10)) / 10
                difficulty_match = max(0.1, difficulty_match)  # Minimum weight
                available_enemies.append((enemy_name, enemy_data, difficulty_match))
        
        # Sort by difficulty match and select top candidates
        available_enemies.sort(key=lambda x: x[2], reverse=True)
        
        # Select enemies based on strategy
        selected_enemies = self._select_enemies_by_strategy(
            available_enemies, target_enemy_count, difficulty_score, strategy
        )
        
        # Generate wave compositions
        wave_compositions = self._generate_enemy_compositions(
            selected_enemies, max_wave, difficulty_score, strategy
        )
        
        return {
            "selected_enemies": selected_enemies,
            "wave_compositions": wave_compositions,
            "enemy_usage_ratio": enemy_usage_ratio,
            "strategy_used": strategy.value
        }
    
    def select_buffs_for_difficulty(self, difficulty_score: int, max_wave: int,
                                  strategy: SelectionStrategy = SelectionStrategy.BALANCED) -> Dict[str, Any]:
        """Select appropriate buff types and spawn rates based on difficulty"""
        
        # Calculate how many buff types to use (90% at difficulty 100)
        max_buffs = len(self.buff_database)
        buff_usage_ratio = min(0.9, 0.2 + (difficulty_score / 100) * 0.7)
        target_buff_count = max(2, int(max_buffs * buff_usage_ratio))
        
        # Filter buffs based on wave availability and difficulty
        available_buffs = []
        for buff_name, buff_data in self.buff_database.items():
            if buff_data.wave_introduction <= max_wave:
                # Weight by difficulty appropriateness
                difficulty_match = 1.0 - abs(buff_data.difficulty_rating - (difficulty_score / 10)) / 10
                difficulty_match = max(0.1, difficulty_match)  # Minimum weight
                available_buffs.append((buff_name, buff_data, difficulty_match))
        
        # Sort by difficulty match and select buffs
        available_buffs.sort(key=lambda x: x[2], reverse=True)
        
        # Select buffs avoiding conflicts and promoting synergies
        selected_buffs = self._select_buffs_with_synergy(
            available_buffs, target_buff_count, difficulty_score, strategy
        )
        
        # Generate buff spawn configuration
        buff_config = self._generate_buff_spawn_config(
            selected_buffs, max_wave, difficulty_score, strategy
        )
        
        return {
            "selected_buffs": selected_buffs,
            "buff_spawn_config": buff_config,
            "buff_usage_ratio": buff_usage_ratio,
            "strategy_used": strategy.value
        }
    
    def _select_enemies_by_strategy(self, available_enemies: List[Tuple], 
                                  target_count: int, difficulty_score: int,
                                  strategy: SelectionStrategy) -> List[str]:
        """Select enemies based on the chosen strategy"""
        selected = []
        
        if strategy == SelectionStrategy.BALANCED:
            # Mix of different difficulty levels
            for i in range(target_count):
                if i < len(available_enemies):
                    selected.append(available_enemies[i][0])
        
        elif strategy == SelectionStrategy.AGGRESSIVE:
            # Prefer faster, more mobile enemies
            aggressive_types = ["FastEnemy", "FlyingEnemy", "TeleportingEnemy", "PhaseShiftEnemy"]
            available_aggressive = [e for e in available_enemies if e[0] in aggressive_types]
            for enemy in available_aggressive[:target_count//2]:
                selected.append(enemy[0])
            # Fill remaining with highest difficulty
            remaining = target_count - len(selected)
            for i in range(remaining):
                if i < len(available_enemies) and available_enemies[i][0] not in selected:
                    selected.append(available_enemies[i][0])
        
        elif strategy == SelectionStrategy.DEFENSIVE:
            # Prefer tanky, defensive enemies
            defensive_types = ["TankEnemy", "ArmoredEnemy", "ShieldedEnemy", "RegeneratingEnemy"]
            available_defensive = [e for e in available_enemies if e[0] in defensive_types]
            for enemy in available_defensive[:target_count//2]:
                selected.append(enemy[0])
            # Fill remaining with highest difficulty
            remaining = target_count - len(selected)
            for i in range(remaining):
                if i < len(available_enemies) and available_enemies[i][0] not in selected:
                    selected.append(available_enemies[i][0])
        
        elif strategy == SelectionStrategy.CHAOS:
            # Random selection weighted by difficulty match
            weights = [e[2] for e in available_enemies]
            selected_indices = []
            for _ in range(min(target_count, len(available_enemies))):
                # Weighted random selection without replacement
                remaining_indices = [i for i in range(len(available_enemies)) if i not in selected_indices]
                remaining_weights = [weights[i] for i in remaining_indices]
                
                if remaining_weights:
                    chosen_idx = random.choices(remaining_indices, weights=remaining_weights)[0]
                    selected_indices.append(chosen_idx)
                    selected.append(available_enemies[chosen_idx][0])
        
        elif strategy == SelectionStrategy.SPECIALIZED:
            # Focus on enemies with unique abilities
            specialized_types = ["InvisibleEnemy", "PhaseShiftEnemy", "AdaptiveEnemy", "VoidEnemy"]
            available_specialized = [e for e in available_enemies if e[0] in specialized_types]
            for enemy in available_specialized[:target_count//2]:
                selected.append(enemy[0])
            # Fill remaining with synergistic enemies
            remaining = target_count - len(selected)
            for i in range(remaining):
                if i < len(available_enemies) and available_enemies[i][0] not in selected:
                    selected.append(available_enemies[i][0])
        
        # Ensure we have basic enemies if difficulty is low
        if difficulty_score < 30 and "BasicEnemy" not in selected:
            if len(selected) < target_count:
                selected.append("BasicEnemy")
            else:
                selected[0] = "BasicEnemy"
        
        return selected[:target_count]
    
    def _select_buffs_with_synergy(self, available_buffs: List[Tuple], 
                                 target_count: int, difficulty_score: int,
                                 strategy: SelectionStrategy) -> List[str]:
        """Select buffs considering synergies and avoiding conflicts"""
        selected = []
        excluded = set()
        
        # Sort by power level and difficulty match
        available_buffs.sort(key=lambda x: (x[1].power_level, x[2]), reverse=True)
        
        for buff_name, buff_data, difficulty_match in available_buffs:
            if len(selected) >= target_count:
                break
            
            if buff_name in excluded:
                continue
            
            # Check for conflicts with already selected buffs
            has_conflict = False
            for selected_buff in selected:
                selected_buff_data = self.buff_database[selected_buff]
                if (buff_name in selected_buff_data.conflicts or 
                    selected_buff in buff_data.conflicts):
                    has_conflict = True
                    break
            
            if not has_conflict:
                selected.append(buff_name)
                # Add conflicting buffs to excluded list
                excluded.update(buff_data.conflicts)
        
        # If we don't have enough buffs, fill with non-conflicting ones
        while len(selected) < target_count and len(selected) < len(available_buffs):
            for buff_name, buff_data, difficulty_match in available_buffs:
                if buff_name not in selected and buff_name not in excluded:
                    selected.append(buff_name)
                    break
            else:
                break  # No more non-conflicting buffs available
        
        return selected
    
    def _generate_enemy_compositions(self, selected_enemies: List[str], 
                                   max_wave: int, difficulty_score: int,
                                   strategy: SelectionStrategy) -> Dict[Tuple[int, int], List[Tuple[str, float]]]:
        """Generate wave compositions for selected enemies"""
        compositions = {}
        
        # Define wave ranges
        wave_ranges = [
            (1, 5), (6, 10), (11, 15), (16, 20), (21, 25), 
            (26, 30), (31, 35), (36, 40), (41, 45), (46, max_wave)
        ]
        
        for start_wave, end_wave in wave_ranges:
            if start_wave > max_wave:
                break
            
            end_wave = min(end_wave, max_wave)
            
            # Filter enemies available for this wave range
            available_for_range = []
            for enemy_name in selected_enemies:
                enemy_data = self.enemy_database[enemy_name]
                if enemy_data.wave_introduction <= end_wave:
                    available_for_range.append(enemy_name)
            
            if not available_for_range:
                available_for_range = ["BasicEnemy"]
            
            # Generate weights based on difficulty and strategy
            composition = []
            total_weight = 0
            
            for enemy_name in available_for_range:
                enemy_data = self.enemy_database[enemy_name]
                
                # Base weight from difficulty match
                base_weight = 1.0 - abs(enemy_data.difficulty_rating - (difficulty_score / 10)) / 10
                base_weight = max(0.1, base_weight)
                
                # Strategy-based weight adjustments
                if strategy == SelectionStrategy.AGGRESSIVE:
                    if "speed" in enemy_data.special_abilities or "teleportation" in enemy_data.special_abilities:
                        base_weight *= 1.5
                elif strategy == SelectionStrategy.DEFENSIVE:
                    if "armor" in enemy_data.special_abilities or "high_health" in enemy_data.special_abilities:
                        base_weight *= 1.5
                elif strategy == SelectionStrategy.CHAOS:
                    base_weight *= random.uniform(0.5, 2.0)
                
                # Early waves should favor easier enemies
                if start_wave <= 10:
                    if enemy_data.difficulty_rating <= 3:
                        base_weight *= 1.5
                    elif enemy_data.difficulty_rating >= 7:
                        base_weight *= 0.5
                
                composition.append((enemy_name, base_weight))
                total_weight += base_weight
            
            # Normalize weights
            if total_weight > 0:
                normalized_composition = [
                    (enemy_name, weight / total_weight) 
                    for enemy_name, weight in composition
                ]
                compositions[(start_wave, end_wave)] = normalized_composition
        
        return compositions
    
    def _generate_buff_spawn_config(self, selected_buffs: List[str], 
                                  max_wave: int, difficulty_score: int,
                                  strategy: SelectionStrategy) -> Dict[str, Any]:
        """Generate buff spawn configuration"""
        # Define wave ranges for buff introduction
        wave_ranges = {}
        
        # Early game (1-15): Only basic buffs
        basic_buffs = [b for b in selected_buffs if self.buff_database[b].difficulty_rating <= 3]
        if basic_buffs:
            base_chance = 0.05 + (difficulty_score / 100) * 0.1  # 5-15% chance
            wave_ranges["1-15"] = {
                "base_chance": base_chance,
                "max_buffs": min(2, len(basic_buffs)),
                "allowed_buffs": basic_buffs
            }
        
        # Mid game (16-30): Basic + intermediate buffs
        mid_buffs = [b for b in selected_buffs if self.buff_database[b].difficulty_rating <= 6]
        if mid_buffs:
            base_chance = 0.1 + (difficulty_score / 100) * 0.2  # 10-30% chance
            wave_ranges["16-30"] = {
                "base_chance": base_chance,
                "max_buffs": min(3, len(mid_buffs)),
                "allowed_buffs": mid_buffs
            }
        
        # Late game (31-45): Most buffs available
        late_buffs = [b for b in selected_buffs if self.buff_database[b].difficulty_rating <= 8]
        if late_buffs:
            base_chance = 0.15 + (difficulty_score / 100) * 0.3  # 15-45% chance
            wave_ranges["31-45"] = {
                "base_chance": base_chance,
                "max_buffs": min(4, len(late_buffs)),
                "allowed_buffs": late_buffs
            }
        
        # End game (46+): All selected buffs
        if max_wave >= 46:
            base_chance = 0.2 + (difficulty_score / 100) * 0.4  # 20-60% chance
            wave_ranges["46+"] = {
                "base_chance": base_chance,
                "max_buffs": min(6, len(selected_buffs)),
                "allowed_buffs": selected_buffs
            }
        
        # Boss multipliers based on difficulty
        boss_multipliers = {
            "mini_boss": 1.5 + (difficulty_score / 100) * 1.0,   # 1.5x - 2.5x
            "boss": 2.0 + (difficulty_score / 100) * 1.5,        # 2.0x - 3.5x  
            "super_boss": 3.0 + (difficulty_score / 100) * 2.0   # 3.0x - 5.0x
        }
        
        return {
            "wave_ranges": wave_ranges,
            "boss_multipliers": boss_multipliers,
            "strategy_used": strategy.value,
            "difficulty_scaling": difficulty_score / 100
        }
    
    def determine_strategy_from_performance(self, performance_data: Dict[str, Any]) -> SelectionStrategy:
        """Determine the best strategy based on player performance data"""
        if not performance_data:
            return SelectionStrategy.BALANCED
        
        avg_score = performance_data.get('average_score', 50)
        win_rate = performance_data.get('win_rate', 50)
        trend = performance_data.get('trend', 'stable')
        
        # High performing players get more challenging strategies
        if avg_score >= 80 and win_rate >= 80:
            if trend == 'improving':
                return SelectionStrategy.CHAOS
            else:
                return SelectionStrategy.SPECIALIZED
        
        # Struggling players get more manageable strategies
        elif avg_score <= 30 or win_rate <= 30:
            return SelectionStrategy.BALANCED
        
        # Medium performance - vary based on trend
        else:
            if trend == 'improving':
                return SelectionStrategy.AGGRESSIVE
            elif trend == 'declining':
                return SelectionStrategy.DEFENSIVE
            else:
                return SelectionStrategy.BALANCED
    
    def generate_complete_enemy_buff_config(self, difficulty_score: int, max_wave: int, 
                                          performance_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate a complete enemy and buff configuration for a level"""
        
        # Determine strategy
        if performance_data:
            strategy = self.determine_strategy_from_performance(performance_data)
        else:
            # Default strategy based on difficulty
            if difficulty_score >= 80:
                strategy = SelectionStrategy.SPECIALIZED
            elif difficulty_score >= 60:
                strategy = SelectionStrategy.AGGRESSIVE
            elif difficulty_score >= 40:
                strategy = SelectionStrategy.BALANCED
            else:
                strategy = SelectionStrategy.DEFENSIVE
        
        # Select enemies and buffs
        enemy_config = self.select_enemies_for_difficulty(difficulty_score, max_wave, strategy)
        buff_config = self.select_buffs_for_difficulty(difficulty_score, max_wave, strategy)
        
        return {
            "ai_selection_metadata": {
                "difficulty_score": difficulty_score,
                "max_wave": max_wave,
                "strategy_used": strategy.value,
                "enemy_usage_ratio": enemy_config["enemy_usage_ratio"],
                "buff_usage_ratio": buff_config["buff_usage_ratio"],
                "generation_method": "intelligent_ai_selection"
            },
            "wave_compositions": enemy_config["wave_compositions"],
            "enemy_buff_config": {
                "enabled": True,
                "custom_spawn_rates": buff_config["buff_spawn_config"],
                "ai_selected_buffs": buff_config["selected_buffs"],
                "ai_selected_enemies": enemy_config["selected_enemies"]
            }
        } 