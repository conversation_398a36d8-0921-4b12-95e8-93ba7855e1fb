# Tower size configuration
# Defines how many grid blocks each tower type occupies

# Tower size format: (width_in_blocks, height_in_blocks)
TOWER_SIZES = {
    'basic': (1, 1),
    'sniper': (1, 1),
    'freezer': (1, 1),
    'detector': (2, 2),      # Larger detection tower
    'antiair': (1, 1),
    'poison': (1, 1),
    'laser': (1, 1),
    'cannon': (1.5, 1.5),    # Cannon tower size (increased by 0.5 blocks)
    'lightning': (1, 1),
    'flame': (1, 1),
    'ice': (1, 1),
    'explosive': (3, 3),     # Massive explosive tower
    'missile': (2.5, 2.5),   # Increased missile tower size (increased by half a block)
    'splash': (1, 1),        # Regular size splash tower
    'destroyer': (2, 2)      # Medium destroyer tower (changed to 2x2)
}


def get_tower_size(tower_type: str) -> tuple:
    """Get the size of a tower type in grid blocks"""
    return TOWER_SIZES.get(tower_type, (1, 1))


def get_tower_grid_visual_size(tower_type: str, cell_size: int) -> int:
    """Get the visual size (radius) for placement preview based on grid size"""
    # Calculate reasonable radius based on grid size for visual placement preview
    grid_width, grid_height = get_tower_size(tower_type)

    # Use a more reasonable radius calculation for square towers
    # Base radius on the larger dimension, scaled appropriately
    max_dimension = max(grid_width, grid_height)

    # Scale factor to make visual sizes reasonable but distinct
    if max_dimension == 1:
        return 15  # Single cell towers - small radius
    elif max_dimension == 1.5:
        return 20  # 1.5x1.5 towers (cannon) - between small and medium
    elif max_dimension == 2:
        # Special handling for larger 2x2 towers
        if tower_type in ['destroyer']:
            return 30  # Larger 2x2 towers - between medium and large
        return 25  # Regular 2x2 towers - medium radius
    elif max_dimension == 2.5:
        return 28  # 2.5x2.5 towers (missile) - slightly larger than 2x2
    elif max_dimension == 3:
        return 35  # 3x3 towers - large radius
    else:
        return 15 + (max_dimension * 8)  # Fallback for larger towers


def get_tower_visual_size(tower_type: str, cell_size: int) -> int:
    """Get the actual collision size (radius) of a tower based on tower.size property"""
    # Use actual tower.size values for more realistic collision detection
    # These values match the tower.size property from each tower class
    tower_collision_sizes = {
        # Small towers (1x1) - Using ACTUAL tower.size values from class constructors
        'basic': 12,      # BasicTower.size = 12 ✓
        'sniper': 15,     # SniperTower.size = 15 ✓
        'freezer': 13,    # FreezerTower.size = 13 ✓
        'antiair': 12,    # AntiAirTower.size = 12 ✓ (CORRECTED)
        'poison': 12,     # PoisonTower.size = 12 ✓ (CORRECTED)
        'laser': 12,      # LaserTower.size = 12 ✓
        'lightning': 12,  # LightningTower.size = 12 ✓ (CORRECTED)
        'flame': 12,      # FlameTower.size = 12 ✓ (CORRECTED)
        'ice': 12,        # IceTower.size = 12 ✓
        'splash': 14,     # SplashTower.size = 14 ✓

        # Medium towers (2x2) - Using ACTUAL tower.size values
        'detector': 12,   # DetectorTower.size = 12 ✓
        'cannon': 15,     # CannonTower.size = 15 ✓ (1.5x1.5 tower)
        'missile': 17,    # MissileTower.size = 17 ✓ (increased - now 2.5x2.5 tower)

        # Large medium towers (2x2) - Larger than regular 2x2 but smaller than 3x3
        'destroyer': 25,  # DestroyerTower.size = 25 ✓ (increased from 20)

        # Large towers (3x3) - Using ACTUAL tower.size values
        'explosive': 16   # ExplosiveTower.size = 16 ✓
    }

    return tower_collision_sizes.get(tower_type, 12)  # Default fallback


def get_tower_occupied_cells(grid_x: int, grid_y: int, tower_type: str) -> list:
    """Get all grid cells occupied by a tower"""
    width, height = get_tower_size(tower_type)
    occupied_cells = []

    for dx in range(width):
        for dy in range(height):
            occupied_cells.append((grid_x + dx, grid_y + dy))

    return occupied_cells


def can_place_tower_at_position(grid_x: int, grid_y: int, tower_type: str,
                                grid_width: int, grid_height: int, existing_towers: list) -> bool:
    """Check if a multi-block tower can be placed at the given position"""
    width, height = get_tower_size(tower_type)

    # Check if tower fits within map bounds
    if grid_x + width > grid_width or grid_y + height > grid_height:
        return False

    # Get all cells this tower would occupy
    occupied_cells = get_tower_occupied_cells(grid_x, grid_y, tower_type)

    # Check if any existing tower occupies these cells
    for tower in existing_towers:
        tower_type_existing = getattr(tower, 'tower_type', 'basic')
        tower_grid_x, tower_grid_y = getattr(
            tower, 'grid_x', 0), getattr(tower, 'grid_y', 0)
        existing_occupied = get_tower_occupied_cells(
            tower_grid_x, tower_grid_y, tower_type_existing)

        # Check for overlap
        for cell in occupied_cells:
            if cell in existing_occupied:
                return False

    return True


def can_place_destroyer_tower(grid_x: int, grid_y: int, map_obj) -> bool:
    """Check if destroyer tower can be placed with special requirements:
    - At least 2 water blocks in the 2x2 area
    - No path or stone blocks in the 2x2 area
    """
    from .terrain_types import WATER, PATH, ROCK

    occupied_cells = get_tower_occupied_cells(grid_x, grid_y, 'destroyer')
    water_count = 0

    for cell_x, cell_y in occupied_cells:
        terrain_type = map_obj.get_terrain_at_grid(cell_x, cell_y)

        # Count water blocks
        if terrain_type == WATER:
            water_count += 1

        # Check for forbidden terrain types
        if terrain_type == PATH or terrain_type == ROCK:
            return False

    # Must have at least 2 water blocks
    return water_count >= 2
