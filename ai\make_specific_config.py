"""
Procedural Configuration Generator for Tower Defense Game

This module generates complete level configurations (maps and wave settings)
based on a difficulty parameter D ∈ [0,100] using mathematical algorithms.
"""

import json
import random
import math
from typing import List, Tuple, Dict, Any
from pathlib import Path
import os

# Terrain constants
GRASS = 0
PATH = 1
ROCK = 2
WATER = 3
FOREST = 4
SAND = 5


class ConfigGenerator:
    """AI system for generating tower defense configurations"""

    def __init__(self):
        self.enemy_types = [
            "BasicEnemy", "FastEnemy", "TankEnemy", "FlyingEnemy", "ShieldedEnemy",
            "InvisibleEnemy", "ArmoredEnemy", "RegeneratingEnemy", "TeleportingEnemy",
            "SplittingEnemy", "EnergyShieldEnemy", "GroundedEnemy", "FireElementalEnemy",
            "ToxicEnemy", "PhaseShiftEnemy", "BlastProofEnemy", "SpectralEnemy",
            "CrystallineEnemy", "ToxicMutantEnemy", "VoidEnemy", "AdaptiveEnemy"
        ]

        self.boss_types = [
            "TimeLordBoss", "NecromancerBoss", "ShadowKing", "CrystalOverlord"
        ]

        # Enemy progression tiers (when each enemy type becomes available)
        self.enemy_tiers = {
            1: ["BasicEnemy", "FastEnemy"],
            6: ["TankEnemy"],
            11: ["FlyingEnemy"],
            16: ["ShieldedEnemy", "ArmoredEnemy"],
            21: ["InvisibleEnemy", "EnergyShieldEnemy", "GroundedEnemy"],
            31: ["RegeneratingEnemy", "TeleportingEnemy", "FireElementalEnemy", "ToxicEnemy"],
            41: ["SplittingEnemy", "PhaseShiftEnemy", "BlastProofEnemy"],
            51: ["SpectralEnemy", "CrystallineEnemy"],
            61: ["ToxicMutantEnemy", "VoidEnemy", "AdaptiveEnemy"]
        }

    def derive_difficulty_factors(self, D: int) -> Dict[str, float]:
        """
        Derive difficulty factors from input D ∈ [0,100] using CORRECTED tower defense principles

        Args:
            D: Difficulty parameter from 0 to 100

        Returns:
            Dictionary containing derived difficulty factors with corrected logic
        """
        D = max(0, min(100, D))  # Clamp D to [0,100]
        normalized_D = D / 100.0

        # Use mathematical difficulty factors from ai/difficulty_factors.py
        from .difficulty_factors import derive_difficulty_factors as get_corrected_factors
        return get_corrected_factors(D)

    def generate_path_dfs(self, width: int, height: int, start: Tuple[int, int],
                          end: Tuple[int, int], complexity: float,
                          min_length: int = 30) -> List[Tuple[int, int]]:
        """
        Generate path using DFS-backtracker maze algorithm with timeout protection

        Args:
            width, height: Grid dimensions
            start, end: Start and end coordinates
            complexity: Affects straight vs turn probability (0.0 = more straight, 1.0 = more turns)
            min_length: Minimum required path length

        Returns:
            List of (x, y) coordinates forming the path
        """
        import time

        # Reduce attempts for faster generation
        max_attempts = min(10, max(3, int(complexity * 20)))
        timeout_per_attempt = 0.5  # 0.5 second timeout per attempt

        for attempt in range(max_attempts):
            start_time = time.time()
            try:
                path = self._dfs_pathfind_with_timeout(
                    width, height, start, end, complexity, timeout_per_attempt)
                if len(path) >= min_length:
                    return path
            except TimeoutError:
                continue  # Try next attempt

            # Check if we've spent too much time total
            if time.time() - start_time > 2.0:  # Max 2 seconds total
                break

        # Fallback: generate a simple path if DFS fails
        print(
            f"DFS pathfinding failed after {max_attempts} attempts, using simple fallback path...")
        return self._generate_fallback_path(start, end, min_length, width, height)

    def _dfs_pathfind_with_timeout(self, width: int, height: int, start: Tuple[int, int],
                                   end: Tuple[int, int], complexity: float, timeout: float) -> List[Tuple[int, int]]:
        """DFS-based pathfinding with timeout protection"""
        import time
        start_time = time.time()

        # Down, Right, Up, Left
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]

        # Initialize visited grid
        visited = [[False for _ in range(width)] for _ in range(height)]
        path = []

        def is_valid(x: int, y: int) -> bool:
            return 0 <= x < width and 0 <= y < height and not visited[y][x]

        def dfs(x: int, y: int, target_x: int, target_y: int) -> bool:
            # Check timeout
            if time.time() - start_time > timeout:
                raise TimeoutError("DFS timeout")

            visited[y][x] = True
            path.append((x, y))

            if x == target_x and y == target_y:
                return True

            # Simplified movement for better performance
            possible_moves = []
            for dx, dy in directions:
                nx, ny = x + dx, y + dy
                if is_valid(nx, ny):
                    # Simple distance-based preference
                    dist_to_goal = abs(nx - target_x) + abs(ny - target_y)
                    weight = 1.0 / (1.0 + dist_to_goal) + random.random() * 0.3
                    possible_moves.append((nx, ny, weight))

            # Sort by weight and try
            possible_moves.sort(key=lambda x: x[2], reverse=True)

            for nx, ny, _ in possible_moves:
                if dfs(nx, ny, target_x, target_y):
                    return True

            # Backtrack
            path.pop()
            visited[y][x] = False
            return False

        # Start DFS from start position
        if dfs(start[0], start[1], end[0], end[1]):
            return path
        else:
            return []

    def _generate_fallback_path(self, start: Tuple[int, int], end: Tuple[int, int],
                                min_length: int, width: int, height: int) -> List[Tuple[int, int]]:
        """Generate a simple fallback path with safety checks"""
        path = [start]
        current_x, current_y = start
        target_x, target_y = end

        # Safety counter to prevent infinite loops
        max_iterations = min_length * 3  # Maximum iterations allowed
        iteration_count = 0

        while (len(path) < min_length or (current_x != target_x or current_y != target_y)) and iteration_count < max_iterations:
            iteration_count += 1

            old_x, old_y = current_x, current_y

            if len(path) >= min_length:
                # Move directly towards target
                if current_x < target_x:
                    current_x += 1
                elif current_x > target_x:
                    current_x -= 1
                elif current_y < target_y:
                    current_y += 1
                elif current_y > target_y:
                    current_y -= 1
            else:
                # Create winding to extend path
                direction = random.choice(['x', 'y'])
                if direction == 'x':
                    if current_x < target_x:
                        current_x += 1
                    elif current_x > target_x:
                        current_x -= 1
                    else:
                        # Random horizontal movement
                        if current_x > 1:
                            current_x -= 1
                        elif current_x < width - 2:
                            current_x += 1
                else:
                    if current_y < target_y:
                        current_y += 1
                    elif current_y > target_y:
                        current_y -= 1
                    else:
                        # Random vertical movement
                        if current_y > 1:
                            current_y -= 1
                        elif current_y < height - 2:
                            current_y += 1

            # Bounds checking
            current_x = max(1, min(width - 2, current_x))
            current_y = max(1, min(height - 2, current_y))

            # Only add if position changed and is new
            if (current_x != old_x or current_y != old_y) and (current_x, current_y) not in path:
                path.append((current_x, current_y))
            elif iteration_count % 10 == 0:  # Force movement every 10 iterations
                # Force a move if we're stuck
                for dx, dy in [(1, 0), (-1, 0), (0, 1), (0, -1)]:
                    new_x = max(1, min(width - 2, current_x + dx))
                    new_y = max(1, min(height - 2, current_y + dy))
                    if (new_x, new_y) not in path:
                        current_x, current_y = new_x, new_y
                        path.append((current_x, current_y))
                        break

        # Ensure we end at the target
        if (current_x, current_y) != (target_x, target_y):
            # Force path to target
            while current_x != target_x:
                current_x += 1 if current_x < target_x else -1
                if (current_x, current_y) not in path:
                    path.append((current_x, current_y))
            while current_y != target_y:
                current_y += 1 if current_y < target_y else -1
                if (current_x, current_y) not in path:
                    path.append((current_x, current_y))

        return path

    def build_terrain_grid(self, width: int, height: int, path: List[Tuple[int, int]],
                           obstacle_density: float) -> List[List[int]]:
        """
        Build terrain grid with path and obstacles

        Args:
            width, height: Grid dimensions
            path: List of path coordinates
            obstacle_density: Density of obstacles to place

        Returns:
            2D grid of terrain types
        """
        # Initialize with grass
        terrain = [[GRASS for _ in range(width)] for _ in range(height)]

        # Mark path cells
        for x, y in path:
            if 0 <= x < width and 0 <= y < height:
                terrain[y][x] = PATH

        # Add border rocks
        for x in range(width):
            terrain[0][x] = ROCK
            terrain[height-1][x] = ROCK
        for y in range(height):
            terrain[y][0] = ROCK
            terrain[y][width-1] = ROCK

        # Calculate number of obstacles to place
        total_cells = width * height
        path_cells = len(path)
        border_cells = 2 * width + 2 * height - 4
        available_cells = total_cells - path_cells - border_cells
        num_obstacles = int(obstacle_density * available_cells)

        # Place random obstacles
        obstacles_placed = 0
        terrain_types = [WATER, FOREST, SAND, ROCK]

        while obstacles_placed < num_obstacles:
            x = random.randint(1, width - 2)
            y = random.randint(1, height - 2)

            # Don't place on path or existing obstacles
            if terrain[y][x] == GRASS:
                terrain[y][x] = random.choice(terrain_types)
                obstacles_placed += 1

        return terrain

    def build_terrain_grid_corrected(self, width: int, height: int, path: List[Tuple[int, int]],
                                     factors: Dict[str, float]) -> List[List[int]]:
        """
        Build terrain grid using CORRECTED tower defense principles:
        - Easy: More buildable space (simple decisions)
        - Hard: Strategic terrain constraints (complex decisions)
        """
        # Initialize with grass
        terrain = [[GRASS for _ in range(width)] for _ in range(height)]

        # Mark path cells
        for x, y in path:
            if 0 <= x < width and 0 <= y < height:
                terrain[y][x] = PATH

        # Calculate terrain distribution based on mathematical principles
        difficulty = factors.get('difficulty', 50)
        # Fallback to medium difficulty
        buildable_space = factors.get('buildable_space', 0.6)
        strategic_density = factors.get(
            'strategic_terrain_density', 0.4)  # Fallback

        # Get all non-path cells for terrain placement
        placeable_cells = []
        for y in range(height):
            for x in range(width):
                if terrain[y][x] == GRASS and not (x == 0 or x == width-1 or y == 0 or y == height-1):
                    placeable_cells.append((x, y))

        if not placeable_cells:
            return terrain

        # Strategic terrain placement based on difficulty
        if difficulty <= 30:  # Easy: Simple terrain, mostly grass
            # 80% buildable = mostly grass with minimal constraints
            strategic_cells = int(len(placeable_cells) * 0.2)
            terrain_types = [WATER] * 30 + [FOREST] * \
                20 + [ROCK] * 50  # Light constraints
        elif difficulty <= 70:  # Medium: Mixed strategic terrain
            # 60% buildable = balanced strategic terrain
            strategic_cells = int(len(placeable_cells) * 0.4)
            terrain_types = [WATER] * 35 + [FOREST] * \
                35 + [ROCK] * 30  # Balanced mix
        else:  # Hard: Heavy strategic constraints
            # 35% buildable = force specialized tower placement
            strategic_cells = int(len(placeable_cells) * 0.65)
            terrain_types = [WATER] * 40 + [FOREST] * \
                40 + [ROCK] * 20  # Force specialization

        # Place strategic terrain
        if strategic_cells > 0 and len(placeable_cells) > 0:
            selected_cells = random.sample(placeable_cells, min(
                strategic_cells, len(placeable_cells)))

            for x, y in selected_cells:
                terrain_type = random.choice(terrain_types)
                terrain[y][x] = terrain_type

        return terrain

    def lookup_enemy_increase(self, wave: int) -> int:
        """Get enemy count increase for wave"""
        if wave <= 5:
            return 1
        elif wave <= 10:
            return 2
        elif wave <= 15:
            return 3
        elif wave <= 20:
            return 4
        elif wave <= 25:
            return 5
        elif wave <= 30:
            return 6
        elif wave <= 35:
            return 7
        elif wave <= 40:
            return 8
        elif wave <= 45:
            return 9
        elif wave <= 50:
            return 10
        elif wave <= 55:
            return 11
        elif wave <= 60:
            return 12
        elif wave <= 65:
            return 13
        elif wave <= 70:
            return 14
        elif wave <= 75:
            return 15
        else:
            return 16

    def get_standard_first_5_waves_config(self) -> List[Tuple[str, float]]:
        """
        Returns the standardized configuration for waves 1-5 that should be used 
        regardless of difficulty or AI modifications.

        Returns:
            List of (EnemyType, probability) tuples for waves 1-5
        """
        return [
            ("BasicEnemy", 0.8),
            ("FastEnemy", 0.2)
        ]

    def lookup_delay_reduction(self, wave: int) -> int:
        """Get spawn delay reduction for wave"""
        if wave <= 10:
            return 5
        elif wave <= 20:
            return 8
        elif wave <= 30:
            return 10
        elif wave <= 40:
            return 12
        elif wave <= 50:
            return 15
        elif wave <= 60:
            return 18
        elif wave <= 70:
            return 20
        else:
            return 22

    def compute_wave_parameters(self, wave_num: int, difficulty_factors: Dict[str, float],
                                total_waves: int = 80) -> Dict[str, Any]:
        """
        Compute parameters for a specific wave

        Args:
            wave_num: Wave number (1-based)
            difficulty_factors: Derived difficulty factors
            total_waves: Total number of waves

        Returns:
            Dictionary with wave parameters
        """
        D = difficulty_factors['difficulty']
        base_count = difficulty_factors['base_count']
        base_delay = difficulty_factors['base_delay']
        health_scale = difficulty_factors['health_scale']
        speed_scale = difficulty_factors['speed_scale']

        # Calculate count
        enemy_increase = self.lookup_enemy_increase(wave_num)
        count = int(base_count + enemy_increase *
                    (1 + 0.5 * (D / 100)) * (wave_num - 1))

        # Calculate delay
        delay_reduction = self.lookup_delay_reduction(wave_num)
        delay = max(
            20, int(base_delay - delay_reduction * (1 + 0.5 * (D / 100))))

        # Calculate multipliers
        max_hp = 60.0
        max_spd = 5.0
        hp_mult = min(max_hp, 1 + health_scale * (wave_num - 1))
        spd_mult = min(max_spd, 1 + speed_scale * (wave_num - 1))

        # Apply special round multipliers
        special_multipliers = {
            10: {'enemy_multiplier': 1.5, 'spawn_delay_multiplier': 0.8},
            20: {'enemy_multiplier': 2.0, 'spawn_delay_multiplier': 0.7},
            30: {'enemy_multiplier': 2.5, 'spawn_delay_multiplier': 0.6},
            40: {'enemy_multiplier': 3.0, 'spawn_delay_multiplier': 0.5},
            50: {'enemy_multiplier': 4.0, 'spawn_delay_multiplier': 0.4},
            60: {'enemy_multiplier': 5.0, 'spawn_delay_multiplier': 0.3},
            70: {'enemy_multiplier': 6.0, 'spawn_delay_multiplier': 0.25},
            80: {'enemy_multiplier': 8.0, 'spawn_delay_multiplier': 0.2}
        }

        if wave_num in special_multipliers:
            count = int(
                count * special_multipliers[wave_num]['enemy_multiplier'])
            delay = int(
                delay * special_multipliers[wave_num]['spawn_delay_multiplier'])

        # Check for boss waves
        boss_waves = {20: "TimeLordBoss", 25: "ShadowKing", 30: "NecromancerBoss",
                      35: "CrystalOverlord", 40: "TimeLordBoss", 45: "ShadowKing",
                      50: "NecromancerBoss", 55: "CrystalOverlord", 60: "TimeLordBoss",
                      65: "ShadowKing", 70: "NecromancerBoss", 75: "CrystalOverlord",
                      80: "TimeLordBoss"}

        boss = boss_waves.get(wave_num)

        return {
            'enemy_count': count,
            'spawn_delay': delay,
            'hp_multiplier': hp_mult,
            'speed_multiplier': spd_mult,
            'boss': boss
        }

    def assign_compositions(self, wave_num: int, difficulty_factors: Dict[str, float]) -> List[Tuple[str, float]]:
        """
        Assign enemy compositions for a wave

        Args:
            wave_num: Wave number
            difficulty_factors: Derived difficulty factors

        Returns:
            List of (EnemyType, probability) tuples
        """
        # STANDARDIZED: Always use the same configuration for waves 1-5
        if wave_num <= 5:
            return self.get_standard_first_5_waves_config()

        D = difficulty_factors['normalized_difficulty']

        # Get available enemy types for this wave
        available_enemies = []
        for tier_wave, enemies in self.enemy_tiers.items():
            if wave_num >= tier_wave:
                available_enemies.extend(enemies)

        if not available_enemies:
            available_enemies = ["BasicEnemy", "FastEnemy"]

        # Base composition weights for waves 6+
        if wave_num <= 10:
            base_weights = {"BasicEnemy": 0.6,
                            "FastEnemy": 0.3, "TankEnemy": 0.1}
        elif wave_num <= 15:
            base_weights = {"BasicEnemy": 0.4, "FastEnemy": 0.3,
                            "TankEnemy": 0.2, "FlyingEnemy": 0.1}
        elif wave_num <= 20:
            base_weights = {"BasicEnemy": 0.25, "FastEnemy": 0.2, "TankEnemy": 0.2,
                            "FlyingEnemy": 0.15, "ShieldedEnemy": 0.15, "ArmoredEnemy": 0.05}
        else:
            # Late game: distribute weight among all available enemies
            equal_weight = 1.0 / len(available_enemies)
            base_weights = {enemy: equal_weight for enemy in available_enemies}

        # Bias heavier types upward by difficulty
        final_weights = {}
        heavy_enemies = ["TankEnemy", "ArmoredEnemy", "SpectralEnemy", "CrystallineEnemy",
                         "ToxicMutantEnemy", "VoidEnemy", "AdaptiveEnemy"]

        for enemy in available_enemies:
            weight = base_weights.get(enemy, 0.05)
            if enemy in heavy_enemies:
                # Increase heavy enemy weight by difficulty
                weight *= (1 + D * 0.5)
            final_weights[enemy] = weight

        # Normalize weights
        total_weight = sum(final_weights.values())
        normalized_weights = [(enemy, weight / total_weight)
                              for enemy, weight in final_weights.items()]

        return normalized_weights

    def group_wave_compositions(self, total_waves: int, difficulty_factors: Dict[str, float]) -> Dict[str, List[Tuple[str, float]]]:
        """
        Group waves with similar compositions into efficient ranges

        Args:
            total_waves: Total number of waves
            difficulty_factors: Derived difficulty factors

        Returns:
            Dictionary with wave range keys and composition values
        """
        wave_compositions = {}

        # STANDARDIZED: Always use the same configuration for waves 1-5
        wave_compositions["1-5"] = self.get_standard_first_5_waves_config()

        # Define logical wave groupings based on enemy availability (starting from wave 6)
        wave_ranges = [
            (6, 10),   # Add Tank
            (11, 15),  # Add Flying
            (16, 20),  # Add Shielded + Armored
            (21, 30),  # Add Invisible + Energy Shield + Grounded
            (31, 40),  # Add Regen + Teleport + Fire + Toxic
            (41, 50),  # Add Splitting + Phase + Blast Proof
            (51, 60),  # Add Spectral + Crystalline
            (61, 80)   # Add Toxic Mutant + Void + Adaptive
        ]

        for start_wave, end_wave in wave_ranges:
            # Adjust end wave if total waves is less than expected
            if start_wave > total_waves:
                break
            actual_end = min(end_wave, total_waves)

            # Use the middle wave of the range for composition calculation
            representative_wave = (start_wave + actual_end) // 2
            composition = self.assign_compositions(
                representative_wave, difficulty_factors)

            range_key = f"{start_wave}-{actual_end}" if start_wave != actual_end else str(
                start_wave)
            wave_compositions[range_key] = composition

        return wave_compositions

    def generate_config(self, difficulty: int, width: int = 20, height: int = 15,
                        total_waves: int = 80, output_path: str = None) -> Dict[str, Any]:
        """
        Generate complete configuration based on difficulty

        Args:
            difficulty: Difficulty parameter D ∈ [0,100]
            width, height: Map dimensions
            total_waves: Number of waves to generate
            output_path: Optional path to save configuration

        Returns:
            Complete configuration dictionary
        """
        # Step 1: Derive difficulty factors
        factors = self.derive_difficulty_factors(difficulty)

        # Step 2: Generate path - FIXED: Shorter paths = Harder difficulty
        start = (1, height // 2)
        end = (width - 2, height // 2)
        # CORRECTED: Easy gets long paths (time to learn), Hard gets short paths (time pressure)
        # 55→25 length (inverse scaling)
        base_path_length = max(25, int(55 - difficulty * 0.3))
        min_path_length = base_path_length

        path = self.generate_path_dfs(
            width, height, start, end, factors['complexity'], min_path_length)

        # Step 3: Build terrain grid - FIXED: Use corrected buildable space logic
        terrain = self.build_terrain_grid_corrected(
            width, height, path, factors)

        # Step 4: Generate map config
        map_config = {
            "default_map": {
                "width": width,
                "height": height,
                "terrain": terrain,
                "path": path
            }
        }

        # Step 5: Generate wave configurations efficiently
        wave_compositions = self.group_wave_compositions(total_waves, factors)
        boss_waves = {}
        special_rounds = {}

        # Only generate boss waves and special rounds (much smaller)
        boss_wave_numbers = [20, 25, 30, 35,
                             40, 45, 50, 55, 60, 65, 70, 75, 80]
        for wave_num in boss_wave_numbers:
            if wave_num <= total_waves:
                wave_params = self.compute_wave_parameters(
                    wave_num, factors, total_waves)
                if wave_params['boss']:
                    boss_waves[str(wave_num)] = wave_params['boss']

        # Generate special rounds for every 10th wave
        for wave_num in range(10, total_waves + 1, 10):
            special_rounds[str(wave_num)] = {
                "enemy_multiplier": 1.5 + (wave_num // 10) * 0.5,
                "spawn_delay_multiplier": max(0.2, 0.8 - (wave_num // 10) * 0.1)
            }

        # Step 6: Assemble wave config with timing balance
        # Use timing balance algorithm to calculate optimal spawn parameters
        try:
            from .wave_timing_balance import calculate_difficulty_based_enemy_spawn_balance

            # Calculate balanced parameters for a representative wave (wave 10)
            balance_result = calculate_difficulty_based_enemy_spawn_balance(
                difficulty, 10, int(factors['base_count'])
            )

            # Use the balanced spawn delay as base
            base_spawn_delay = balance_result['spawn_delay']
            # 30% of base as minimum
            min_spawn_delay = max(10, int(base_spawn_delay * 0.3))

            print(f"🔧⏱️ Timing-balanced spawn config: {base_spawn_delay} frames base delay, "
                  f"{balance_result['total_spawn_time_seconds']:.1f}s total spawn time")

        except ImportError:
            # Fallback to original calculation if import fails
            base_spawn_delay = int(factors['base_delay'])
            min_spawn_delay = 20

        wave_config = {
            "total_waves": total_waves,  # Store total waves for UI access
            "spawn_config": {
                "base_enemy_count": int(factors['base_count']),
                "base_spawn_delay": base_spawn_delay,
                "min_spawn_delay": min_spawn_delay,
                # Boss waves limited to 5 total enemies (4 regular + 1 boss)
                "boss_enemy_count": 1
            },
            "round_progression": {
                "enemy_increase_per_round": {
                    "wave_ranges": {
                        "1-5": 1, "6-10": 2, "11-15": 3, "16-20": 4, "21-25": 5,
                        "26-30": 6, "31-35": 7, "36-40": 8, "41-45": 9, "46-50": 10,
                        "51-55": 11, "56-60": 12, "61-65": 13, "66-70": 14,
                        "71-75": 15, "76-80": 16
                    },
                    "default": 1
                },
                "spawn_delay_reduction_per_round": {
                    "wave_ranges": {
                        "1-10": 5, "11-20": 8, "21-30": 10, "31-40": 12,
                        "41-50": 15, "51-60": 18, "61-70": 20, "71-80": 22
                    },
                    "default": 5
                },
                "special_rounds": special_rounds
            },
            "wave_compositions": wave_compositions,
            "boss_waves": boss_waves,
            "enemy_scaling": {
                "health_per_wave": 0.3 * (1 + factors['normalized_difficulty']),
                "speed_per_wave": 0.05 * (1 + factors['normalized_difficulty']),
                "reward_per_wave": 0.12,
                "size_per_wave": 0.02,
                "max_health_multiplier": 60.0,
                "max_speed_multiplier": 5.0,
                "max_reward_multiplier": 20.0,
                "max_size_multiplier": 2.0,
                "damage_scaling_per_wave": 0.1,
                "max_damage_multiplier": 4.0
            },
            "money_config": {
                "normal_wave_bonus": 50,
                "boss_wave_bonus": 200
            }
        }

        # Step 7: Merge and create final config
        # FIXED: Corrected logic - easier levels should have MORE money, harder levels should have LESS money
        # Base money calculation: Start with 80 for easy (difficulty 0), scale down to 30 for hard (difficulty 100)
        base_money = max(20, int(80 - factors['normalized_difficulty'] * 50))
        base_lives = max(10, int(25 - factors['normalized_difficulty'] * 10))

        final_config = {
            "game_config": {
                "starting_money": base_money,
                "starting_lives": base_lives
            },
            # ★ SYNCHRONIZED: progression_config uses same starting money as game_config ★
            "progression_config": {
                "starting_money": base_money,  # Same as game_config for consistency
                "starting_lives": base_lives,
                "economic_scaling": "automatic"
            },
            # ★ NEW: Add tower_config for modular AI tower cost control ★
            "tower_config": {
                "cost_progression": {
                    "early_game_waves": 15,
                    "mid_game_waves": 30,
                    "early_increase_per_wave": 0.02,
                    "mid_increase_per_wave": 0.03,
                    "late_increase_per_wave": 0.05,
                    "max_cost_multiplier": 3.0
                },
                "dynamic_cost_increase": {
                    "per_tower_built_multiplier": 0.15,
                    "max_per_tower_multiplier": 20
                }
            },
            # ★ NEW: Add enemy_buffs for modular AI enemy modifications ★
            "enemy_buffs": {
                "max_health_modifier": 1.0,
                "max_speed_modifier": 1.0,
                "global_damage_modifier": 1.0,
                "special_abilities_enabled": True
            },
            "wave_config": wave_config,
            "map_config": map_config,
            # NOTE: Static configurations (tower_config, balance_config) are now loaded
            # from separate files (static_tower_config.json, static_balance_config.json)
            # This allows AI systems to focus on generating dynamic content like waves and maps
            "_generation_metadata": {
                "difficulty": difficulty,
                "difficulty_factors": factors,
                "generation_timestamp": None,
                "algorithm_version": "1.0"
            }
        }

        # Step 8: Export if output path provided
        if output_path:
            import datetime
            final_config["_generation_metadata"]["generation_timestamp"] = datetime.datetime.now(
            ).isoformat()

            with open(output_path, 'w') as f:
                json.dump(final_config, f, indent=2, separators=(
                    ',', ': '))  # Readable with compact commas
            print(f"Configuration generated and saved to {output_path}")

        # ENFORCE MINIMUM STARTING MONEY (Fix for low starting cash issue)
        final_config = self._enforce_minimum_starting_money(final_config)

        return final_config

    def _enforce_minimum_starting_money(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Enforce minimum starting money of 20 dollars across all config sections"""
        MINIMUM_MONEY = 20

        # Check and fix game_config
        if 'game_config' in config and 'starting_money' in config['game_config']:
            if config['game_config']['starting_money'] < MINIMUM_MONEY:
                old_money = config['game_config']['starting_money']
                config['game_config']['starting_money'] = MINIMUM_MONEY
                print(
                    f"⚠️ FIXED: Starting money increased from ${old_money} to ${MINIMUM_MONEY} (minimum enforced)")

        # Check and fix progression_config, and sync with game_config
        if 'progression_config' in config and 'starting_money' in config['progression_config']:
            if config['progression_config']['starting_money'] < MINIMUM_MONEY:
                old_money = config['progression_config']['starting_money']
                config['progression_config']['starting_money'] = MINIMUM_MONEY

                # SYNC: Also update game_config to match
                if 'game_config' not in config:
                    config['game_config'] = {}
                config['game_config']['starting_money'] = MINIMUM_MONEY

                print(
                    f"⚠️ FIXED: Starting money increased from ${old_money} to ${MINIMUM_MONEY} (minimum enforced, synced to both configs)")

        # Check and fix any other money references in the config
        if 'starting_money' in config:
            if config['starting_money'] < MINIMUM_MONEY:
                old_money = config['starting_money']
                config['starting_money'] = MINIMUM_MONEY
                print(
                    f"⚠️ FIXED: Root starting money increased from ${old_money} to ${MINIMUM_MONEY} (minimum enforced)")

        return config


def main():
    """Interactive configuration generator with user input"""
    generator = ConfigGenerator()

    print("=== Procedural Tower Defense Configuration Generator ===")
    print()

    while True:
        try:
            # Get difficulty input from user
            difficulty = input(
                "Enter difficulty level (1-100, or 'quit' to exit): ").strip()

            if difficulty.lower() in ['quit', 'q', 'exit']:
                print("Goodbye!")
                break

            difficulty = int(difficulty)

            if not (1 <= difficulty <= 100):
                print("Error: Difficulty must be between 1 and 100.")
                continue

            # Get optional custom parameters
            print(f"\nGenerating configuration for difficulty {difficulty}...")

            width = input("Map width (default 20): ").strip()
            width = int(width) if width and width.isdigit() else 20

            height = input("Map height (default 15): ").strip()
            height = int(height) if height and height.isdigit() else 15

            waves = input("Number of waves (default 80): ").strip()
            waves = int(waves) if waves and waves.isdigit() else 80

            # Generate filename
            output_name = input(
                "Output filename (default: auto-generated): ").strip()
            if not output_name:
                output_name = f"generated_d{difficulty}_w{waves}.json"
            if not output_name.endswith('.json'):
                output_name += '.json'

            # Fix path handling - use absolute path and ensure directory exists
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_dir = os.path.join(os.path.dirname(current_dir), 'config')

            # Create config directory if it doesn't exist
            os.makedirs(config_dir, exist_ok=True)

            output_path = os.path.join(config_dir, output_name)

            # Generate configuration
            config = generator.generate_config(
                difficulty=difficulty,
                width=width,
                height=height,
                total_waves=waves,
                output_path=output_path
            )

            # Show summary
            factors = config["_generation_metadata"]["difficulty_factors"]
            map_info = config["map_config"]["default_map"]

            print(f"\n✅ Configuration generated successfully!")
            print(f"   Difficulty: {difficulty}/100")
            print(f"   Complexity: {factors['complexity']:.2f}")
            print(f"   Map size: {width}x{height}")
            print(f"   Path length: {len(map_info['path'])} cells")
            print(f"   Base enemies: {factors['base_count']:.1f}")
            print(f"   Base delay: {factors['base_delay']} frames")
            print(f"   Saved to: {output_name}")
            print()

        except ValueError:
            print("Error: Please enter a valid number.")
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")


def demo_batch():
    """Generate demo configurations for different difficulties"""
    generator = ConfigGenerator()

    print("=== Generating Demo Configurations ===")

    # Demo difficulties
    difficulties = [
        (10, "Tutorial", "Very easy for new players"),
        (25, "Casual", "Relaxed gameplay"),
        (50, "Normal", "Balanced challenge"),
        (75, "Hard", "For experienced players"),
        (90, "Nightmare", "Ultimate challenge")
    ]

    # Fix path handling - use absolute path and ensure directory exists
    current_dir = os.path.dirname(os.path.abspath(__file__))
    config_dir = os.path.join(os.path.dirname(current_dir), 'config')
    base_config_dir = os.path.join(config_dir, 'base')
    os.makedirs(base_config_dir, exist_ok=True)

    for difficulty, name, desc in difficulties:
        print(f"Generating {name} ({desc})...")
        output_path = os.path.join(
            base_config_dir, f"demo_{name.lower()}.json")
        config = generator.generate_config(
            difficulty=difficulty,
            output_path=output_path
        )
        factors = config["_generation_metadata"]["difficulty_factors"]
        print(f"  ✅ Saved with complexity {factors['complexity']:.2f}")

    print("\n🎉 All demo configurations generated!")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--demo":
        demo_batch()
    else:
        main()
