"""
UI Rendering - Handles all drawing operations for the game UI
"""
import pygame
import math
from typing import Dict, List, Tuple, Optional
from .tower_icons import TowerIconManager
from .ui_utils import UIUtils, FontManager


class UIRenderer:
    """Handles all UI drawing operations"""

    def __init__(self, screen_width: int, screen_height: int):
        self.screen_width = screen_width
        self.screen_height = screen_height

        # Colors
        self.BLACK = (0, 0, 0)
        self.WHITE = (255, 255, 255)
        self.GREEN = (0, 255, 0)
        self.BLUE = (0, 0, 255)
        self.GRAY = (128, 128, 128)
        self.LIGHT_GRAY = (200, 200, 200)
        self.DARK_GRAY = (64, 64, 64)
        self.RED = (255, 0, 0)
        self.YELLOW = (255, 255, 0)

        # UI Colors
        self.UI_BG = (40, 40, 40)
        self.UI_BORDER = (80, 80, 80)
        self.UI_HOVER = (60, 60, 60)
        self.UI_SELECTED = (100, 150, 100)

        # Fonts
        self.large_font = pygame.font.Font(None, 36)
        self.medium_font = pygame.font.Font(None, 28)
        self.small_font = pygame.font.Font(None, 24)
        self.tiny_font = pygame.font.Font(None, 20)

        # Layout constants
        self.bottom_bar_height = 120
        self.bottom_bar_y = screen_height - self.bottom_bar_height
        self.tower_slot_width = 80
        self.tower_slot_height = 80
        self.tower_slot_margin = 10

        # Button spacing and positioning with equal distances
        button_spacing = 15  # Equal spacing between button groups

        # Rock removal button constants
        self.rock_button_width = 100
        self.rock_button_height = 40
        self.rock_button_x = screen_width - 370  # Start position
        self.rock_button_y = 80

        # Water placement button constants (grouped with grass)
        self.water_button_width = 60
        self.water_button_height = 40
        self.water_button_x = self.rock_button_x + \
            self.rock_button_width + button_spacing
        self.water_button_y = 80

        # Grass placement button constants (directly next to water)
        self.grass_button_width = 60
        self.grass_button_height = 40
        self.grass_button_x = self.water_button_x + \
            self.water_button_width  # No gap between water/grass
        self.grass_button_y = 80

        # Speed button constants (positioned with equal spacing after grass button)
        self.speed_button_width = 80
        self.speed_button_height = 40
        self.speed_button_x = self.grass_button_x + \
            self.grass_button_width + button_spacing
        self.speed_button_y = 80

        # Enemy info button constants
        self.enemy_info_button_width = 120
        self.enemy_info_button_height = 40
        self.enemy_info_button_x = 500  # Fixed position to avoid overlaps
        self.enemy_info_button_y = 80

        # Upgrade all button constants
        self.upgrade_all_button_width = 110
        self.upgrade_all_button_height = 40
        self.upgrade_all_button_x = 630  # Right of enemy info button
        self.upgrade_all_button_y = 80

        # Tower icon manager
        self.tower_icon_manager = TowerIconManager(icon_size=32)

    def draw_game_stats(self, screen: pygame.Surface, money: int, lives: int, wave_info: Dict, game_speed: int = 1,
                        rock_removal_mode: bool = False, rock_removal_cost: int = 25,
                        water_placement_mode: bool = False, grass_placement_mode: bool = False, terrain_currency: int = 0,
                        xp_progress: Dict = None):
        """Draw the main game statistics in top area"""
        # Background for stats area
        stats_rect = pygame.Rect(0, 0, self.screen_width, 130)
        pygame.draw.rect(screen, (20, 20, 20), stats_rect)
        pygame.draw.rect(screen, self.UI_BORDER, stats_rect, 2)

        # Money
        money_text = self.large_font.render(
            f"Money: ${money}", True, self.GREEN)
        screen.blit(money_text, (20, 20))

        # Lives
        lives_color = self.RED if lives <= 3 else self.WHITE
        lives_text = self.large_font.render(
            f"Lives: {lives}", True, lives_color)
        screen.blit(lives_text, (20, 60))

        # Terrain Currency (top right area near buttons)
        terrain_currency_text = self.large_font.render(
            f"Terrain: {terrain_currency}", True, (100, 150, 255))
        # Position it above the terrain placement buttons
        terrain_currency_x = self.water_button_x - 20  # Slightly left of water button
        terrain_currency_y = 20  # Top area
        screen.blit(terrain_currency_text,
                    (terrain_currency_x, terrain_currency_y))

        # Wave information with scaling indicator (current/total format)
        current_wave = wave_info['wave_number']
        # Default to 80 if not found
        total_waves = wave_info.get('total_waves', 80)
        wave_text = self.large_font.render(
            f"Wave: {current_wave}/{total_waves}", True, self.WHITE)
        screen.blit(wave_text, (20, 100))

        # Enemy scaling indicator (subtle)
        if wave_info['wave_number'] > 1:
            scaling_factor = (wave_info['wave_number'] - 1) * 8  # 8% per wave
            scaling_text = self.tiny_font.render(
                f"Enemy Boost: +{scaling_factor}%", True, (255, 200, 100))
            screen.blit(scaling_text, (150, 105))

        # Wave progress bar
        self.draw_wave_progress_bar(screen, wave_info)

        # XP progress bar
        if xp_progress:
            self.draw_xp_progress_bar(screen, xp_progress)

        # Enemy info button
        self.draw_enemy_info_button(screen)

        # Upgrade all button
        self.draw_upgrade_all_button(screen)

        # Rock removal button
        self.draw_rock_removal_button(
            screen, rock_removal_mode, rock_removal_cost, money)

        # Water placement button
        self.draw_water_placement_button(
            screen, water_placement_mode, terrain_currency)

        # Grass placement button
        self.draw_grass_placement_button(
            screen, grass_placement_mode, terrain_currency)

        # Speed button
        self.draw_speed_button(screen, game_speed)

    def draw_wave_progress_bar(self, screen: pygame.Surface, wave_info: Dict):
        """Draw wave progress bar in stats area"""
        bar_x = 250
        bar_y = 50
        bar_width = 200
        bar_height = 20

        # Background
        pygame.draw.rect(screen, self.DARK_GRAY,
                         (bar_x, bar_y, bar_width, bar_height))

        # Progress
        progress = wave_info.get('progress', 0)
        progress_width = int(bar_width * progress)
        pygame.draw.rect(screen, self.GREEN,
                         (bar_x, bar_y, progress_width, bar_height))

        # Border
        pygame.draw.rect(screen, self.WHITE,
                         (bar_x, bar_y, bar_width, bar_height), 2)

        # Text
        progress_text = self.small_font.render(
            "Wave Progress", True, self.WHITE)
        screen.blit(progress_text, (bar_x, bar_y - 25))

    def draw_xp_progress_bar(self, screen: pygame.Surface, xp_progress: Dict):
        """Draw XP progress bar in stats area"""
        bar_x = 250
        bar_y = 90  # Below wave progress bar
        bar_width = 200
        bar_height = 16

        # Background
        pygame.draw.rect(screen, self.DARK_GRAY,
                         (bar_x, bar_y, bar_width, bar_height))

        # Progress
        progress_percentage = xp_progress.get('progress_percentage', 0) / 100.0
        progress_width = int(bar_width * progress_percentage)
        pygame.draw.rect(screen, (100, 150, 255),  # Blue color for XP
                         (bar_x, bar_y, progress_width, bar_height))

        # Border
        pygame.draw.rect(screen, self.WHITE,
                         (bar_x, bar_y, bar_width, bar_height), 2)

        # Level text (left side)
        level_text = self.small_font.render(
            f"Level {xp_progress.get('current_level', 1)}", True, self.WHITE)
        screen.blit(level_text, (bar_x, bar_y - 20))

        # XP text (right side)
        current_xp = xp_progress.get('current_xp', 0)
        xp_per_level = xp_progress.get('xp_per_level', 1000)
        xp_text = self.tiny_font.render(
            f"{current_xp}/{xp_per_level} XP", True, self.WHITE)
        xp_rect = xp_text.get_rect(right=bar_x + bar_width, y=bar_y - 20)
        screen.blit(xp_text, xp_rect)

    def draw_terrain_legend(self, screen: pygame.Surface):
        """Draw terrain legend in top right"""
        legend_x = min(self.screen_width - 200, 980 -
                       190)  # Keep within game area
        legend_y = 20

        # Background
        legend_rect = pygame.Rect(legend_x - 10, legend_y - 10, 190, 110)
        pygame.draw.rect(screen, (20, 20, 20), legend_rect)
        pygame.draw.rect(screen, self.UI_BORDER, legend_rect, 1)

        legend_title = self.small_font.render("Terrain:", True, self.WHITE)
        screen.blit(legend_title, (legend_x, legend_y))

        terrain_legend = [
            ((34, 139, 34), "Grass"),
            ((139, 69, 19), "Path"),
            ((105, 105, 105), "Rock"),
            ((30, 144, 255), "Water"),
            ((0, 100, 0), "Forest")
        ]

        for i, (color, name) in enumerate(terrain_legend):
            y = legend_y + 20 + i * 15
            pygame.draw.rect(screen, color, (legend_x, y, 10, 10))
            pygame.draw.rect(screen, self.WHITE, (legend_x, y, 10, 10), 1)
            desc_text = self.tiny_font.render(name, True, self.WHITE)
            screen.blit(desc_text, (legend_x + 15, y - 1))

    def draw_performance_info(self, screen: pygame.Surface, performance_data: Dict):
        """Draw performance information in top right corner"""
        perf_x = self.screen_width - 200
        perf_y = 140  # Below terrain legend

        # Calculate height based on optimization info
        optimization = performance_data.get('optimization', {})
        has_optimization = bool(optimization)
        height = 120 if has_optimization else 90

        # Background
        perf_rect = pygame.Rect(perf_x - 10, perf_y - 10, 190, height)
        pygame.draw.rect(screen, (20, 20, 20), perf_rect)
        pygame.draw.rect(screen, self.UI_BORDER, perf_rect, 1)

        # Title
        perf_title = self.small_font.render("Performance:", True, self.WHITE)
        screen.blit(perf_title, (perf_x, perf_y))

        # FPS
        fps = performance_data.get('fps', 0)
        fps_color = self.GREEN if fps >= 50 else self.YELLOW if fps >= 30 else self.RED
        fps_text = self.tiny_font.render(f"FPS: {fps}", True, fps_color)
        screen.blit(fps_text, (perf_x, perf_y + 20))

        # Frame time
        frame_time = performance_data.get('avg_frame_time_ms', 0)
        frame_color = self.GREEN if frame_time <= 20 else self.YELLOW if frame_time <= 33 else self.RED
        frame_text = self.tiny_font.render(
            f"Frame: {frame_time:.1f}ms", True, frame_color)
        screen.blit(frame_text, (perf_x, perf_y + 35))

        # Entity counts
        entities = performance_data.get('entity_counts', {})
        enemies_text = self.tiny_font.render(
            f"Enemies: {entities.get('enemies', 0)}", True, self.WHITE)
        screen.blit(enemies_text, (perf_x, perf_y + 50))

        projectiles_text = self.tiny_font.render(
            f"Projectiles: {entities.get('projectiles', 0)}", True, self.WHITE)
        screen.blit(projectiles_text, (perf_x, perf_y + 65))

        # Optimization info
        if has_optimization:
            opt_level = optimization.get('level', 'auto')
            opt_color = self.GREEN if opt_level == 'high' else self.YELLOW if opt_level == 'medium' else self.RED
            opt_text = self.tiny_font.render(
                f"Opt: {opt_level.upper()}", True, opt_color)
            screen.blit(opt_text, (perf_x, perf_y + 80))

            # Show optimization features
            features = []
            if optimization.get('object_pooling', False):
                features.append("POOL")
            if optimization.get('spatial_partitioning', False):
                features.append("GRID")
            if optimization.get('batch_processing', False):
                features.append("BATCH")
            if optimization.get('adaptive_updates', False):
                features.append("ADAPT")

            if features:
                features_text = self.tiny_font.render(
                    " ".join(features), True, self.WHITE)
                screen.blit(features_text, (perf_x, perf_y + 95))

    def draw_speed_button(self, screen: pygame.Surface, game_speed: int):
        """Draw the game speed control button"""
        button_rect = pygame.Rect(self.speed_button_x, self.speed_button_y,
                                  self.speed_button_width, self.speed_button_height)

        # Button background - different color based on speed
        if game_speed == 1:
            button_color = (60, 60, 100)  # Blue for normal speed
            text_color = self.WHITE
        else:
            button_color = (100, 60, 60)  # Red for fast speed
            text_color = self.YELLOW

        pygame.draw.rect(screen, button_color, button_rect)
        pygame.draw.rect(screen, self.WHITE, button_rect, 2)

        # Button text
        speed_text = self.small_font.render(
            f"Speed: {game_speed}x", True, text_color)
        text_rect = speed_text.get_rect(center=button_rect.center)
        screen.blit(speed_text, text_rect)

        # Add visual indicator for different speeds
        if game_speed == 2:
            # Add arrows for fast speed
            arrow_y = self.speed_button_y - 8
            for i in range(2):
                arrow_x = self.speed_button_x + 20 + i * 20
                pygame.draw.polygon(screen, self.YELLOW, [
                    (arrow_x, arrow_y),
                    (arrow_x + 8, arrow_y + 4),
                    (arrow_x, arrow_y + 8)
                ])

    def is_speed_button_clicked(self, pos: Tuple[int, int]) -> bool:
        """Check if the speed button was clicked"""
        mouse_x, mouse_y = pos
        return (self.speed_button_x <= mouse_x <= self.speed_button_x + self.speed_button_width and
                self.speed_button_y <= mouse_y <= self.speed_button_y + self.speed_button_height)

    def draw_rock_removal_button(self, screen: pygame.Surface, rock_removal_mode: bool,
                                 rock_removal_cost: int, money: int):
        """Draw the rock removal button"""
        button_rect = pygame.Rect(self.rock_button_x, self.rock_button_y,
                                  self.rock_button_width, self.rock_button_height)

        # Button background - different color based on mode and affordability
        can_afford = money >= rock_removal_cost

        if rock_removal_mode:
            button_color = (100, 60, 100)  # Purple for rock removal mode
            text_color = self.YELLOW
        else:
            button_color = (60, 60, 60)  # Gray for normal mode
            text_color = self.WHITE if can_afford else self.GRAY

        pygame.draw.rect(screen, button_color, button_rect)
        pygame.draw.rect(screen, self.WHITE, button_rect, 2)

        # Button text
        main_text = "Remove Rock" if not rock_removal_mode else "Exit Remove"
        cost_text = f"${rock_removal_cost}"

        # Draw main text
        main_text_surface = self.tiny_font.render(main_text, True, text_color)
        main_text_rect = main_text_surface.get_rect(
            center=(button_rect.centerx, button_rect.centery - 8))
        screen.blit(main_text_surface, main_text_rect)

        # Draw cost text (only when not in removal mode)
        if not rock_removal_mode:
            cost_color = self.GREEN if can_afford else self.RED
            cost_text_surface = self.tiny_font.render(
                cost_text, True, cost_color)
            cost_text_rect = cost_text_surface.get_rect(
                center=(button_rect.centerx, button_rect.centery + 8))
            screen.blit(cost_text_surface, cost_text_rect)

        # Add visual indicator for rock removal mode
        if rock_removal_mode:
            # Add pickaxe-like icon
            icon_x = self.rock_button_x + 10
            icon_y = self.rock_button_y + self.rock_button_height // 2
            pygame.draw.line(screen, self.YELLOW,
                             (icon_x, icon_y), (icon_x + 8, icon_y - 8), 2)
            pygame.draw.line(screen, self.YELLOW, (icon_x + 8,
                             icon_y - 8), (icon_x + 12, icon_y - 4), 2)

    def is_rock_removal_button_clicked(self, pos: Tuple[int, int]) -> bool:
        """Check if the rock removal button was clicked"""
        mouse_x, mouse_y = pos
        return (self.rock_button_x <= mouse_x <= self.rock_button_x + self.rock_button_width and
                self.rock_button_y <= mouse_y <= self.rock_button_y + self.rock_button_height)

    def draw_water_placement_button(self, screen: pygame.Surface, water_placement_mode: bool, terrain_currency: int = 0):
        """Draw the water placement button"""
        button_rect = pygame.Rect(self.water_button_x, self.water_button_y,
                                  self.water_button_width, self.water_button_height)

        # Check if player can afford water placement (cost: 1)
        can_afford = terrain_currency >= 1

        # Button background - different color based on mode and affordability
        if water_placement_mode:
            button_color = (30, 100, 200)  # Blue for water placement mode
            text_color = self.YELLOW
        elif not can_afford:
            button_color = (40, 40, 40)  # Dark gray if can't afford
            text_color = (128, 128, 128)  # Gray text
        else:
            button_color = (60, 60, 60)  # Gray for normal mode
            text_color = self.WHITE

        pygame.draw.rect(screen, button_color, button_rect)
        pygame.draw.rect(screen, self.WHITE, button_rect, 2)

        # Button text
        main_text = "Water" if not water_placement_mode else "Exit"
        main_text_surface = self.tiny_font.render(main_text, True, text_color)
        main_text_rect = main_text_surface.get_rect(center=button_rect.center)
        screen.blit(main_text_surface, main_text_rect)

    def is_water_placement_button_clicked(self, pos: Tuple[int, int]) -> bool:
        """Check if the water placement button was clicked"""
        mouse_x, mouse_y = pos
        return (self.water_button_x <= mouse_x <= self.water_button_x + self.water_button_width and
                self.water_button_y <= mouse_y <= self.water_button_y + self.water_button_height)

    def draw_grass_placement_button(self, screen: pygame.Surface, grass_placement_mode: bool, terrain_currency: int = 0):
        """Draw the grass placement button"""
        button_rect = pygame.Rect(self.grass_button_x, self.grass_button_y,
                                  self.grass_button_width, self.grass_button_height)

        # Check if player can afford grass placement (cost: 2)
        can_afford = terrain_currency >= 2

        # Button background - different color based on mode and affordability
        if grass_placement_mode:
            button_color = (50, 150, 50)  # Green for grass placement mode
            text_color = self.YELLOW
        elif not can_afford:
            button_color = (40, 40, 40)  # Dark gray if can't afford
            text_color = (128, 128, 128)  # Gray text
        else:
            button_color = (60, 60, 60)  # Gray for normal mode
            text_color = self.WHITE

        pygame.draw.rect(screen, button_color, button_rect)
        pygame.draw.rect(screen, self.WHITE, button_rect, 2)

        # Button text
        main_text = "Grass" if not grass_placement_mode else "Exit"
        main_text_surface = self.tiny_font.render(main_text, True, text_color)
        main_text_rect = main_text_surface.get_rect(center=button_rect.center)
        screen.blit(main_text_surface, main_text_rect)

    def is_grass_placement_button_clicked(self, pos: Tuple[int, int]) -> bool:
        """Check if the grass placement button was clicked"""
        mouse_x, mouse_y = pos
        return (self.grass_button_x <= mouse_x <= self.grass_button_x + self.grass_button_width and
                self.grass_button_y <= mouse_y <= self.grass_button_y + self.grass_button_height)

    def draw_enemy_info_button(self, screen: pygame.Surface):
        """Draw the enemy info lookup button"""
        # Button background
        button_color = (60, 60, 80)
        hover_color = (80, 80, 100)

        # For now, just draw normal color (hover detection would need mouse position)
        pygame.draw.rect(screen, button_color,
                         (self.enemy_info_button_x, self.enemy_info_button_y,
                          self.enemy_info_button_width, self.enemy_info_button_height))
        pygame.draw.rect(screen, self.WHITE,
                         (self.enemy_info_button_x, self.enemy_info_button_y,
                          self.enemy_info_button_width, self.enemy_info_button_height), 2)

        # Button text
        text = self.small_font.render("Enemy Info", True, self.WHITE)
        text_rect = text.get_rect(center=(self.enemy_info_button_x + self.enemy_info_button_width // 2,
                                          self.enemy_info_button_y + self.enemy_info_button_height // 2))
        screen.blit(text, text_rect)

    def is_enemy_info_button_clicked(self, pos: Tuple[int, int]) -> bool:
        """Check if the enemy info button was clicked"""
        mouse_x, mouse_y = pos
        return (self.enemy_info_button_x <= mouse_x <= self.enemy_info_button_x + self.enemy_info_button_width and
                self.enemy_info_button_y <= mouse_y <= self.enemy_info_button_y + self.enemy_info_button_height)

    def draw_upgrade_all_button(self, screen: pygame.Surface):
        """Draw the upgrade all towers button"""
        # Button background
        # Purple color to distinguish from other buttons
        button_color = (80, 60, 100)
        # Lighter purple for hover (hover detection would need mouse position)
        hover_color = (100, 80, 120)

        # For now, just draw normal color
        pygame.draw.rect(screen, button_color,
                         (self.upgrade_all_button_x, self.upgrade_all_button_y,
                          self.upgrade_all_button_width, self.upgrade_all_button_height))
        pygame.draw.rect(screen, self.WHITE,
                         (self.upgrade_all_button_x, self.upgrade_all_button_y,
                          self.upgrade_all_button_width, self.upgrade_all_button_height), 2)

        # Button text
        text = self.small_font.render("Upgrade All", True, self.WHITE)
        text_rect = text.get_rect(center=(self.upgrade_all_button_x + self.upgrade_all_button_width // 2,
                                          self.upgrade_all_button_y + self.upgrade_all_button_height // 2))
        screen.blit(text, text_rect)

        # Add upgrade icon (small up arrow)
        icon_x = self.upgrade_all_button_x + 10
        icon_y = self.upgrade_all_button_y + self.upgrade_all_button_height // 2
        pygame.draw.polygon(screen, (255, 215, 0), [  # Gold color
            (icon_x, icon_y + 3),
            (icon_x + 3, icon_y - 3),
            (icon_x + 6, icon_y + 3)
        ])

    def is_upgrade_all_button_clicked(self, pos: Tuple[int, int]) -> bool:
        """Check if the upgrade all button was clicked"""
        mouse_x, mouse_y = pos
        return (self.upgrade_all_button_x <= mouse_x <= self.upgrade_all_button_x + self.upgrade_all_button_width and
                self.upgrade_all_button_y <= mouse_y <= self.upgrade_all_button_y + self.upgrade_all_button_height)

    def draw_tower_bar(self, screen: pygame.Surface, tower_data: List[dict], money: int,
                       scroll_offset: int, max_scroll: int, selected_index: Optional[int],
                       hovered_index: Optional[int]):
        """Draw the scrollable tower bar at the bottom"""
        # Background
        tower_bar_rect = pygame.Rect(
            0, self.bottom_bar_y, self.screen_width, self.bottom_bar_height)
        pygame.draw.rect(screen, self.UI_BG, tower_bar_rect)
        pygame.draw.rect(screen, self.UI_BORDER, tower_bar_rect, 2)

        # Title
        title_text = self.medium_font.render("Towers", True, self.WHITE)
        screen.blit(title_text, (20, self.bottom_bar_y + 5))

        # Tower slots
        start_x = 20
        start_y = self.bottom_bar_y + 30

        for i, tower in enumerate(tower_data):
            slot_x = start_x + i * \
                (self.tower_slot_width + self.tower_slot_margin) - scroll_offset

            # Skip if completely off screen
            if slot_x + self.tower_slot_width < 0 or slot_x > self.screen_width:
                continue

            self._draw_tower_slot(screen, tower, slot_x, start_y, money,
                                  i == selected_index, i == hovered_index)

        # Scroll indicators
        if scroll_offset > 0:
            # Left scroll indicator
            pygame.draw.polygon(screen, self.WHITE, [
                (5, self.bottom_bar_y + 60),
                (15, self.bottom_bar_y + 50),
                (15, self.bottom_bar_y + 70)
            ])

        if scroll_offset < max_scroll:
            # Right scroll indicator
            pygame.draw.polygon(screen, self.WHITE, [
                (self.screen_width - 5, self.bottom_bar_y + 60),
                (self.screen_width - 15, self.bottom_bar_y + 50),
                (self.screen_width - 15, self.bottom_bar_y + 70)
            ])

    def _draw_tower_slot(self, screen: pygame.Surface, tower_data: dict, x: int, y: int,
                         money: int, is_selected: bool, is_hovered: bool):
        """Draw a single tower slot"""
        slot_rect = pygame.Rect(
            x, y, self.tower_slot_width, self.tower_slot_height)

        # Determine slot state
        can_afford = money >= tower_data['cost']

        # Slot background
        if is_selected:
            bg_color = self.UI_SELECTED
        elif is_hovered:
            bg_color = self.UI_HOVER
        else:
            bg_color = self.DARK_GRAY

        pygame.draw.rect(screen, bg_color, slot_rect)

        # Border color based on affordability
        border_color = self.WHITE if can_afford else self.RED
        pygame.draw.rect(screen, border_color, slot_rect, 2)

        # Get tower icon
        tower_type = tower_data.get('type', 'basic')
        icon = self.tower_icon_manager.get_icon_for_ui(tower_type, can_afford)

        if icon:
            # Draw tower icon image
            icon_x = x + (self.tower_slot_width - icon.get_width()) // 2
            icon_y = y + 15  # Position icon in upper part of slot
            screen.blit(icon, (icon_x, icon_y))
        else:
            # Fallback to simple circle if no icon available
            icon_center = (x + self.tower_slot_width // 2, y + 25)
            tower_color = tower_data['color'] if can_afford else self.GRAY

            # Draw tower icon
            pygame.draw.circle(screen, tower_color, icon_center, 12)
            pygame.draw.circle(screen, border_color, icon_center, 12, 2)

            # Draw tower "barrel" for visual appeal
            barrel_end = (icon_center[0] + 8, icon_center[1] - 8)
            pygame.draw.line(screen, border_color, icon_center, barrel_end, 3)

        # Cost text
        cost_color = self.WHITE if can_afford else self.RED
        cost_text = self.tiny_font.render(
            f"${tower_data['cost']}", True, cost_color)
        cost_rect = cost_text.get_rect(
            centerx=x + self.tower_slot_width // 2, y=y + 50)
        screen.blit(cost_text, cost_rect)

    def draw_tower_tooltip(self, screen: pygame.Surface, tower_data: dict, mouse_pos: Tuple[int, int]):
        """Draw tooltip for hovered tower"""
        # Tooltip background
        tooltip_width = 200
        tooltip_height = 80
        tooltip_x = min(mouse_pos[0] + 10, self.screen_width - tooltip_width)
        tooltip_y = max(10, mouse_pos[1] - tooltip_height - 10)

        tooltip_rect = pygame.Rect(
            tooltip_x, tooltip_y, tooltip_width, tooltip_height)
        pygame.draw.rect(screen, self.UI_BG, tooltip_rect)
        pygame.draw.rect(screen, self.UI_BORDER, tooltip_rect, 2)

        # Tower name
        name_text = self.small_font.render(
            tower_data['name'], True, self.WHITE)
        screen.blit(name_text, (tooltip_x + 5, tooltip_y + 5))

        # Description
        desc_text = self.tiny_font.render(
            tower_data['description'], True, self.LIGHT_GRAY)
        screen.blit(desc_text, (tooltip_x + 5, tooltip_y + 25))

        # Stats
        y_offset = 45
        for stat, value in tower_data['stats'].items():
            stat_text = self.tiny_font.render(
                f"{stat}: {value}", True, self.WHITE)
            screen.blit(stat_text, (tooltip_x + 5, tooltip_y + y_offset))
            y_offset += 15

    def draw_pause_overlay(self, screen: pygame.Surface):
        """Draw pause overlay"""
        # Semi-transparent overlay
        UIUtils.draw_overlay(screen, alpha=128)

        # Pause text
        UIUtils.draw_centered_text(screen, "PAUSED", self.large_font,
                                   self.screen_width//2, self.screen_height//2)

        # Instructions
        UIUtils.draw_centered_text(screen, "Press SPACE to continue", self.medium_font,
                                   self.screen_width//2, self.screen_height//2 + 50)

    def draw_victory_screen(self, screen: pygame.Surface, wave_info: dict):
        """Draw victory screen when player beats the final wave"""
        # Semi-transparent dark overlay
        UIUtils.draw_overlay(screen, alpha=180)

        # Victory panel background
        panel_width = 500
        panel_height = 300
        panel_x = (self.screen_width - panel_width) // 2
        panel_y = (self.screen_height - panel_height) // 2

        # Draw panel background with border
        UIUtils.draw_panel(screen, panel_x, panel_y, panel_width, panel_height,
                           bg_color=(20, 20, 20), border_color=(255, 215, 0), border_width=4)

        # Victory title
        victory_text = self.large_font.render(
            "VICTORY!", True, (255, 215, 0))  # Gold text
        victory_rect = victory_text.get_rect(
            center=(self.screen_width//2, panel_y + 60))
        screen.blit(victory_text, victory_rect)

        # Congratulations message
        final_wave = wave_info.get('wave_number', 80)
        congrats_text = self.medium_font.render(
            f"Congratulations! You survived {final_wave} waves!", True, self.WHITE)
        congrats_rect = congrats_text.get_rect(
            center=(self.screen_width//2, panel_y + 120))
        screen.blit(congrats_text, congrats_rect)

        # Instructions
        restart_text = self.medium_font.render(
            "Press R to restart or click buttons below", True, self.WHITE)
        restart_rect = restart_text.get_rect(
            center=(self.screen_width//2, panel_y + 160))
        screen.blit(restart_text, restart_rect)

        # Button setup
        button_width = 180
        button_height = 50
        button_spacing = 20
        button_y = panel_y + 200

        # Restart button (left side)
        restart_button_x = (self.screen_width -
                            (2 * button_width + button_spacing)) // 2
        pygame.draw.rect(screen, (50, 150, 50), (restart_button_x,
                         button_y, button_width, button_height))
        pygame.draw.rect(screen, (100, 200, 100), (restart_button_x,
                         button_y, button_width, button_height), 3)

        restart_button_text = self.medium_font.render(
            "RESTART", True, self.WHITE)
        restart_text_rect = restart_button_text.get_rect(
            center=(restart_button_x + button_width//2, button_y + button_height//2))
        screen.blit(restart_button_text, restart_text_rect)

        # Exit to menu button (right side)
        menu_button_x = restart_button_x + button_width + button_spacing
        pygame.draw.rect(screen, (150, 50, 50), (menu_button_x,
                         button_y, button_width, button_height))
        pygame.draw.rect(screen, (200, 100, 100), (menu_button_x,
                         button_y, button_width, button_height), 3)

        menu_button_text = self.medium_font.render(
            "MAIN MENU", True, self.WHITE)
        menu_text_rect = menu_button_text.get_rect(
            center=(menu_button_x + button_width//2, button_y + button_height//2))
        screen.blit(menu_button_text, menu_text_rect)

    def draw_game_over_screen(self, screen: pygame.Surface):
        """Draw game over screen when player loses all lives"""
        # Semi-transparent dark overlay
        overlay = pygame.Surface((self.screen_width, self.screen_height))
        overlay.set_alpha(180)
        overlay.fill((0, 0, 0))
        screen.blit(overlay, (0, 0))

        # Game Over panel background
        panel_width = 400
        panel_height = 250
        panel_x = (self.screen_width - panel_width) // 2
        panel_y = (self.screen_height - panel_height) // 2

        # Draw panel background with border
        pygame.draw.rect(screen, (20, 20, 20),
                         (panel_x, panel_y, panel_width, panel_height))
        pygame.draw.rect(screen, (200, 50, 50), (panel_x, panel_y,
                         panel_width, panel_height), 4)  # Red border

        # Game Over title
        game_over_text = self.large_font.render(
            "GAME OVER", True, (200, 50, 50))  # Red text
        game_over_rect = game_over_text.get_rect(
            center=(self.screen_width//2, panel_y + 60))
        screen.blit(game_over_text, game_over_rect)

        # Try again message
        try_again_text = self.medium_font.render(
            "Better luck next time!", True, self.WHITE)
        try_again_rect = try_again_text.get_rect(
            center=(self.screen_width//2, panel_y + 110))
        screen.blit(try_again_text, try_again_rect)

        # Instructions
        restart_text = self.medium_font.render(
            "Press R to restart or click buttons below", True, self.WHITE)
        restart_rect = restart_text.get_rect(
            center=(self.screen_width//2, panel_y + 140))
        screen.blit(restart_text, restart_rect)

        # Button setup
        button_width = 180
        button_height = 50
        button_spacing = 20
        button_y = panel_y + 170

        # Restart button (left side)
        restart_button_x = (self.screen_width -
                            (2 * button_width + button_spacing)) // 2
        pygame.draw.rect(screen, (150, 50, 50), (restart_button_x,
                         button_y, button_width, button_height))
        pygame.draw.rect(screen, (200, 100, 100), (restart_button_x,
                         button_y, button_width, button_height), 3)

        restart_button_text = self.medium_font.render(
            "RESTART", True, self.WHITE)
        restart_text_rect = restart_button_text.get_rect(
            center=(restart_button_x + button_width//2, button_y + button_height//2))
        screen.blit(restart_button_text, restart_text_rect)

        # Exit to menu button (right side)
        menu_button_x = restart_button_x + button_width + button_spacing
        pygame.draw.rect(screen, (50, 50, 150), (menu_button_x,
                         button_y, button_width, button_height))
        pygame.draw.rect(screen, (100, 100, 200), (menu_button_x,
                         button_y, button_width, button_height), 3)

        menu_button_text = self.medium_font.render(
            "MAIN MENU", True, self.WHITE)
        menu_text_rect = menu_button_text.get_rect(
            center=(menu_button_x + button_width//2, button_y + button_height//2))
        screen.blit(menu_button_text, menu_text_rect)

    def draw_wave_complete(self, screen: pygame.Surface, wave_number: int, wave_bonus: int):
        """Draw wave completion notification"""
        # Background panel
        panel_width = 300
        panel_height = 120
        panel_x = (self.screen_width - panel_width) // 2
        panel_y = 100  # Top of screen

        # Draw panel with border
        pygame.draw.rect(screen, (20, 50, 20),
                         (panel_x, panel_y, panel_width, panel_height))
        pygame.draw.rect(screen, (100, 200, 100),
                         (panel_x, panel_y, panel_width, panel_height), 3)

        # Wave complete text
        wave_text = self.medium_font.render(
            f"Wave {wave_number} Complete!", True, (100, 255, 100))
        wave_rect = wave_text.get_rect(
            center=(panel_x + panel_width//2, panel_y + 40))
        screen.blit(wave_text, wave_rect)

        # Bonus money text
        bonus_text = self.small_font.render(
            f"Bonus: +${wave_bonus}", True, self.WHITE)
        bonus_rect = bonus_text.get_rect(
            center=(panel_x + panel_width//2, panel_y + 80))
        screen.blit(bonus_text, bonus_rect)

    def draw_level_up_notification(self, screen: pygame.Surface, new_level: int, terrain_currency_gained: int):
        """Draw level up notification"""
        # Background panel
        panel_width = 350
        panel_height = 140
        panel_x = (self.screen_width - panel_width) // 2
        panel_y = 150  # Below wave complete notification

        # Draw panel with glowing border effect
        pygame.draw.rect(screen, (20, 20, 50),
                         (panel_x, panel_y, panel_width, panel_height))
        pygame.draw.rect(screen, (100, 150, 255),
                         (panel_x, panel_y, panel_width, panel_height), 4)

        # Level up text
        level_text = self.medium_font.render(
            f"LEVEL UP!", True, (255, 255, 100))
        level_rect = level_text.get_rect(
            center=(panel_x + panel_width//2, panel_y + 30))
        screen.blit(level_text, level_rect)

        # New level text
        new_level_text = self.small_font.render(
            f"You are now Level {new_level}!", True, self.WHITE)
        new_level_rect = new_level_text.get_rect(
            center=(panel_x + panel_width//2, panel_y + 65))
        screen.blit(new_level_text, new_level_rect)

        # Terrain currency reward text
        reward_text = self.small_font.render(
            f"Earned: +{terrain_currency_gained} Terrain Currency", True, (100, 255, 100))
        reward_rect = reward_text.get_rect(
            center=(panel_x + panel_width//2, panel_y + 95))
        screen.blit(reward_text, reward_rect)

    def draw_wave_start_indicator(self, screen: pygame.Surface, next_wave_number: int):
        """Draw indicator when player can start the next wave"""
        # Background panel - positioned at the top so it doesn't block the map
        panel_width = 400
        panel_height = 100
        panel_x = (self.screen_width - panel_width) // 2
        panel_y = 140  # Below the stats area but above the map

        # Animated background (pulsing effect)
        import time
        # Pulse between 0-40
        pulse = int(20 * (0.5 + 0.5 * math.sin(time.time() * 3)))
        bg_color = (20 + pulse, 60 + pulse, 120 + pulse)

        # Draw panel with animated border
        pygame.draw.rect(screen, bg_color,
                         (panel_x, panel_y, panel_width, panel_height))
        border_color = (100 + pulse, 150 + pulse, 255)
        pygame.draw.rect(screen, border_color,
                         (panel_x, panel_y, panel_width, panel_height), 4)

        # Title text
        title_text = self.medium_font.render(
            f"Ready for Wave {next_wave_number}!", True, self.WHITE)
        title_rect = title_text.get_rect(
            center=(panel_x + panel_width//2, panel_y + 25))
        screen.blit(title_text, title_rect)

        # Instruction text (prominent)
        instruction_text = self.small_font.render(
            "Press SPACE to start", True, (255, 255, 100))
        instruction_rect = instruction_text.get_rect(
            center=(panel_x + panel_width//2, panel_y + 50))
        screen.blit(instruction_text, instruction_rect)

        # Additional instruction text
        sub_text = self.tiny_font.render(
            "Prepare your defenses first!", True, self.LIGHT_GRAY)
        sub_rect = sub_text.get_rect(
            center=(panel_x + panel_width//2, panel_y + 75))
        screen.blit(sub_text, sub_rect)
