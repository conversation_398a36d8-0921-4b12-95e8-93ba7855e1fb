"""
Launcher Renderer for Tower Defense Game Launcher
Handles all drawing and rendering operations
"""

import pygame
from typing import Dict, List, Any, Optional
from .card_renderer import Card<PERSON>enderer
from .performance_renderer import PerformanceRenderer
from .preview_renderer import PreviewRenderer


class LauncherRenderer:
    """Handles all drawing and rendering for the launcher"""

    def __init__(self, screen_width: int, screen_height: int):
        """Initialize the launcher renderer"""
        self.screen_width = screen_width
        self.screen_height = screen_height

        # Define color scheme (modern, clean)
        self.BACKGROUND = (32, 34, 40)      # Dark modern background
        self.CARD_BG = (48, 52, 64)         # Card background
        self.HOVER_BG = (56, 60, 74)        # Hover background
        self.SELECTED_BG = (64, 68, 84)     # Selected background
        self.TEXT_PRIMARY = (240, 242, 247)  # Primary text
        self.TEXT_SECONDARY = (155, 164, 181)  # Secondary text
        self.ACCENT_BLUE = (59, 130, 246)   # Primary action color
        self.ACCENT_GREEN = (34, 197, 94)   # Success color
        self.ACCENT_ORANGE = (251, 146, 60)  # Warning color
        self.BORDER_LIGHT = (75, 85, 99)    # Light border
        self.BORDER_DARK = (31, 41, 55)     # Dark border

        # Modern, elegant fonts
        self.title_font = pygame.font.Font(None, 42)
        self.subtitle_font = pygame.font.Font(None, 28)
        self.menu_font = pygame.font.Font(None, 24)
        self.info_font = pygame.font.Font(None, 20)
        self.small_font = pygame.font.Font(None, 16)

        # Create color and font dictionaries for sub-renderers
        self.colors = {
            'BACKGROUND': self.BACKGROUND,
            'CARD_BG': self.CARD_BG,
            'HOVER_BG': self.HOVER_BG,
            'SELECTED_BG': self.SELECTED_BG,
            'TEXT_PRIMARY': self.TEXT_PRIMARY,
            'TEXT_SECONDARY': self.TEXT_SECONDARY,
            'ACCENT_BLUE': self.ACCENT_BLUE,
            'ACCENT_GREEN': self.ACCENT_GREEN,
            'ACCENT_ORANGE': self.ACCENT_ORANGE,
            'BORDER_LIGHT': self.BORDER_LIGHT,
            'BORDER_DARK': self.BORDER_DARK
        }

        self.fonts = {
            'title': self.title_font,
            'subtitle': self.subtitle_font,
            'menu': self.menu_font,
            'info': self.info_font,
            'small': self.small_font
        }

        # Initialize sub-renderers
        self.card_renderer = CardRenderer(self.colors, self.fonts)
        self.performance_renderer = PerformanceRenderer(
            self.colors, self.fonts, screen_width, screen_height)
        self.preview_renderer = PreviewRenderer(
            self.colors, self.fonts, screen_width, screen_height)

    def draw(self, screen, navigation_manager, config_manager, event_handler,
             performance_stats=None, variant_ui=None, global_upgrade_ui=None):
        """Draw the elegant launcher interface"""
        # Soft background
        screen.fill(self.BACKGROUND)

        # Show level preview if active
        if navigation_manager.show_level_preview and navigation_manager.preview_config:
            self.preview_renderer.draw_level_preview(
                screen, navigation_manager)
            return

        # Header section
        header_height = 100
        pygame.draw.rect(screen, self.CARD_BG,
                         (0, 0, self.screen_width, header_height))
        pygame.draw.line(screen, self.BORDER_LIGHT, (0,
                         header_height), (self.screen_width, header_height), 1)

        # Title and subtitle based on current view
        self._draw_header(screen, navigation_manager,
                          config_manager, header_height)

        # Draw currency displays in top right
        self._draw_currency_displays(screen, global_upgrade_ui)

        # Draw back button in variants view or level options view
        if navigation_manager.current_view == "variants" or navigation_manager.current_view == "level_options":
            self.draw_back_button(screen)

        # Action buttons (draw in ALL views)
        self.draw_action_buttons(screen, event_handler)

        # Show level options view if active
        if navigation_manager.current_view == "level_options":
            self.draw_level_options(screen, navigation_manager, config_manager)
        else:
            # Main content area (only for main and variants views)
            content_y = header_height + 20
            content_height = self.screen_height - \
                header_height - 120  # Leave space for buttons

            # In variants view, leave space for back button
            if navigation_manager.current_view == "variants":
                content_y += 50
                content_height -= 50

            current_configs = config_manager.get_current_config_list(
                navigation_manager)
            if current_configs:
                self.card_renderer.draw_level_cards(
                    screen, navigation_manager, config_manager, event_handler, content_y, content_height)

        # Performance panel (if visible)
        if navigation_manager.show_performance_panel:
            if performance_stats:
                self.performance_renderer.draw_performance_panel(
                    screen, performance_stats)
            else:
                # Show a message when no performance data is available
                self.performance_renderer.draw_no_performance_data_message(
                    screen)

        # Upgrade menu (if visible)
        if navigation_manager.show_upgrade_menu and global_upgrade_ui:
            try:
                global_upgrade_ui.draw(screen)
            except Exception as e:
                print(f"Error drawing upgrade menu: {e}")

        # If upgrade menu was closed from within the UI, reset the flag
        if navigation_manager.show_upgrade_menu and global_upgrade_ui and not global_upgrade_ui.is_open:
            navigation_manager.show_upgrade_menu = False

        # Variant selector (if visible)
        if navigation_manager.show_variant_selector and variant_ui:
            variant_ui.draw(screen)

        # Status message
        if navigation_manager.show_generation_status and navigation_manager.generation_message:
            self.draw_status_message(
                screen, navigation_manager.generation_message)

        pygame.display.flip()

    def _draw_header(self, screen, navigation_manager, config_manager, header_height):
        """Draw the header section with title and subtitle"""
        if navigation_manager.current_view == "main":
            title_text = self.title_font.render(
                "Tower Defense", True, self.TEXT_PRIMARY)
            current_configs = config_manager.get_current_config_list(
                navigation_manager)
            if current_configs:
                subtitle_text = self.info_font.render(
                    "Select a level to configure", True, self.TEXT_SECONDARY)
            else:
                subtitle_text = self.info_font.render(
                    "No levels found", True, self.ACCENT_ORANGE)
        elif navigation_manager.current_view == "level_options":
            # Show level options view
            level_name = navigation_manager.level_options_config.get(
                'name', 'Unknown Level') if navigation_manager.level_options_config else 'Unknown'
            title_text = self.title_font.render(
                level_name, True, self.TEXT_PRIMARY)
            subtitle_text = self.info_font.render(
                "Choose how to play this level", True, self.TEXT_SECONDARY)
        else:  # variants view
            # Show base level name with indicator this is variants view
            base_level_display = (
                navigation_manager.selected_base_level or "Unknown").replace('_', ' ').title()
            title_text = self.title_font.render(
                f"{base_level_display} - Variants", True, self.TEXT_PRIMARY)
            variant_count = len(config_manager.variants.get(
                navigation_manager.selected_base_level or "", []))
            subtitle_text = self.info_font.render(
                f"{variant_count} variants available", True, self.TEXT_SECONDARY)

        title_rect = title_text.get_rect(center=(self.screen_width // 2, 35))
        screen.blit(title_text, title_rect)
        subtitle_rect = subtitle_text.get_rect(
            center=(self.screen_width // 2, 65))
        screen.blit(subtitle_text, subtitle_rect)

    def draw_back_button(self, screen):
        """Draw back button in variants view"""
        back_btn_x, back_btn_y = 50, 50
        back_btn_width, back_btn_height = 100, 35

        # Button background
        pygame.draw.rect(screen, self.BORDER_DARK, (back_btn_x, back_btn_y,
                         back_btn_width, back_btn_height), border_radius=8)
        pygame.draw.rect(screen, self.CARD_BG, (back_btn_x + 1, back_btn_y + 1,
                         back_btn_width - 2, back_btn_height - 2), border_radius=7)

        # Button text
        back_text = self.info_font.render("← Back", True, self.TEXT_PRIMARY)
        back_rect = back_text.get_rect(
            center=(back_btn_x + back_btn_width // 2, back_btn_y + back_btn_height // 2))
        screen.blit(back_text, back_rect)

    def draw_action_buttons(self, screen, event_handler):
        """Draw modern action buttons at the bottom"""
        import time

        # Clear pressed states after brief feedback duration
        event_handler.clear_pressed_states()

        button_area_height = 80
        button_y = self.screen_height - button_area_height

        # Background for button area
        pygame.draw.rect(screen, self.CARD_BG, (0, button_y,
                         self.screen_width, button_area_height))
        pygame.draw.line(screen, self.BORDER_LIGHT,
                         (0, button_y), (self.screen_width, button_y), 1)

        # Button specifications
        button_height = 35
        button_y_pos = button_y + 22
        button_spacing = 20

        # Calculate button positions (centered) - 3 buttons now
        btn1_width = 200  # Generate New Level
        btn2_width = 120  # Stats
        btn3_width = 120  # Upgrades
        total_width = btn1_width + btn2_width + btn3_width + button_spacing * 2
        start_x = (self.screen_width - total_width) // 2

        # Generate button (primary)
        btn1_x = start_x
        is_pressed = event_handler.pressed_button == 'generate'
        btn_color = (int(self.ACCENT_BLUE[0] * 0.8), int(self.ACCENT_BLUE[1] * 0.8), int(
            self.ACCENT_BLUE[2] * 0.8)) if is_pressed else self.ACCENT_BLUE

        pygame.draw.rect(screen, btn_color, (btn1_x, button_y_pos,
                         btn1_width, button_height), border_radius=8)

        btn1_text = self.info_font.render(
            "Generate New Level", True, self.CARD_BG)
        offset = 2 if is_pressed else 0
        btn1_rect = btn1_text.get_rect(center=(
            btn1_x + btn1_width//2 + offset, button_y_pos + button_height//2 + offset))
        screen.blit(btn1_text, btn1_rect)

        # Performance/Stats button
        btn2_x = btn1_x + btn1_width + button_spacing
        is_pressed = event_handler.pressed_button == 'stats'
        perf_color = (int(self.ACCENT_GREEN[0] * 0.8), int(self.ACCENT_GREEN[1] * 0.8), int(
            self.ACCENT_GREEN[2] * 0.8)) if is_pressed else self.ACCENT_GREEN

        pygame.draw.rect(screen, perf_color, (btn2_x, button_y_pos,
                         btn2_width, button_height), border_radius=8)

        # Show different style if panel is not open
        # Note: We'll need to pass show_performance_panel state to determine this

        offset = 2 if is_pressed else 0
        btn2_text = self.info_font.render("Stats", True, self.CARD_BG)
        btn2_rect = btn2_text.get_rect(center=(
            btn2_x + btn2_width//2 + offset, button_y_pos + button_height//2 + offset))
        screen.blit(btn2_text, btn2_rect)

        # Upgrade button
        btn3_x = btn2_x + btn2_width + button_spacing
        is_pressed = event_handler.pressed_button == 'upgrades'
        upgrade_color = (int(self.ACCENT_ORANGE[0] * 0.8), int(self.ACCENT_ORANGE[1] * 0.8), int(
            self.ACCENT_ORANGE[2] * 0.8)) if is_pressed else self.ACCENT_ORANGE

        pygame.draw.rect(screen, upgrade_color, (btn3_x, button_y_pos,
                         btn3_width, button_height), border_radius=8)

        # Show different style if menu is not open
        # Note: We'll need to pass show_upgrade_menu state to determine this

        offset = 2 if is_pressed else 0
        btn3_text = self.info_font.render("Upgrades", True, self.CARD_BG)
        btn3_rect = btn3_text.get_rect(center=(
            btn3_x + btn3_width//2 + offset, button_y_pos + button_height//2 + offset))
        screen.blit(btn3_text, btn3_rect)

        # Store button coordinates for click detection
        event_handler.set_button_coords({
            'generate': (btn1_x, button_y_pos, btn1_width, button_height),
            'stats': (btn2_x, button_y_pos, btn2_width, button_height),
            'upgrades': (btn3_x, button_y_pos, btn3_width, button_height)
        })

    def draw_status_message(self, screen, message):
        """Draw status message with modern styling"""
        if not message:
            return

        # Create a large, centered overlay for better visibility
        overlay_height = 80
        overlay_y = self.screen_height // 2 - overlay_height // 2

        # Semi-transparent background
        overlay = pygame.Surface((self.screen_width, overlay_height))
        overlay.set_alpha(220)
        overlay.fill(
            self.ACCENT_GREEN if "✅" in message else self.ACCENT_ORANGE if "❌" in message else self.ACCENT_BLUE)
        screen.blit(overlay, (0, overlay_y))

        # Message text (larger and centered)
        status_text = self.subtitle_font.render(message, True, self.CARD_BG)
        status_rect = status_text.get_rect(
            center=(self.screen_width // 2, overlay_y + overlay_height // 2))
        screen.blit(status_text, status_rect)

        # Add instruction text
        instruction_text = self.info_font.render(
            "Click anywhere to dismiss", True, self.CARD_BG)
        instruction_rect = instruction_text.get_rect(
            center=(self.screen_width // 2, overlay_y + overlay_height // 2 + 25))
        screen.blit(instruction_text, instruction_rect)

    def draw_modern_difficulty_bar(self, screen, x: int, y: int, difficulty: int, width: int = 100, height: int = 6):
        """Draw a modern, sleek difficulty bar"""
        if not isinstance(difficulty, (int, float)):
            difficulty = 50
        difficulty = max(0, min(100, int(difficulty)))

        # Background bar
        pygame.draw.rect(screen, self.BORDER_LIGHT,
                         (x, y, width, height), border_radius=height//2)

        # Progress fill
        fill_width = int((difficulty / 100) * width)
        if fill_width > 0:
            if difficulty <= 30:
                color = self.ACCENT_GREEN
            elif difficulty <= 70:
                color = self.ACCENT_ORANGE
            else:
                color = (220, 53, 69)  # Red for high difficulty

            pygame.draw.rect(screen, color, (x, y, fill_width,
                             height), border_radius=height//2)

    def wrap_text(self, text: str, font, max_width: int) -> list:
        """Wrap text to fit within a given width"""
        words = text.split(' ')
        lines = []
        current_line = []

        for word in words:
            test_line = ' '.join(current_line + [word])
            if font.size(test_line)[0] <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    lines.append(word)

        if current_line:
            lines.append(' '.join(current_line))

        return lines

    def draw_level_options(self, screen, navigation_manager, config_manager):
        """Draw the level options view (play original vs view variants)"""
        if not navigation_manager.level_options_config:
            return

        # Level info card
        card_width = 600
        card_height = 200
        card_x = (self.screen_width - card_width) // 2
        card_y = 180

        # Card background
        pygame.draw.rect(screen, self.CARD_BG, (card_x, card_y,
                         card_width, card_height), border_radius=12)
        pygame.draw.rect(screen, self.BORDER_LIGHT, (card_x,
                         card_y, card_width, card_height), 2, border_radius=12)

        # Level details
        config = navigation_manager.level_options_config
        margin = 30

        # Level name (already in title, but show description)
        desc_y = card_y + margin
        description = config.get(
            'description', 'A challenging tower defense level.')
        desc_lines = self.wrap_text(
            description, self.menu_font, card_width - 2 * margin)
        for line in desc_lines:
            desc_text = self.menu_font.render(line, True, self.TEXT_SECONDARY)
            screen.blit(desc_text, (card_x + margin, desc_y))
            desc_y += 25

        # Difficulty bar
        difficulty = config.get('difficulty', 50)
        diff_text = self.info_font.render(
            f"Difficulty: {difficulty}/100", True, self.TEXT_SECONDARY)
        screen.blit(diff_text, (card_x + margin, desc_y + 10))
        self.draw_modern_difficulty_bar(
            screen, card_x + margin, desc_y + 35, difficulty, card_width - 2 * margin, 8)

        # Action buttons
        button_width = 200
        button_height = 50
        button_spacing = 30
        center_x = self.screen_width // 2
        buttons_y = card_y + card_height + 50

        # "Play Original" button
        play_btn_x = center_x - button_width // 2
        play_btn_y = buttons_y
        pygame.draw.rect(screen, self.ACCENT_GREEN, (play_btn_x,
                         play_btn_y, button_width, button_height), border_radius=25)

        play_text = self.subtitle_font.render(
            "Play Original", True, self.CARD_BG)
        play_rect = play_text.get_rect(
            center=(play_btn_x + button_width // 2, play_btn_y + button_height // 2))
        screen.blit(play_text, play_rect)

        # "View Variants" button (only if variants exist)
        base_level = config_manager.get_base_level_for_config(config)
        has_variants = base_level in config_manager.variants and len(
            config_manager.variants[base_level]) > 0

        current_button_y = buttons_y + button_height + button_spacing

        if has_variants:
            variants_btn_x = center_x - button_width // 2
            variants_btn_y = current_button_y
            variant_count = len(config_manager.variants[base_level])

            pygame.draw.rect(screen, self.ACCENT_BLUE, (variants_btn_x,
                             variants_btn_y, button_width, button_height), border_radius=25)

            variants_text = self.subtitle_font.render(
                f"View Variants ({variant_count})", True, self.CARD_BG)
            variants_rect = variants_text.get_rect(center=(
                variants_btn_x + button_width // 2, variants_btn_y + button_height // 2))
            screen.blit(variants_text, variants_rect)

            current_button_y += button_height + button_spacing

        # "Create Variant" button
        create_variant_btn_x = center_x - button_width // 2
        create_variant_btn_y = current_button_y

        pygame.draw.rect(screen, (138, 43, 226), (create_variant_btn_x,
                         create_variant_btn_y, button_width, button_height), border_radius=25)

        create_variant_text = self.subtitle_font.render(
            "Create Variant", True, self.CARD_BG)
        create_variant_rect = create_variant_text.get_rect(center=(
            create_variant_btn_x + button_width // 2, create_variant_btn_y + button_height // 2))
        screen.blit(create_variant_text, create_variant_rect)

        # Instructions
        instructions_y = current_button_y + button_height + 20

        instructions = "Click a button above to proceed, or use the back button to return to the main menu"
        instr_text = self.small_font.render(
            instructions, True, self.TEXT_SECONDARY)
        instr_rect = instr_text.get_rect(
            center=(self.screen_width // 2, instructions_y))
        screen.blit(instr_text, instr_rect)

    def _draw_currency_displays(self, screen, global_upgrade_ui):
        """Draw currency displays in the top right corner"""
        if not global_upgrade_ui or not hasattr(global_upgrade_ui, 'upgrade_system'):
            return

        upgrade_system = global_upgrade_ui.upgrade_system

        # Position currencies in top right corner
        currency_x = self.screen_width - 280  # Right side with some margin
        currency_y = 15  # Top margin
        panel_width = 260
        panel_height = 85

        # Draw background panel for currencies
        currency_panel = pygame.Rect(
            currency_x, currency_y, panel_width, panel_height)
        pygame.draw.rect(screen, self.CARD_BG, currency_panel)
        pygame.draw.rect(screen, self.BORDER_LIGHT, currency_panel, 1)

        # Draw upgrade points
        points_text = self.subtitle_font.render(
            f"Points: {upgrade_system.points}", True, self.ACCENT_BLUE)
        screen.blit(points_text, (currency_x + 10, currency_y + 8))

        # Draw terrain currency below points
        terrain_currency_text = self.subtitle_font.render(
            f"Terrain: {upgrade_system.terrain_currency}", True, self.ACCENT_ORANGE)
        screen.blit(terrain_currency_text, (currency_x + 10, currency_y + 32))

        # Draw player level below terrain currency
        level_text = self.subtitle_font.render(
            f"Level: {upgrade_system.player_level}", True, self.ACCENT_GREEN)
        screen.blit(level_text, (currency_x + 10, currency_y + 56))
