from typing import List, <PERSON><PERSON>
from .enemy import Enemy

class BasicEnemy(Enemy):
    """Basic enemy with standard stats"""
    def __init__(self, path: List[Tuple[int, int]], wave_number: int = 1):
        super().__init__(path, wave_number)
        self.max_health = 2
        self.health = self.max_health
        self.speed = 1.0
        self.reward = 5
        self.size = 8
        self.color = (255, 100, 100)  # Light red 