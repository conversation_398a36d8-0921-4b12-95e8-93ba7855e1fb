# Update game.py for manual wave start functionality

with open('game.py', 'r') as f:
    content = f.read()

# 1. Update SPACE key handling to support manual wave start
old_space_handler = '''        if key == pygame.K_SPACE:
            self.paused = not self.paused'''

new_space_handler = '''        if key == pygame.K_SPACE:
            # Check if we can start next wave first
            if hasattr(self.wave_manager, 'can_start_next_wave') and self.wave_manager.can_start_next_wave():
                wave_info = self.wave_manager.start_next_wave_manual()
                if wave_info and wave_info.get('wave_started'):
                    # Update tower costs for the new wave
                    current_wave = wave_info.get('wave_number', 1)
                    self.tower_manager.set_current_wave(current_wave)
            else:
                # Normal pause/unpause if no wave to start
                self.paused = not self.paused'''

content = content.replace(old_space_handler, new_space_handler)

# 2. Update get_game_state to include wave waiting status
old_game_state = '''        return {
            'money': self.money,
            'lives': self.lives,
            'wave_info': wave_info,
            'paused': self.paused,
            'game_over': self.game_over,
            'victory': self.victory,
            'show_victory_screen': self.show_victory_screen,
            'show_game_over_screen': self.show_game_over_screen,
            'selected_tower': placement_state['selected_tower_type'],
            'show_wave_complete': self.show_wave_complete,
            'completed_wave_number': self.completed_wave_number,
            'wave_bonus': self.wave_bonus,
            'towers': self.towers,
            'game_speed': self.game_speed,
            'performance': self.get_performance_info()
        }'''

new_game_state = '''        return {
            'money': self.money,
            'lives': self.lives,
            'wave_info': wave_info,
            'paused': self.paused,
            'game_over': self.game_over,
            'victory': self.victory,
            'show_victory_screen': self.show_victory_screen,
            'show_game_over_screen': self.show_game_over_screen,
            'selected_tower': placement_state['selected_tower_type'],
            'show_wave_complete': self.show_wave_complete,
            'completed_wave_number': self.completed_wave_number,
            'wave_bonus': self.wave_bonus,
            'towers': self.towers,
            'game_speed': self.game_speed,
            'performance': self.get_performance_info(),
            'waiting_for_next_wave': getattr(self.wave_manager, 'waiting_for_next_wave', False),
            'can_start_next_wave': hasattr(self.wave_manager, 'can_start_next_wave') and self.wave_manager.can_start_next_wave()
        }'''

content = content.replace(old_game_state, new_game_state)

# 3. Update update_waves to remove auto-start and use manual start
old_wave_update = '''                # Start next wave after a delay
                if not wave_info.get('is_final_wave', False):
                    next_wave_info = self.wave_manager.start_next_wave()
                    if next_wave_info:
                        # Update tower costs for the new wave
                        current_wave = next_wave_info.get('wave_number', 1)
                        self.tower_manager.set_current_wave(current_wave)'''

new_wave_update = '''                # Manual wave start - no auto-start
                # Wave will wait for player to press SPACE
                pass'''

content = content.replace(old_wave_update, new_wave_update)

with open('game.py', 'w') as f:
    f.write(content)

