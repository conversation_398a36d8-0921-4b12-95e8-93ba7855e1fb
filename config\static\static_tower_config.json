{"description": "Static tower configuration - costs, progression rules, and mechanics that don't change game to game", "version": "1.0", "base_costs": {"basic": 20, "sniper": 40, "cannon": 60, "freezer": 30, "poison": 35, "laser": 80, "lightning": 50, "missile": 100, "flame": 45, "ice": 55, "detector": 25, "antiair": 70, "explosive": 90, "splash": 120, "destroyer": 1500}, "cost_progression": {"description": "How tower costs scale with wave progression", "early_game_waves": 15, "mid_game_waves": 30, "early_increase_per_wave": 0.02, "mid_increase_per_wave": 0.03, "late_increase_per_wave": 0.05, "max_cost_multiplier": 3.0}, "dynamic_cost_increase": {"description": "How tower costs increase based on usage", "per_tower_built_multiplier": 0.15, "max_per_tower_multiplier": 20}, "tower_descriptions": {"basic": "Cheap starter tower with decent range", "sniper": "Long range, high damage single target", "cannon": "POWERFUL siege artillery with massive splash damage", "freezer": "Completely freezes single enemies", "poison": "Poison damage over time with area effect", "laser": "Pierces through multiple enemies in a line", "lightning": "Chains between nearby enemies", "missile": "Homing missiles with area damage, 2x2 size", "flame": "Cone attack with burn damage over time", "ice": "Area freeze effect, no direct damage", "detector": "Reveals invisible enemies in large radius", "antiair": "Specialized anti-aircraft targeting", "explosive": "DEVASTATING rocket launcher with huge explosions", "splash": "Water-only placement, makes enemies wet", "destroyer": "WATER-ONLY: Extreme range naval artillery"}, "size_requirements": {"description": "Tower placement size requirements", "single_cell": ["basic", "sniper", "cannon", "freezer", "poison", "laser", "lightning", "flame", "ice", "detector", "antiair", "explosive", "splash"], "2x2_cell": ["missile", "destroyer"], "water_only": ["splash", "destroyer"], "land_only": ["basic", "sniper", "cannon", "freezer", "poison", "laser", "lightning", "flame", "ice", "detector", "antiair", "explosive", "missile"]}}