"""
Rendering optimization utilities for the tower defense game
Provides batching, caching, and performance improvements for drawing operations
"""

import pygame
from typing import List, Dict, Tuple, Any, Optional
from collections import defaultdict


class SpriteCache:
    """Cache for frequently used sprites and surfaces"""
    
    def __init__(self, max_size: int = 100):
        self.cache: Dict[str, pygame.Surface] = {}
        self.access_count: Dict[str, int] = defaultdict(int)
        self.max_size = max_size
    
    def get(self, key: str) -> Optional[pygame.Surface]:
        """Get a cached surface"""
        if key in self.cache:
            self.access_count[key] += 1
            return self.cache[key]
        return None
    
    def put(self, key: str, surface: pygame.Surface):
        """Cache a surface"""
        if len(self.cache) >= self.max_size:
            # Remove least accessed item
            least_used = min(self.access_count.items(), key=lambda x: x[1])
            del self.cache[least_used[0]]
            del self.access_count[least_used[0]]
        
        self.cache[key] = surface.copy()
        self.access_count[key] = 1
    
    def clear(self):
        """Clear the cache"""
        self.cache.clear()
        self.access_count.clear()


class DrawBatch:
    """Batch similar drawing operations for better performance"""
    
    def __init__(self):
        self.rectangles: List[Tuple[pygame.Surface, pygame.Rect, Tuple[int, int, int]]] = []
        self.circles: List[Tuple[pygame.Surface, Tuple[int, int, int], Tuple[int, int], int]] = []
        self.lines: List[Tuple[pygame.Surface, Tuple[int, int, int], Tuple[int, int], Tuple[int, int], int]] = []
        self.blits: List[Tuple[pygame.Surface, pygame.Surface, Tuple[int, int]]] = []
    
    def add_rectangle(self, surface: pygame.Surface, rect: pygame.Rect, color: Tuple[int, int, int]):
        """Add a rectangle to the batch"""
        self.rectangles.append((surface, rect, color))
    
    def add_circle(self, surface: pygame.Surface, color: Tuple[int, int, int], 
                   center: Tuple[int, int], radius: int):
        """Add a circle to the batch"""
        self.circles.append((surface, color, center, radius))
    
    def add_line(self, surface: pygame.Surface, color: Tuple[int, int, int],
                 start: Tuple[int, int], end: Tuple[int, int], width: int = 1):
        """Add a line to the batch"""
        self.lines.append((surface, color, start, end, width))
    
    def add_blit(self, dest_surface: pygame.Surface, source_surface: pygame.Surface, 
                 pos: Tuple[int, int]):
        """Add a blit operation to the batch"""
        self.blits.append((dest_surface, source_surface, pos))
    
    def execute(self):
        """Execute all batched operations"""
        # Execute rectangles
        for surface, rect, color in self.rectangles:
            pygame.draw.rect(surface, color, rect)
        
        # Execute circles
        for surface, color, center, radius in self.circles:
            pygame.draw.circle(surface, color, center, radius)
        
        # Execute lines
        for surface, color, start, end, width in self.lines:
            pygame.draw.line(surface, color, start, end, width)
        
        # Execute blits
        for dest_surface, source_surface, pos in self.blits:
            dest_surface.blit(source_surface, pos)
    
    def clear(self):
        """Clear all batched operations"""
        self.rectangles.clear()
        self.circles.clear()
        self.lines.clear()
        self.blits.clear()
    
    def is_empty(self) -> bool:
        """Check if the batch is empty"""
        return (not self.rectangles and not self.circles and 
                not self.lines and not self.blits)


class RenderOptimizer:
    """Main rendering optimization system"""
    
    def __init__(self, screen_width: int, screen_height: int):
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # Optimization systems
        self.sprite_cache = SpriteCache()
        self.draw_batch = DrawBatch()
        
        # Performance tracking
        self.frame_count = 0
        self.draw_calls_saved = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Dirty rectangle tracking for partial updates
        self.dirty_rects: List[pygame.Rect] = []
        self.enable_dirty_rect_optimization = False
    
    def start_frame(self):
        """Start a new frame"""
        self.frame_count += 1
        self.draw_batch.clear()
        if self.enable_dirty_rect_optimization:
            self.dirty_rects.clear()
    
    def end_frame(self):
        """End the current frame and execute batched operations"""
        if not self.draw_batch.is_empty():
            self.draw_batch.execute()
            self.draw_calls_saved += 1
    
    def get_cached_surface(self, key: str, create_func=None, *args, **kwargs) -> pygame.Surface:
        """Get a cached surface or create it if not found"""
        surface = self.sprite_cache.get(key)
        if surface is not None:
            self.cache_hits += 1
            return surface
        
        self.cache_misses += 1
        if create_func is not None:
            surface = create_func(*args, **kwargs)
            self.sprite_cache.put(key, surface)
            return surface
        
        raise ValueError(f"Surface not found in cache and no create function provided: {key}")
    
    def batch_rectangle(self, surface: pygame.Surface, rect: pygame.Rect, color: Tuple[int, int, int]):
        """Add a rectangle to the draw batch"""
        self.draw_batch.add_rectangle(surface, rect, color)
        if self.enable_dirty_rect_optimization:
            self.dirty_rects.append(rect)
    
    def batch_circle(self, surface: pygame.Surface, color: Tuple[int, int, int],
                     center: Tuple[int, int], radius: int):
        """Add a circle to the draw batch"""
        self.draw_batch.add_circle(surface, color, center, radius)
        if self.enable_dirty_rect_optimization:
            rect = pygame.Rect(center[0] - radius, center[1] - radius, radius * 2, radius * 2)
            self.dirty_rects.append(rect)
    
    def batch_blit(self, dest_surface: pygame.Surface, source_surface: pygame.Surface,
                   pos: Tuple[int, int]):
        """Add a blit operation to the draw batch"""
        self.draw_batch.add_blit(dest_surface, source_surface, pos)
        if self.enable_dirty_rect_optimization:
            rect = pygame.Rect(pos[0], pos[1], source_surface.get_width(), source_surface.get_height())
            self.dirty_rects.append(rect)
    
    def create_text_surface(self, text: str, font: pygame.font.Font, color: Tuple[int, int, int],
                           cache_key: str = None) -> pygame.Surface:
        """Create a text surface with optional caching"""
        if cache_key is None:
            cache_key = f"text_{hash((text, font, color))}"
        
        def create_text():
            return font.render(text, True, color)
        
        return self.get_cached_surface(cache_key, create_text)
    
    def create_colored_surface(self, width: int, height: int, color: Tuple[int, int, int],
                              cache_key: str = None) -> pygame.Surface:
        """Create a colored surface with optional caching"""
        if cache_key is None:
            cache_key = f"colored_{width}x{height}_{color}"
        
        def create_surface():
            surface = pygame.Surface((width, height))
            surface.fill(color)
            return surface
        
        return self.get_cached_surface(cache_key, create_surface)
    
    def optimize_surface_flags(self, surface: pygame.Surface) -> pygame.Surface:
        """Optimize surface flags for better performance"""
        # Convert surface to display format for faster blitting
        if surface.get_flags() & pygame.SRCALPHA:
            return surface.convert_alpha()
        else:
            return surface.convert()
    
    def get_dirty_rects(self) -> List[pygame.Rect]:
        """Get dirty rectangles for partial screen updates"""
        return self.dirty_rects.copy()
    
    def enable_dirty_rect_tracking(self, enabled: bool = True):
        """Enable or disable dirty rectangle tracking"""
        self.enable_dirty_rect_optimization = enabled
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        total_cache_requests = self.cache_hits + self.cache_misses
        cache_hit_rate = (self.cache_hits / total_cache_requests * 100) if total_cache_requests > 0 else 0
        
        return {
            'frame_count': self.frame_count,
            'draw_calls_saved': self.draw_calls_saved,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'cache_hit_rate': cache_hit_rate,
            'cached_surfaces': len(self.sprite_cache.cache),
            'dirty_rects_enabled': self.enable_dirty_rect_optimization
        }
    
    def clear_cache(self):
        """Clear all caches"""
        self.sprite_cache.clear()
    
    def reset_stats(self):
        """Reset performance statistics"""
        self.frame_count = 0
        self.draw_calls_saved = 0
        self.cache_hits = 0
        self.cache_misses = 0
