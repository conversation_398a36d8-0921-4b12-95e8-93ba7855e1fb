"""
Map Generation Module for Tower Defense

Contains map, terrain, and path generation logic for procedural and AI-guided systems.
"""

import random
import time
from typing import List, Tuple

# Terrain constants
GRASS = 0
PATH = 1
ROCK = 2
WATER = 3
FOREST = 4
SAND = 5


def generate_path_dfs(width: int, height: int, start: Tuple[int, int], 
                     end: Tuple[int, int], complexity: float, 
                     min_length: int = 30) -> List[Tuple[int, int]]:
    """
    Generate a path using DFS with timeout fallback to ensure solvability
    """
    timeout = 2.0  # 2 second timeout for DFS
    
    # Try DFS pathfinding with timeout
    path = dfs_pathfind_with_timeout(width, height, start, end, complexity, timeout)
    
    if path is None or len(path) < min_length:
        print(f"DFS failed or path too short ({len(path) if path else 0} < {min_length}), using fallback...")
        path = generate_fallback_path(start, end, min_length, width, height)
    
    return path


def dfs_pathfind_with_timeout(width: int, height: int, start: Tuple[int, int], 
                             end: Tuple[int, int], complexity: float, timeout: float) -> List[Tuple[int, int]]:
    """
    DFS pathfinding with timeout and complexity-based direction bias
    """
    start_time = time.time()
    
    def is_valid(x: int, y: int) -> bool:
        return 0 <= x < width and 0 <= y < height
    
    def dfs(x: int, y: int, target_x: int, target_y: int) -> bool:
        # Check timeout
        if time.time() - start_time > timeout:
            return False
        
        path.append((x, y))
        visited.add((x, y))
        
        if x == target_x and y == target_y:
            return True
        
        # Create direction list with bias based on complexity
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0)]  # right, down, left, up
        
        # High complexity = more random exploration
        # Low complexity = more direct path
        if random.random() < complexity:
            random.shuffle(directions)
        else:
            # Bias towards target direction
            dx_to_target = 1 if target_x > x else -1 if target_x < x else 0
            dy_to_target = 1 if target_y > y else -1 if target_y < y else 0
            
            # Prefer directions that move towards target
            preferred = []
            other = []
            for dx, dy in directions:
                if (dx != 0 and dx == dx_to_target) or (dy != 0 and dy == dy_to_target):
                    preferred.append((dx, dy))
                else:
                    other.append((dx, dy))
            directions = preferred + other
        
        # Try each direction
        for dx, dy in directions:
            new_x, new_y = x + dx, y + dy
            
            if (is_valid(new_x, new_y) and 
                (new_x, new_y) not in visited and
                (abs(new_x - x) + abs(new_y - y)) == 1):  # Only orthogonal moves
                
                if dfs(new_x, new_y, target_x, target_y):
                    return True
        
        # Backtrack
        path.pop()
        return False
    
    path = []
    visited = set()
    
    try:
        if dfs(start[0], start[1], end[0], end[1]):
            return path
    except RecursionError:
        print("DFS hit recursion limit")
    
    return None


def generate_fallback_path(start: Tuple[int, int], end: Tuple[int, int], 
                          min_length: int, width: int, height: int) -> List[Tuple[int, int]]:
    """
    Generate a guaranteed path as fallback with strategic loops for length
    """
    path = [start]
    current = start
    
    # If we need more length than direct path, add strategic detours
    direct_distance = abs(end[0] - start[0]) + abs(end[1] - start[1])
    
    if min_length > direct_distance:
        extra_length_needed = min_length - direct_distance
        
        # Add detours by going in loops
        detour_count = extra_length_needed // 4  # Each loop adds ~4 cells
        
        for i in range(min(detour_count, 3)):  # Limit to 3 detours max
            # Add a small rectangular detour
            if current[1] + 2 < height - 1 and current[0] + 2 < width - 1:
                # Right-down-left-up mini loop
                detour_path = [
                    (current[0] + 1, current[1]),
                    (current[0] + 2, current[1]),
                    (current[0] + 2, current[1] + 1),
                    (current[0] + 1, current[1] + 1),
                    (current[0], current[1] + 1)
                ]
                path.extend(detour_path)
                current = (current[0], current[1] + 1)
    
    # Now move towards the end
    while current != end:
        if current[0] < end[0]:
            current = (current[0] + 1, current[1])
        elif current[0] > end[0]:
            current = (current[0] - 1, current[1])
        elif current[1] < end[1]:
            current = (current[0], current[1] + 1)
        elif current[1] > end[1]:
            current = (current[0], current[1] - 1)
        
        path.append(current)
        
        # Safety check
        if len(path) > width * height:
            break
    
    return path


def build_terrain_grid_corrected(width: int, height: int, path: List[Tuple[int, int]], 
                               difficulty: int) -> List[List[int]]:
    """
    Build terrain grid using CORRECTED tower defense principles:
    - Easy: More buildable space (simple decisions) 
    - Hard: Strategic terrain constraints (complex decisions)
    - Always includes rock border around the map edges
    """
    # Initialize with grass
    terrain = [[GRASS for _ in range(width)] for _ in range(height)]
    
    # Mark path cells
    for x, y in path:
        if 0 <= x < width and 0 <= y < height:
            terrain[y][x] = PATH
    
    # Add rock border around the entire map (but preserve path cells)
    for y in range(height):
        for x in range(width):
            if (x == 0 or x == width-1 or y == 0 or y == height-1):
                # Only place rock if it's not a path cell
                if terrain[y][x] != PATH:
                    terrain[y][x] = ROCK
    
    # Get all non-path, non-border cells for interior terrain placement
    placeable_cells = []
    for y in range(1, height-1):  # Skip borders (now rocks)
        for x in range(1, width-1):  # Skip borders (now rocks)
            if terrain[y][x] == GRASS:
                placeable_cells.append((x, y))
    
    if not placeable_cells:
        return terrain
    
    # Strategic terrain placement based on mathematical difficulty principles
    if difficulty <= 30:  # Easy: Simple terrain, mostly grass (80% buildable)
        strategic_cells = int(len(placeable_cells) * 0.2)
        terrain_types = [WATER] * 30 + [FOREST] * 20 + [ROCK] * 50  # Light constraints
    elif difficulty <= 70:  # Medium: Mixed strategic terrain (60% buildable)
        strategic_cells = int(len(placeable_cells) * 0.4)
        terrain_types = [WATER] * 35 + [FOREST] * 35 + [ROCK] * 30  # Balanced mix
    else:  # Hard: Heavy strategic constraints (35% buildable)
        strategic_cells = int(len(placeable_cells) * 0.65)
        terrain_types = [WATER] * 40 + [FOREST] * 40 + [ROCK] * 20  # Force specialization
    
    # Place strategic terrain in interior
    if strategic_cells > 0:
        selected_cells = random.sample(placeable_cells, min(strategic_cells, len(placeable_cells)))
        
        for x, y in selected_cells:
            terrain_type = random.choice(terrain_types)
            terrain[y][x] = terrain_type
    
    return terrain


def build_terrain_grid(width: int, height: int, path: List[Tuple[int, int]], 
                      obstacle_density: float) -> List[List[int]]:
    """
    LEGACY terrain grid builder - DEPRECATED
    This uses the OLD (incorrect) tower defense logic where more obstacles = harder.
    Use build_terrain_grid_corrected() for proper tower defense design principles.
    """
    # Initialize with grass
    terrain = [[GRASS for _ in range(width)] for _ in range(height)]
    
    # Mark path cells
    for x, y in path:
        if 0 <= x < width and 0 <= y < height:
            terrain[y][x] = PATH
    
    # Legacy obstacle placement (INCORRECT LOGIC)
    total_placeable_cells = width * height - len(path)
    num_obstacles = int(total_placeable_cells * obstacle_density)
    
    obstacles_placed = 0
    terrain_types = [WATER, FOREST, SAND, ROCK]
    
    while obstacles_placed < num_obstacles:
        x = random.randint(1, width - 2)
        y = random.randint(1, height - 2)
        
        if terrain[y][x] == GRASS:
            terrain[y][x] = random.choice(terrain_types)
            obstacles_placed += 1
    
    return terrain


def create_balanced_terrain_distribution(width: int, height: int, path: List[Tuple[int, int]], 
                                       terrain_strategy: str = "balanced") -> List[List[int]]:
    """
    Create terrain with strategic distribution based on strategy
    Always includes rock border around the map edges
    """
    # Initialize with grass
    terrain = [[GRASS for _ in range(width)] for _ in range(height)]
    
    # Mark path cells
    for x, y in path:
        if 0 <= x < width and 0 <= y < height:
            terrain[y][x] = PATH
    
    # Add rock border around the entire map (but preserve path cells)
    for y in range(height):
        for x in range(width):
            if (x == 0 or x == width-1 or y == 0 or y == height-1):
                # Only place rock if it's not a path cell
                if terrain[y][x] != PATH:
                    terrain[y][x] = ROCK
    
    # Define terrain strategies
    if terrain_strategy.lower() == "forest_focus":
        terrain_weights = [FOREST] * 40 + [WATER] * 10 + [SAND] * 10 + [ROCK] * 20 + [GRASS] * 20
    elif terrain_strategy.lower() == "water_focus":
        terrain_weights = [WATER] * 30 + [FOREST] * 15 + [SAND] * 15 + [ROCK] * 20 + [GRASS] * 20
    elif terrain_strategy.lower() == "challenging":
        terrain_weights = [SAND] * 25 + [ROCK] * 25 + [WATER] * 15 + [FOREST] * 15 + [GRASS] * 20
    elif terrain_strategy.lower() == "beginner_friendly":
        terrain_weights = [GRASS] * 50 + [FOREST] * 25 + [WATER] * 15 + [SAND] * 5 + [ROCK] * 5
    else:  # balanced
        terrain_weights = [GRASS] * 30 + [FOREST] * 20 + [WATER] * 20 + [SAND] * 15 + [ROCK] * 15
    
    # Place strategic terrain in interior only (borders are now rocks)
    placeable_cells = [(x, y) for x in range(1, width-1) for y in range(1, height-1) 
                      if terrain[y][x] == GRASS]
    
    # Fill 30-50% of placeable cells with terrain
    num_terrain_cells = len(placeable_cells) // 3
    selected_cells = random.sample(placeable_cells, min(num_terrain_cells, len(placeable_cells)))
    
    for x, y in selected_cells:
        terrain_type = random.choice(terrain_weights)
        terrain[y][x] = terrain_type
    
    return terrain 