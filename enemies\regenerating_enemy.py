from .enemy import Enemy
import pygame


class RegeneratingEnemy(Enemy):
    """Enemy that regenerates health over time"""

    def __init__(self, path, wave_number=1):
        super().__init__(path, wave_number)
        self.max_health = 80
        self.health = self.max_health  # Start with full health
        self.speed = 1.0
        self.reward = 16
        self.color = (0, 255, 100)  # Light green
        self.regen_amount = 8  # Fixed regeneration amount (10% of base 80 HP)
        self.regen_timer = 0
        self.regen_delay = 30  # 0.5 seconds at 60 FPS before regen starts

    def update(self):
        """Update with regeneration logic"""
        super().update()
        self._update_health_regeneration(1.0)

    def update_with_speed(self, speed_multiplier: float):
        """Update with speed multiplier for performance optimization"""
        super().update_with_speed(speed_multiplier)
        self._update_health_regeneration(speed_multiplier)

    def _update_health_regeneration(self, speed_multiplier: float):
        """Update health regeneration with proper speed multiplier handling"""
        # Regenerate health if damaged
        if self.health < self.max_health:
            self.regen_timer += speed_multiplier

            if self.regen_timer >= self.regen_delay:
                # Regenerate fixed amount, but don't exceed maximum
                regen_amount = min(self.regen_amount,
                                   self.max_health - self.health)
                self.health += regen_amount
                self.regen_timer = 0

    def take_damage(self, damage, tower_type: str = 'basic'):
        """Take damage and reset regeneration timer"""
        actual_damage = super().take_damage(damage, tower_type)
        self.regen_timer = 0  # Reset regeneration timer when damaged
        return actual_damage

    def draw(self, screen):
        """Draw regenerating enemy with special effects"""
        # Draw main enemy
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), 10)
        pygame.draw.circle(screen, (255, 255, 255),
                           (int(self.x), int(self.y)), 10, 2)

        # Draw regeneration aura if regenerating
        if self.health < self.max_health:  # Show immediately when damaged
            # Calculate regeneration progress (0.0 to 1.0)
            regen_progress = self.regen_timer / self.regen_delay

            # Pulsing green effect that gets brighter as regeneration approaches
            pulse_intensity = int(64 + 191 * regen_progress)  # 64 to 255
            pulse_alpha = int(
                128 + 127 * abs(((self.regen_timer % 30) / 30.0) * 2 - 1))

            aura_color = (0, pulse_intensity, 0)
            pygame.draw.circle(screen, aura_color,
                               (int(self.x), int(self.y)), 15, 3)

        # Draw health bar
        if self.health < self.max_health:
            health_percentage = self.health / self.max_health
            bar_width = 20
            bar_height = 4

            pygame.draw.rect(screen, (255, 0, 0),
                             (self.x - 10, self.y - 18, bar_width, bar_height))
            pygame.draw.rect(screen, (0, 255, 0),
                             (self.x - 10, self.y - 18, int(bar_width * health_percentage), bar_height))
