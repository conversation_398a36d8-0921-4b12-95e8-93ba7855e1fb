from .enemy import Enemy
import pygame
import math
import random


class ShadowKing(Enemy):
    """Ultra powerful boss that manipulates shadows and dimensions"""

    def __init__(self, path, wave_number=1):
        super().__init__(path, wave_number)
        self.health = 733  # Reduced from 2200 to 1/3
        self.max_health = 733
        self.speed = 0.8  # Slightly faster but less tanky
        self.reward = 650  # NERFED: was 850
        self.color = (30, 30, 30)  # Very dark gray
        self.size = 33

        # Shadow abilities - NERFED
        self.phase_shift_timer = 0
        self.phase_shift_cooldown = 600  # 10 seconds (was 7)
        self.phase_shift_active = False
        self.phase_shift_duration = 120  # 2 seconds (was 3)

        # Teleportation ability
        self.teleport_timer = 0
        self.teleport_cooldown = 600  # 10 seconds
        self.teleport_charging = False
        self.teleport_charge_duration = 300  # 5 seconds
        self.teleport_charge_timer = 0
        self.original_speed = self.speed  # Store original speed

        # Tower destruction ability
        self.tower_destruction_timer = 0
        self.tower_destruction_cooldown = 1500  # 25 seconds
        self.towers_to_destroy = 7
        self.destruction_range = 200  # Range around <PERSON> King to find towers

        # Darkness manipulation
        self.darkness_aura_radius = 120
        self.darkness_timer = 0
        self.darkness_interval = 90  # Every 1.5 seconds

        # Boss resistances - NERFED
        self.damage_reduction = 0.35  # Takes 65% damage (was 55%)
        self.projectile_dodge_chance = 0.20  # 20% chance to dodge (was 30%)

        # Phase system
        self.phase = 1
        self.max_phases = 3

        # BALANCE FIX: Override immunities to ensure freeze effects can work
        # Bosses should never be completely immune to crowd control
        self.immunities['freeze_immune'] = False

        # Visual effects
        self.shadow_particles = []
        self.transparency = 255  # Full opacity when not phased
        self.shadow_tendrils = []

    def update(self):
        """Update with shadow abilities"""
        # Update phase based on health
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            self.phase = 1
        elif health_percentage > 0.33:
            self.phase = 2
        else:
            self.phase = 3

        super().update()

        # Update ability timers
        self.phase_shift_timer += 1
        self.teleport_timer += 1
        self.tower_destruction_timer += 1
        self.darkness_timer += 1

        # Handle phase shift
        if self.phase_shift_active:
            self.phase_shift_duration -= 1
            self.transparency = 100  # Semi-transparent when phased
            if self.phase_shift_duration <= 0:
                self.phase_shift_active = False
                self.transparency = 255

        # Handle teleport charging - must stay stationary
        if self.teleport_charging:
            self.teleport_charge_timer += 1
            self.speed = 0  # Stop moving while charging
            if self.teleport_charge_timer >= self.teleport_charge_duration:
                self.execute_teleport()
        else:
            # Restore normal speed when not charging
            if hasattr(self, 'original_speed'):
                self.speed = self.original_speed

        # Trigger abilities
        self.update_shadow_abilities()
        self.update_shadow_particles()

    def update_with_speed(self, speed_multiplier: float):
        """Update with speed multiplier for performance optimization"""
        # Update phase based on health
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            self.phase = 1
        elif health_percentage > 0.33:
            self.phase = 2
        else:
            self.phase = 3

        super().update_with_speed(speed_multiplier)

        # Update ability timers with speed multiplier
        self.phase_shift_timer += speed_multiplier
        self.teleport_timer += speed_multiplier
        self.tower_destruction_timer += speed_multiplier
        self.darkness_timer += speed_multiplier

        # Handle phase shift
        if self.phase_shift_active:
            self.phase_shift_duration -= speed_multiplier
            self.transparency = 100  # Semi-transparent when phased
            if self.phase_shift_duration <= 0:
                self.phase_shift_active = False
                self.transparency = 255

        # Handle teleport charging - must stay stationary
        if self.teleport_charging:
            self.teleport_charge_timer += speed_multiplier
            self.speed = 0  # Stop moving while charging
            if self.teleport_charge_timer >= self.teleport_charge_duration:
                self.execute_teleport()
        else:
            # Restore normal speed when not charging
            if hasattr(self, 'original_speed'):
                self.speed = self.original_speed

        # Trigger abilities
        self.update_shadow_abilities()
        self.update_shadow_particles()

    def update_shadow_abilities(self):
        """Update shadow-based abilities"""
        # Phase shift ability
        if self.phase_shift_timer >= self.phase_shift_cooldown:
            self.activate_phase_shift()

        # Teleportation ability
        if (self.teleport_timer >= self.teleport_cooldown and
                not self.teleport_charging and not self.phase_shift_active):
            self.start_teleport_charge()

        # Tower destruction ability
        if self.tower_destruction_timer >= self.tower_destruction_cooldown:
            self.trigger_tower_destruction()

        # Darkness aura
        if self.darkness_timer >= self.darkness_interval:
            self.pulse_darkness()
            self.darkness_timer = 0

    def activate_phase_shift(self):
        """Enter shadow dimension - become harder to hit"""
        self.phase_shift_active = True
        self.phase_shift_duration = 180
        self.phase_shift_timer = 0

        # Create phase shift effect
        for _ in range(25):
            particle = {
                'x': self.x + random.uniform(-self.size, self.size),
                'y': self.y + random.uniform(-self.size, self.size),
                'vx': random.uniform(-3, 3),
                'vy': random.uniform(-3, 3),
                'life': 50,
                'max_life': 50,
                'color': (100, 0, 200),  # Purple shadow
                'type': 'phase_shift'
            }
            self.shadow_particles.append(particle)

    def pulse_darkness(self):
        """Create expanding ring of darkness"""
        for i in range(12):
            angle = (i * 30) * math.pi / 180
            particle = {
                'x': self.x,
                'y': self.y,
                'vx': math.cos(angle) * 4,
                'vy': math.sin(angle) * 4,
                'life': 60,
                'max_life': 60,
                'color': (20, 20, 20),
                'type': 'darkness'
            }
            self.shadow_particles.append(particle)

    def start_teleport_charge(self):
        """Begin charging for teleportation"""
        self.teleport_charging = True
        self.teleport_charge_timer = 0
        self.teleport_timer = 0
        self.speed = 0  # Immediately stop moving

        # Create charging effect particles
        for _ in range(30):
            particle = {
                'x': self.x + random.uniform(-self.size * 2, self.size * 2),
                'y': self.y + random.uniform(-self.size * 2, self.size * 2),
                'vx': random.uniform(-2, 2),
                'vy': random.uniform(-2, 2),
                'life': 300,  # Last for the entire charge duration
                'max_life': 300,
                'color': (150, 0, 150),  # Purple charging energy
                'type': 'teleport_charge'
            }
            self.shadow_particles.append(particle)

    def execute_teleport(self):
        """Execute the teleportation"""
        # Find a new position along the path (move forward or backward)
        current_index = self.path_index
        path_length = len(self.path)

        # Try to teleport forward along the path
        teleport_distance = random.randint(3, 8)  # 3-8 segments forward
        new_index = min(current_index + teleport_distance, path_length - 1)

        # If we can't go forward much, try going backward
        if new_index - current_index < 3:
            new_index = max(0, current_index - teleport_distance)

        # Set new position
        if new_index != current_index:
            self.path_index = new_index
            self.x = float(self.path[new_index][0])
            self.y = float(self.path[new_index][1])

            # Update distance traveled appropriately
            segments_jumped = abs(new_index - current_index)
            avg_segment_length = 25  # Rough estimate
            if new_index > current_index:
                self.distance_traveled += segments_jumped * avg_segment_length
            else:
                # Moving backward, reduce distance traveled
                self.distance_traveled = max(
                    0, self.distance_traveled - segments_jumped * avg_segment_length)

        # Create teleport effect
        for _ in range(40):
            particle = {
                'x': self.x + random.uniform(-self.size * 3, self.size * 3),
                'y': self.y + random.uniform(-self.size * 3, self.size * 3),
                'vx': random.uniform(-5, 5),
                'vy': random.uniform(-5, 5),
                'life': 80,
                'max_life': 80,
                'color': (200, 0, 200),  # Bright purple teleport
                'type': 'teleport'
            }
            self.shadow_particles.append(particle)

        # Reset teleport state
        self.teleport_charging = False
        self.teleport_charge_timer = 0
        self.speed = self.original_speed  # Restore movement

    def trigger_tower_destruction(self):
        """Trigger the tower destruction ability"""
        self.tower_destruction_timer = 0

        # Create destruction effect particles
        for _ in range(50):
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(0, self.destruction_range)
            particle = {
                'x': self.x + math.cos(angle) * distance,
                'y': self.y + math.sin(angle) * distance,
                'vx': random.uniform(-3, 3),
                'vy': random.uniform(-3, 3),
                'life': 120,  # 2 seconds
                'max_life': 120,
                'color': (200, 0, 0),  # Red destruction energy
                'type': 'destruction'
            }
            self.shadow_particles.append(particle)

        # Store destruction request for game to handle
        # (We can't directly access towers from here)
        self.pending_tower_destruction = {
            'x': self.x,
            'y': self.y,
            'range': self.destruction_range,
            'count': self.towers_to_destroy
        }

    def get_pending_tower_destruction(self):
        """Get and clear any pending tower destruction request"""
        if hasattr(self, 'pending_tower_destruction'):
            destruction_data = self.pending_tower_destruction
            delattr(self, 'pending_tower_destruction')
            return destruction_data
        return None

    def update_shadow_particles(self):
        """Update shadow particle effects"""
        for particle in self.shadow_particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['life'] -= 1

            # Particle-specific behavior
            if particle['type'] == 'darkness':
                # Darkness particles expand outward
                particle['vx'] *= 1.05
                particle['vy'] *= 1.05
            elif particle['type'] == 'teleport_charge':
                # Charging particles spiral inward
                dx = self.x - particle['x']
                dy = self.y - particle['y']
                particle['vx'] += dx * 0.02
                particle['vy'] += dy * 0.02
            elif particle['type'] == 'teleport':
                # Teleport particles burst outward then fade
                particle['vx'] *= 0.95
                particle['vy'] *= 0.95
            elif particle['type'] == 'destruction':
                # Destruction particles swirl and expand
                particle['vx'] *= 1.02
                particle['vy'] *= 1.02

            if particle['life'] <= 0:
                self.shadow_particles.remove(particle)

    def take_damage(self, damage, tower_type: str = 'basic'):
        """Take damage with shadow resistances"""
        # Double damage while charging teleport
        if self.teleport_charging:
            damage *= 2

        # Increased dodge chance when phase shifted - NERFED
        dodge_chance = self.projectile_dodge_chance  # Base 20%
        if self.phase_shift_active:
            dodge_chance = 0.5  # 50% dodge when phased (was 70%)
            print(
                f"Shadow King phase shifted - dodge chance: {dodge_chance * 100}%")

        # Can't dodge while charging teleport
        if not self.teleport_charging:
            dodge_roll = random.random()
            if dodge_roll < dodge_chance:
                # Visual feedback for dodge
                print(
                    f"Shadow King DODGED! (roll: {dodge_roll:.3f} < {dodge_chance:.3f})")
                return 0  # Dodged the attack

        # Apply 3x damage multiplier for frozen enemies
        if self.frozen:
            damage = int(damage * 3.0)

        # Apply damage reduction
        reduced_damage = damage * (1 - self.damage_reduction)
        actual_damage = min(reduced_damage, self.health)

        self.health -= reduced_damage
        return actual_damage

    def apply_terrain_speed_effects(self):
        """Override to prevent speed changes during teleport charging"""
        # If charging teleport, maintain speed = 0 regardless of terrain
        if self.teleport_charging:
            self.speed = 0
            return

        # Otherwise, use normal terrain speed effects
        super().apply_terrain_speed_effects()

    def draw(self, screen):
        """Draw the Shadow King with shadow effects"""
        # Draw darkness aura (modified during teleport charging)
        if self.teleport_charging:
            # Pulsing purple aura while charging
            # Pulse every 0.5 seconds
            pulse = (self.teleport_charge_timer / 30) % 1.0
            aura_alpha = int(120 + 60 * abs(pulse - 0.5) * 2)
            aura_color = (100, 0, 100, aura_alpha)  # Purple
        else:
            aura_alpha = 80 if not self.phase_shift_active else 40
            aura_color = (0, 0, 0, aura_alpha)  # Black

        aura_surface = pygame.Surface((self.darkness_aura_radius * 2,
                                       self.darkness_aura_radius * 2),
                                      pygame.SRCALPHA)
        pygame.draw.circle(aura_surface, aura_color,
                           (self.darkness_aura_radius, self.darkness_aura_radius),
                           self.darkness_aura_radius)
        screen.blit(aura_surface, (self.x - self.darkness_aura_radius,
                                   self.y - self.darkness_aura_radius))

        # Draw shadow particles
        for particle in self.shadow_particles:
            alpha = particle['life'] / particle['max_life']
            size = max(1, int(6 * alpha))
            color = list(particle['color'])

            pygame.draw.circle(screen, color,
                               (int(particle['x']), int(particle['y'])), size)

        # Draw main boss with transparency effects
        boss_surface = pygame.Surface(
            (self.size * 3, self.size * 3), pygame.SRCALPHA)

        # Shadow King appearance - multiple shadow layers
        for i in range(3):
            layer_size = self.size - i * 3
            layer_alpha = int(self.transparency * (0.8 - i * 0.2))
            layer_color = (30 + i * 20, 30 + i * 20, 30 + i * 20, layer_alpha)

            pygame.draw.circle(boss_surface, layer_color,
                               (self.size * 1.5, self.size * 1.5), layer_size)

        # Draw crown/spikes
        crown_points = []
        for i in range(8):
            angle = i * 45 * math.pi / 180
            spike_length = self.size + 10 if i % 2 == 0 else self.size + 5
            spike_x = self.size * 1.5 + math.cos(angle) * spike_length
            spike_y = self.size * 1.5 + math.sin(angle) * spike_length
            crown_points.append((int(spike_x), int(spike_y)))

        # Draw shadow crown
        for point in crown_points:
            pygame.draw.line(boss_surface, (100, 100, 100, self.transparency),
                             (self.size * 1.5, self.size * 1.5), point, 3)

        # Blit the boss surface to screen
        screen.blit(boss_surface, (self.x - self.size *
                    1.5, self.y - self.size * 1.5))

        # Draw glowing eyes
        eye_color = (255, 0, 0) if self.phase < 3 else (
            255, 255, 0)  # Red eyes, yellow in final phase
        eye_alpha = self.transparency

        left_eye_x = self.x - 8
        right_eye_x = self.x + 8
        eye_y = self.y - 5

        eye_surface = pygame.Surface((6, 6), pygame.SRCALPHA)
        pygame.draw.circle(eye_surface, (*eye_color, eye_alpha), (3, 3), 3)

        screen.blit(eye_surface, (left_eye_x - 3, eye_y - 3))
        screen.blit(eye_surface, (right_eye_x - 3, eye_y - 3))

        # Draw health bar
        bar_width = self.size * 4
        bar_height = 12

        pygame.draw.rect(screen, (20, 20, 20),
                         (self.x - bar_width//2, self.y - self.size - 30, bar_width, bar_height))

        health_percentage = self.health / self.max_health
        health_color = (
            100, 0, 100) if not self.phase_shift_active else (150, 0, 200)
        pygame.draw.rect(screen, health_color,
                         (self.x - bar_width//2, self.y - self.size - 30,
                          int(bar_width * health_percentage), bar_height))

        # Draw boss title
        font = pygame.font.Font(None, 28)
        phase_text = " (PHASED)" if self.phase_shift_active else ""
        title_text = font.render(
            f"SHADOW KING{phase_text}", True, (255, 255, 255))
        title_rect = title_text.get_rect(
            center=(self.x, self.y - self.size - 50))
        screen.blit(title_text, title_rect)
