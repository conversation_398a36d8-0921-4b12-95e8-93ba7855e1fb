"""
Tower Category System - Organizes towers by their roles and characteristics
"""

from enum import Enum
from typing import Dict, List, Set, Optional

class TowerCategory(Enum):
    """Tower categories based on their roles and mechanics"""
    PRECISION_DIRECT_FIRE = "precision_direct_fire"
    AREA_DEVASTATION = "area_devastation"
    ELEMENTAL_ENERGY = "elemental_energy"
    STATUS_TACTICAL = "status_tactical"
    ENVIRONMENTAL_SPECIALISTS = "environmental_specialists"

class TowerCategoryManager:
    """Manages tower categorization and provides category-based functionality"""
    
    def __init__(self):
        # Define tower categories with their characteristics
        self.category_definitions = {
            TowerCategory.PRECISION_DIRECT_FIRE: {
                'name': '🎯 Precision & Direct Fire',
                'description': 'High-damage, single-target towers with precise targeting',
                'characteristics': [
                    'Single-target focus',
                    'Reliable damage output', 
                    'Strategic positioning important',
                    'Consistent performance'
                ],
                'towers': {'basic', 'sniper', 'antiair', 'laser'},
                'color': (0, 150, 255),  # Blue
                'icon': '🎯'
            },
            
            TowerCategory.AREA_DEVASTATION: {
                'name': '💥 Area Devastation',
                'description': 'Massive explosion damage and area denial',
                'characteristics': [
                    'High splash damage',
                    'Area denial',
                    'Excellent against groups',
                    'Some terrain restrictions'
                ],
                'towers': {'explosive', 'cannon', 'missile', 'splash', 'destroyer'},
                'color': (255, 100, 0),  # Orange/Red
                'icon': '💥'
            },
            
            TowerCategory.ELEMENTAL_ENERGY: {
                'name': '⚡ Elemental & Energy',
                'description': 'Magical/energy-based attacks with special effects',
                'characteristics': [
                    'Status effects',
                    'Elemental synergies',
                    'Crowd control',
                    'Damage over time'
                ],
                'towers': {'lightning', 'flame', 'ice', 'freezer'},
                'color': (255, 255, 0),  # Yellow
                'icon': '⚡'
            },
            
            TowerCategory.STATUS_TACTICAL: {
                'name': '🧪 Status & Tactical',
                'description': 'Specialized tactical roles and status effects',
                'characteristics': [
                    'Support roles',
                    'Status effects',
                    'Strategic necessity',
                    'Specialized counters'
                ],
                'towers': {'poison', 'detector'},
                'color': (0, 255, 0),  # Green
                'icon': '🧪'
            },
            
            TowerCategory.ENVIRONMENTAL_SPECIALISTS: {
                'name': '🌊 Environmental Specialists',
                'description': 'Terrain-dependent towers with unique placement requirements',
                'characteristics': [
                    'Terrain restrictions',
                    'Unique placement rules',
                    'Specialized environments',
                    'Map-dependent power'
                ],
                'towers': {'splash', 'destroyer'},  # Note: splash appears in both categories
                'color': (0, 255, 255),  # Cyan
                'icon': '🌊'
            }
        }
        
        # Create reverse mapping: tower_type -> category
        self.tower_to_category = {}
        for category, data in self.category_definitions.items():
            for tower_type in data['towers']:
                if tower_type not in self.tower_to_category:
                    self.tower_to_category[tower_type] = []
                self.tower_to_category[tower_type].append(category)
    
    def get_category_for_tower(self, tower_type: str) -> List[TowerCategory]:
        """Get the category(ies) for a specific tower type"""
        return self.tower_to_category.get(tower_type, [])
    
    def get_primary_category_for_tower(self, tower_type: str) -> Optional[TowerCategory]:
        """Get the primary category for a tower (first category if multiple)"""
        categories = self.get_category_for_tower(tower_type)
        return categories[0] if categories else None
    
    def get_towers_in_category(self, category: TowerCategory) -> Set[str]:
        """Get all towers in a specific category"""
        return self.category_definitions.get(category, {}).get('towers', set())
    
    def get_category_info(self, category: TowerCategory) -> Dict:
        """Get complete information about a category"""
        return self.category_definitions.get(category, {})
    
    def get_all_categories(self) -> List[TowerCategory]:
        """Get all available categories"""
        return list(self.category_definitions.keys())
    
    def get_category_color(self, category: TowerCategory) -> tuple:
        """Get the color associated with a category"""
        return self.category_definitions.get(category, {}).get('color', (128, 128, 128))
    
    def get_category_icon(self, category: TowerCategory) -> str:
        """Get the icon for a category"""
        return self.category_definitions.get(category, {}).get('icon', '🏗️')
    
    def is_tower_in_category(self, tower_type: str, category: TowerCategory) -> bool:
        """Check if a tower belongs to a specific category"""
        return tower_type in self.get_towers_in_category(category)
    
    def get_towers_by_category(self) -> Dict[TowerCategory, Set[str]]:
        """Get all towers organized by category"""
        result = {}
        for category in self.category_definitions.keys():
            result[category] = self.get_towers_in_category(category)
        return result
    
    def get_category_stats(self) -> Dict[TowerCategory, Dict]:
        """Get statistics about each category"""
        stats = {}
        for category, data in self.category_definitions.items():
            stats[category] = {
                'name': data['name'],
                'tower_count': len(data['towers']),
                'towers': list(data['towers']),
                'color': data['color'],
                'icon': data['icon']
            }
        return stats
    
    def print_category_breakdown(self):
        """Print a detailed breakdown of all categories (for debugging)"""
        print("\n=== TOWER CATEGORY BREAKDOWN ===")
        for category, data in self.category_definitions.items():
            print(f"\n{data['icon']} {data['name']}")
            print(f"   Description: {data['description']}")
            print(f"   Towers: {', '.join(sorted(data['towers']))}")
            print(f"   Characteristics:")
            for char in data['characteristics']:
                print(f"     • {char}")
    
    def validate_categorization(self, all_tower_types: Set[str]) -> Dict[str, List[str]]:
        """Validate that all towers are properly categorized"""
        validation_result = {
            'uncategorized': [],
            'multi_category': [],
            'warnings': []
        }
        
        # Check for uncategorized towers
        categorized_towers = set()
        for category_data in self.category_definitions.values():
            categorized_towers.update(category_data['towers'])
        
        for tower_type in all_tower_types:
            if tower_type not in categorized_towers:
                validation_result['uncategorized'].append(tower_type)
        
        # Check for towers in multiple categories
        for tower_type in categorized_towers:
            categories = self.get_category_for_tower(tower_type)
            if len(categories) > 1:
                validation_result['multi_category'].append(tower_type)
        
        # Add warnings for unusual cases
        if validation_result['uncategorized']:
            validation_result['warnings'].append(f"Found {len(validation_result['uncategorized'])} uncategorized towers")
        
        if validation_result['multi_category']:
            validation_result['warnings'].append(f"Found {len(validation_result['multi_category'])} towers in multiple categories")
        
        return validation_result 