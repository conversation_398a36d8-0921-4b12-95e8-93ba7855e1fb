from .enemy import Enemy
from .basic_enemy import BasicEnemy
import pygame


class SplittingEnemy(Enemy):
    """Enemy that splits into smaller enemies when destroyed"""

    def __init__(self, path, split_count=2, generation=1, wave_number=1):
        super().__init__(path, wave_number)
        self.generation = generation
        self.split_count = split_count

        # Scale stats based on generation
        base_health = 30
        base_speed = 0.8
        base_reward = 20

        # Prevent division by zero
        safe_generation = max(1, generation)
        self.health = int(base_health / safe_generation)
        self.max_health = self.health
        self.speed = base_speed + (generation - 1) * 0.3
        self.reward = int(base_reward / safe_generation)

        # Visual properties
        self.color = (255, 0, 255)  # Magenta
        self.size = max(12 - generation * 2, 6)  # Smaller each generation

    def draw(self, screen):
        """Draw splitting enemy with generation indicators"""
        # Draw main body
        pygame.draw.circle(screen, self.color,
                           (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, (255, 255, 255),
                           (int(self.x), int(self.y)), self.size, 2)

        # Draw generation indicators (small dots)
        for i in range(self.generation):
            dot_x = self.x - self.size + 3 + i * 4
            dot_y = self.y - self.size + 3
            pygame.draw.circle(screen, (255, 255, 255),
                               (int(dot_x), int(dot_y)), 2)

        # Draw health bar
        if self.health < self.max_health and self.max_health > 0:
            health_percentage = self.health / self.max_health
            bar_width = self.size * 2
            bar_height = 3

            pygame.draw.rect(screen, (255, 0, 0),
                             (self.x - self.size, self.y - self.size - 8, bar_width, bar_height))
            pygame.draw.rect(screen, (0, 255, 0),
                             (self.x - self.size, self.y - self.size - 8,
                              int(bar_width * health_percentage), bar_height))

    def on_death(self):
        """Return list of random enemies to spawn when this enemy dies"""
        # Check generation limit - stop splitting at generation 3
        if self.generation >= 3:
            return []

        # Always spawn 2 random enemies (regardless of generation)
        spawned_enemies = []

        # Get available enemy types from the current wave configuration
        available_enemies = self._get_available_enemies_for_wave()

        if not available_enemies:
            # Fallback to basic enemies if no configuration found
            from . import BasicEnemy, FastEnemy
            available_enemies = [BasicEnemy, FastEnemy]

        # Spawn 2 random enemies from the available pool
        import random
        for i in range(2):
            enemy_class = random.choice(available_enemies)
            new_enemy = enemy_class(self.path, self.wave_number)

            # CRITICAL: Properly inherit path position and state
            new_enemy.path_index = self.path_index
            new_enemy.distance_traveled = self.distance_traveled

            # Position them at the actual death location with very small offsets
            # This ensures they continue from where the splitting enemy died
            # Very small random horizontal offset
            offset_x = random.uniform(-8, 8)
            # Very small random vertical offset
            offset_y = random.uniform(-8, 8)
            new_enemy.x = self.x + offset_x
            new_enemy.y = self.y + offset_y

            # Make spawned enemies slightly weaker (75% health, minimum 1)
            new_enemy.health = max(1, int(new_enemy.health * 0.75))
            new_enemy.max_health = new_enemy.health

            # Ensure they inherit any status effects appropriately
            new_enemy.reached_end = False

            # Debug info (optional)
            # print(f"Split enemy spawned: {enemy_class.__name__} at path_index {new_enemy.path_index}, pos ({new_enemy.x:.1f}, {new_enemy.y:.1f})")

            spawned_enemies.append(new_enemy)

        return spawned_enemies

    def _get_available_enemies_for_wave(self):
        """Get list of enemy classes available for the current wave (excluding bosses)"""
        try:
            from config.game_config import get_wave_config
            config = get_wave_config()
            wave_compositions = config.get('wave_compositions', {})
            boss_waves = config.get('boss_waves', {})

            # Find the appropriate wave range for current wave
            available_enemy_names = []
            for wave_range, enemy_types in wave_compositions.items():
                # Handle both range formats: "1-5" and single numbers "6"
                if isinstance(wave_range, str):
                    if '-' in wave_range:
                        min_wave, max_wave = map(int, wave_range.split('-'))
                    else:
                        # Single wave number
                        min_wave = max_wave = int(wave_range)
                elif isinstance(wave_range, tuple):
                    min_wave, max_wave = wave_range
                else:
                    continue

                if min_wave <= self.wave_number <= max_wave:
                    # Extract enemy names from the composition (excluding bosses)
                    for enemy_name, weight in enemy_types:
                        if enemy_name not in boss_waves.values():  # Exclude boss enemies
                            available_enemy_names.append(enemy_name)
                    break

            # Convert enemy names to actual classes (excluding SplittingEnemy to prevent infinite chains)
            available_enemy_classes = []
            for enemy_name in available_enemy_names:
                if enemy_name == 'SplittingEnemy':
                    continue  # Don't spawn more splitting enemies
                enemy_class = self._get_enemy_class_by_name(enemy_name)
                if enemy_class:
                    available_enemy_classes.append(enemy_class)

            return available_enemy_classes

        except Exception as e:
            print(
                f"Error getting available enemies for wave {self.wave_number}: {e}")
            return []

    def _get_enemy_class_by_name(self, enemy_name):
        """Convert enemy name string to actual enemy class"""
        try:
            # Import all enemy types
            from . import (BasicEnemy, FastEnemy, TankEnemy, InvisibleEnemy, FlyingEnemy,
                           ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy, FireElementalEnemy,
                           ToxicEnemy, RegeneratingEnemy, TeleportingEnemy, SplittingEnemy,
                           PhaseShiftEnemy, BlastProofEnemy, SpectralEnemy, CrystallineEnemy,
                           ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy, ShieldedEnemy)

            # Map enemy names to classes
            enemy_map = {
                'BasicEnemy': BasicEnemy,
                'FastEnemy': FastEnemy,
                'TankEnemy': TankEnemy,
                'InvisibleEnemy': InvisibleEnemy,
                'FlyingEnemy': FlyingEnemy,
                'ArmoredEnemy': ArmoredEnemy,
                'EnergyShieldEnemy': EnergyShieldEnemy,
                'GroundedEnemy': GroundedEnemy,
                'FireElementalEnemy': FireElementalEnemy,
                'ToxicEnemy': ToxicEnemy,
                'RegeneratingEnemy': RegeneratingEnemy,
                'TeleportingEnemy': TeleportingEnemy,
                'SplittingEnemy': SplittingEnemy,
                'PhaseShiftEnemy': PhaseShiftEnemy,
                'BlastProofEnemy': BlastProofEnemy,
                'SpectralEnemy': SpectralEnemy,
                'CrystallineEnemy': CrystallineEnemy,
                'ToxicMutantEnemy': ToxicMutantEnemy,
                'VoidEnemy': VoidEnemy,
                'AdaptiveEnemy': AdaptiveEnemy,
                'ShieldedEnemy': ShieldedEnemy
            }

            return enemy_map.get(enemy_name)

        except ImportError as e:
            print(f"Could not import enemy class {enemy_name}: {e}")
            return None
