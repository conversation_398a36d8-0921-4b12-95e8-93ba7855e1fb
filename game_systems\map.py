import pygame
import math
from typing import List, Tu<PERSON>, Optional
from config.game_config import get_map_config
from .terrain_types import *
from .tower_sizes import *


class Map:
    """Handles grid-based map layout, terrain types, and tower placement validation"""

    def __init__(self, screen_width: int, screen_height: int, map_name: str = 'default_map'):
        self.screen_width = screen_width
        self.screen_height = screen_height
        self.map_name = map_name

        # Load map data from centralized config
        all_maps = get_map_config()
        if map_name not in all_maps:
            map_name = 'default_map'  # Fallback to default

        map_data = all_maps[map_name]
        self.grid_layout = map_data['terrain']
        self.grid_width = map_data['width']
        self.grid_height = map_data['height']
        self.path_waypoints = map_data['path']

        # Calculate dynamic map positioning and cell size to fill available space
        self.top_ui_height = 140  # Space for top UI
        self.bottom_ui_height = 120  # Space for bottom UI

        # Available space for map
        available_width = screen_width
        available_height = screen_height - self.top_ui_height - self.bottom_ui_height

        # Calculate cell size to fit the grid perfectly in available space
        cell_width = available_width // self.grid_width
        cell_height = available_height // self.grid_height
        # Use smaller to maintain aspect ratio
        self.cell_size = min(cell_width, cell_height)

        # Calculate actual map dimensions and center it
        actual_map_width = self.grid_width * self.cell_size
        actual_map_height = self.grid_height * self.cell_size

        self.map_offset_x = (available_width - actual_map_width) // 2
        self.map_offset_y = self.top_ui_height + \
            (available_height - actual_map_height) // 2

        # Convert grid waypoints to pixel coordinates
        self.path = self._convert_waypoints_to_pixels()

        # Colors for placement preview
        self.GREEN = (0, 255, 0)
        self.RED = (255, 0, 0)
        self.YELLOW = (255, 255, 0)

        # Tower placement constraints (dynamic based on cell size)
        self.min_distance_between_towers = max(
            self.cell_size * 0.8, 25)  # Scale with cell size

    def _convert_waypoints_to_pixels(self) -> List[Tuple[int, int]]:
        """Convert grid-based waypoints to pixel coordinates"""
        pixel_path = []
        for grid_x, grid_y in self.path_waypoints:
            pixel_x = self.map_offset_x + grid_x * self.cell_size + self.cell_size // 2
            pixel_y = self.map_offset_y + grid_y * self.cell_size + self.cell_size // 2
            pixel_path.append((pixel_x, pixel_y))
        return pixel_path

    def get_path(self) -> List[Tuple[int, int]]:
        """Get the enemy path in pixel coordinates"""
        return self.path

    def pixel_to_grid(self, pixel_x: int, pixel_y: int) -> Tuple[int, int]:
        """Convert pixel coordinates to grid coordinates"""
        grid_x = (pixel_x - self.map_offset_x) // self.cell_size
        grid_y = (pixel_y - self.map_offset_y) // self.cell_size
        return (grid_x, grid_y)

    def grid_to_pixel(self, grid_x: int, grid_y: int) -> Tuple[int, int]:
        """Convert grid coordinates to pixel coordinates (center of cell)"""
        pixel_x = self.map_offset_x + grid_x * self.cell_size + self.cell_size // 2
        pixel_y = self.map_offset_y + grid_y * self.cell_size + self.cell_size // 2
        return (pixel_x, pixel_y)

    def get_terrain_at_pixel(self, pixel_x: int, pixel_y: int) -> int:
        """Get terrain type at pixel coordinates"""
        grid_x, grid_y = self.pixel_to_grid(pixel_x, pixel_y)
        return self.get_terrain_at_grid(grid_x, grid_y)

    def get_terrain_at_grid(self, grid_x: int, grid_y: int) -> int:
        """Get terrain type at grid coordinates"""
        if 0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height:
            return self.grid_layout[grid_y][grid_x]
        return GRASS  # Default to grass if out of bounds

    def is_valid_tower_position(self, pixel_x: int, pixel_y: int, existing_towers: List,
                                tower_type: str = None) -> bool:  # type: ignore
        """Check if a position is valid for tower placement"""
        grid_x, grid_y = self.pixel_to_grid(pixel_x, pixel_y)

        # Check if tower can be placed at this position (multi-block support)
        if tower_type and not can_place_tower_at_position(grid_x, grid_y, tower_type,
                                                          self.grid_width, self.grid_height, existing_towers):
            return False

        # Special validation for destroyer tower
        if tower_type == 'destroyer':
            from .tower_sizes import can_place_destroyer_tower
            if not can_place_destroyer_tower(grid_x, grid_y, self):
                return False

        # Check if within map bounds (for single cell, multi-cell handled above)
        if not tower_type and not (0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height):
            return False

        # Get all cells that would be occupied by this tower
        if tower_type:
            occupied_cells = get_tower_occupied_cells(
                grid_x, grid_y, tower_type)
        else:
            occupied_cells = [(grid_x, grid_y)]

        # For destroyer tower, we already validated terrain requirements above
        if tower_type == 'destroyer':
            return True

        # Check terrain for all occupied cells (for non-destroyer towers)
        for cell_x, cell_y in occupied_cells:
            if not (0 <= cell_x < self.grid_width and 0 <= cell_y < self.grid_height):
                return False

            terrain_type = self.get_terrain_at_grid(cell_x, cell_y)

            # Check if terrain allows tower placement
            if not is_tower_placeable(terrain_type):
                return False

            # Check if specific tower type can be placed on this terrain
            if tower_type and not can_place_tower_type(terrain_type, tower_type):
                return False

        return True

    def _calculate_tower_center_from_edge_click(self, click_x: int, click_y: int, tower_type: str) -> tuple:
        """Calculate tower center position for edge-based placement"""
        from .tower_sizes import get_tower_visual_size
        tower_radius = get_tower_visual_size(tower_type, self.cell_size)

        # Calculate map center for reference
        map_center_x = self.map_offset_x + \
            (self.grid_width * self.cell_size) // 2
        map_center_y = self.map_offset_y + \
            (self.grid_height * self.cell_size) // 2

        # Calculate direction from map center to click position
        dx = click_x - map_center_x
        dy = click_y - map_center_y

        # Normalize the direction vector
        distance = (dx * dx + dy * dy) ** 0.5
        if distance == 0:
            # Click is at map center, place tower center at click
            return click_x, click_y

        # Calculate unit vector pointing from map center to click
        unit_x = dx / distance
        unit_y = dy / distance

        # Move tower center inward by tower radius from the click position
        # This places the tower edge at the click position
        center_x = click_x - unit_x * tower_radius
        center_y = click_y - unit_y * tower_radius

        return int(center_x), int(center_y)

    def _validate_tower_terrain_coverage(self, center_x: int, center_y: int, radius: int, tower_type: str) -> bool:
        """Validate that the tower doesn't overlap with invalid terrain like paths"""
        from .terrain_types import is_tower_placeable, can_place_tower_type, PATH

        # Sample points in a circle around the tower center
        sample_points = 8  # Check 8 points around the perimeter plus center

        # Reset water check counter for water-only towers
        if tower_type in ['splash', 'destroyer']:
            self._water_check_non_water_count = 0

        # Check center point
        center_grid_x, center_grid_y = self.pixel_to_grid(center_x, center_y)
        center_terrain = self.get_terrain_at_grid(center_grid_x, center_grid_y)

        if not is_tower_placeable(center_terrain):
            return False
        if not can_place_tower_type(center_terrain, tower_type):
            return False

        # Check points around the perimeter
        for i in range(sample_points):
            # Distribute points evenly around circle
            angle = (i * 2 * 3.14159) / sample_points

            # Calculate sample point on the tower's edge
            sample_x = center_x + radius * 0.8 * \
                math.cos(angle)  # 0.8 to stay slightly inside
            sample_y = center_y + radius * 0.8 * math.sin(angle)

            # Convert to grid coordinates
            grid_x, grid_y = self.pixel_to_grid(int(sample_x), int(sample_y))

            # Check if sample point is within map bounds
            if not (0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height):
                # If sample point is outside map, that's invalid
                return False

            terrain_type = self.get_terrain_at_grid(grid_x, grid_y)

            # Strict check: no part of tower can overlap with path
            if terrain_type == PATH:
                return False

            # For water-only towers, ensure sufficient water coverage
            if tower_type in ['splash', 'destroyer']:
                if terrain_type != WATER:
                    # Count how many non-water points we've found
                    if not hasattr(self, '_water_check_non_water_count'):
                        self._water_check_non_water_count = 0
                    self._water_check_non_water_count += 1

                    # Allow some non-water points but not too many
                    max_non_water = sample_points // 3  # Allow up to 1/3 non-water
                    if self._water_check_non_water_count > max_non_water:
                        return False

            # For other towers, only block if terrain is completely unplaceable (like rocks)
            # Allow placement on grass, water, forest, sand, etc.
            if not is_tower_placeable(terrain_type):
                return False

        return True

    def is_valid_tower_position_pixel(self, pixel_x: int, pixel_y: int, existing_towers: List,
                                      tower_type: str = None) -> bool:
        """Check if a position is valid for pixel-based tower placement (edge-based)"""
        if not tower_type:
            return False

        # Calculate tower center position from edge click
        tower_center_x, tower_center_y = self._calculate_tower_center_from_edge_click(
            pixel_x, pixel_y, tower_type)

        # Get tower visual size for collision detection
        from .tower_sizes import get_tower_visual_size
        tower_radius = get_tower_visual_size(tower_type, self.cell_size)

        # Check if tower center would be within map bounds
        min_center_x = self.map_offset_x + tower_radius
        max_center_x = self.map_offset_x + self.grid_width * self.cell_size - tower_radius
        min_center_y = self.map_offset_y + tower_radius
        max_center_y = self.map_offset_y + self.grid_height * self.cell_size - tower_radius

        if (tower_center_x < min_center_x or tower_center_x > max_center_x or
                tower_center_y < min_center_y or tower_center_y > max_center_y):
            return False

        # Check collision with existing towers using distance-based detection
        for existing_tower in existing_towers:
            distance = ((tower_center_x - existing_tower.x) ** 2 +
                        (tower_center_y - existing_tower.y) ** 2) ** 0.5
            existing_tower_radius = get_tower_visual_size(
                getattr(existing_tower, 'tower_type', 'basic'), self.cell_size)

            # Minimum distance should be sum of radii plus minimal buffer
            # Reduced buffer for tighter placement
            min_distance = tower_radius + existing_tower_radius + 2
            if distance < min_distance:
                return False

        # Check terrain validation by sampling multiple points around the tower
        # This prevents towers from overlapping with paths or invalid terrain
        if not self._validate_tower_terrain_coverage(tower_center_x, tower_center_y, tower_radius, tower_type):
            return False

        # Special validation for destroyer tower (3x3 water requirement)
        if tower_type == 'destroyer':
            return self._validate_destroyer_pixel_placement(tower_center_x, tower_center_y)

        return True

    def get_tower_center_from_edge_click(self, pixel_x: int, pixel_y: int, tower_type: str) -> tuple:
        """Public method to get tower center position for edge-based placement"""
        return self._calculate_tower_center_from_edge_click(pixel_x, pixel_y, tower_type)

    def _validate_destroyer_pixel_placement(self, pixel_x: int, pixel_y: int) -> bool:
        """Validate destroyer tower placement with pixel coordinates"""
        from .terrain_types import WATER, PATH, ROCK
        from .tower_sizes import get_tower_visual_size

        # Get destroyer tower radius
        destroyer_radius = get_tower_visual_size('destroyer', self.cell_size)

        # Check area around the tower position for water requirements
        water_count = 0
        check_radius = destroyer_radius * 1.2  # Slightly larger area for water check

        # Sample points in a grid pattern around the tower
        sample_points = 9  # 3x3 grid of sample points
        for i in range(sample_points):
            for j in range(sample_points):
                # Calculate sample position
                offset_x = (i - sample_points // 2) * \
                    (check_radius * 2 / (sample_points - 1))
                offset_y = (j - sample_points // 2) * \
                    (check_radius * 2 / (sample_points - 1))

                sample_x = pixel_x + offset_x
                sample_y = pixel_y + offset_y

                # Check if sample point is within destroyer area
                distance = ((sample_x - pixel_x) ** 2 +
                            (sample_y - pixel_y) ** 2) ** 0.5
                if distance <= destroyer_radius:
                    terrain_type = self.get_terrain_at_pixel(
                        int(sample_x), int(sample_y))

                    # Count water blocks
                    if terrain_type == WATER:
                        water_count += 1

                    # Check for forbidden terrain types
                    if terrain_type == PATH or terrain_type == ROCK:
                        return False

        # Must have sufficient water coverage (at least 1/3 of sampled points)
        return water_count >= sample_points * sample_points // 3

    def get_placement_info(self, pixel_x: int, pixel_y: int, tower_type: str = None) -> dict:  # type: ignore
        """Get detailed placement information for UI feedback"""
        grid_x, grid_y = self.pixel_to_grid(pixel_x, pixel_y)
        terrain_type = self.get_terrain_at_grid(grid_x, grid_y)

        info = {
            'terrain_name': get_terrain_name(terrain_type),
            'can_place': is_tower_placeable(terrain_type),
            'tower_allowed': True if not tower_type else can_place_tower_type(terrain_type, tower_type),
            'special_rules': get_terrain_property(terrain_type, 'special_rules')
        }

        return info

    # REMOVED: apply_terrain_effects_to_tower() - now handled by Tower.apply_terrain_effects()

    def draw_terrain(self, screen: pygame.Surface):
        """Draw the terrain grid with smooth transitions"""
        for y in range(self.grid_height):
            for x in range(self.grid_width):
                terrain_type = self.grid_layout[y][x]
                color = get_terrain_color(terrain_type)

                rect = pygame.Rect(
                    self.map_offset_x + x * self.cell_size,
                    self.map_offset_y + y * self.cell_size,
                    self.cell_size,
                    self.cell_size
                )
                pygame.draw.rect(screen, color, rect)

                # Add subtle animations for different terrain types
                if terrain_type == 3:  # WATER
                    self._add_subtle_water_effect(screen, rect, x, y)
                elif terrain_type == 0:  # GRASS
                    self._add_subtle_grass_effect(screen, rect, x, y)
                elif terrain_type == 4:  # FOREST
                    self._add_subtle_forest_effect(screen, rect, x, y)
                # SAND - Fixed: was incorrectly on ROCK (2)
                elif terrain_type == 5:
                    self._add_subtle_sand_effect(screen, rect, x, y)
                elif terrain_type == 2:  # ROCK
                    self._add_subtle_rock_effect(screen, rect, x, y)
                elif terrain_type == 6:  # PIT
                    self._add_subtle_pit_effect(screen, rect, x, y)

    def _add_subtle_water_effect(self, screen: pygame.Surface, rect: pygame.Rect, grid_x: int, grid_y: int):
        """Add very subtle water ripple effect that doesn't change the base appearance"""
        import pygame
        import math

        # Only add 1-2 very small, subtle ripples per water tile
        current_time = pygame.time.get_ticks()

        # Use grid position to create consistent but varied ripple timing
        phase_offset = (grid_x * 0.7 + grid_y * 1.1) * 2
        ripple_phase = (current_time * 0.002 + phase_offset) % (2 * math.pi)

        # Only draw ripple occasionally and very subtly
        if math.sin(ripple_phase) > 0.7:  # Only show ripple 15% of the time
            center_x = rect.centerx
            center_y = rect.centery

            # Very small, subtle ripple
            ripple_radius = 3 + math.sin(ripple_phase * 2) * 1
            ripple_color = (50, 180, 255)  # Slightly lighter blue

            # Draw just a thin circle, very subtle
            pygame.draw.circle(screen, ripple_color,
                               (center_x, center_y), int(ripple_radius), 1)

    def _add_subtle_grass_effect(self, screen: pygame.Surface, rect: pygame.Rect, grid_x: int, grid_y: int):
        """Add subtle swaying grass effect"""
        import pygame
        import math

        current_time = pygame.time.get_ticks()

        # Use grid position for consistent but varied timing
        phase_offset = (grid_x * 1.3 + grid_y * 0.9) * 1.5
        sway_phase = (current_time * 0.001 + phase_offset) % (2 * math.pi)

        # Only show grass blades occasionally and subtly
        if math.sin(sway_phase) > 0.6:  # Show grass 20% of the time
            # Create 1-2 small grass blades that sway gently
            for i in range(2):
                blade_x = rect.x + (rect.width // 4) + i * (rect.width // 2)
                blade_y = rect.y + rect.height - 3

                # Calculate gentle sway
                sway_offset = math.sin(sway_phase + i * 0.5) * 2
                blade_top_x = blade_x + sway_offset
                blade_top_y = blade_y - 4

                # Draw thin grass blade
                grass_color = (20, 100, 20)  # Darker green
                pygame.draw.line(screen, grass_color,
                                 (blade_x, blade_y), (int(blade_top_x), blade_top_y), 1)

    def _add_subtle_forest_effect(self, screen: pygame.Surface, rect: pygame.Rect, grid_x: int, grid_y: int):
        """Add subtle falling leaves effect with fall-stop-disappear cycle"""
        import pygame
        import math

        current_time = pygame.time.get_ticks()

        # Use grid position for consistent but varied leaf timing
        phase_offset = (grid_x * 2.1 + grid_y * 1.7) * 0.8
        # Slower cycle for more realistic leaf behavior (8 second cycle)
        leaf_cycle = (current_time * 0.0003 + phase_offset) % (2 * math.pi)

        # Create a 3-phase cycle: fall (40%), rest (30%), disappear (30%)
        cycle_progress = (leaf_cycle / (2 * math.pi))  # 0 to 1

        if cycle_progress < 0.4:  # Falling phase (40% of cycle)
            # Leaf is falling
            fall_progress = cycle_progress / 0.4  # 0 to 1 during fall

            # Start from top, fall to bottom with gentle horizontal drift
            leaf_x = rect.x + rect.width // 3 + math.sin(leaf_cycle * 2) * 8
            leaf_y = rect.y + fall_progress * (rect.height - 5)

            # Draw falling leaf
            leaf_colors = [(139, 69, 19), (160, 82, 45),
                           (210, 180, 140)]  # Brown shades
            leaf_color = leaf_colors[(grid_x + grid_y) % len(leaf_colors)]
            pygame.draw.circle(screen, leaf_color,
                               (int(leaf_x), int(leaf_y)), 1)

        elif cycle_progress < 0.7:  # Resting phase (30% of cycle)
            # Leaf has landed and is resting on ground
            rest_progress = (cycle_progress - 0.4) / 0.3  # 0 to 1 during rest

            # Leaf sits at bottom of cell, maybe with slight movement
            leaf_x = rect.x + rect.width // 3 + math.sin(leaf_cycle * 2) * 8
            leaf_y = rect.y + rect.height - 5 + \
                math.sin(current_time * 0.002) * 1  # Slight ground movement

            # Gradually fade the leaf color as it rests
            fade_factor = 1.0 - (rest_progress * 0.3)  # Fade to 70% opacity
            leaf_colors = [(139, 69, 19), (160, 82, 45), (210, 180, 140)]
            base_color = leaf_colors[(grid_x + grid_y) % len(leaf_colors)]
            leaf_color = tuple(int(c * fade_factor) for c in base_color)
            pygame.draw.circle(screen, leaf_color,
                               (int(leaf_x), int(leaf_y)), 1)

        # Disappear phase (30% of cycle) - no drawing, leaf is gone

    def _add_subtle_sand_effect(self, screen: pygame.Surface, rect: pygame.Rect, grid_x: int, grid_y: int):
        """Add subtle sand effects with drifting particles and wind ripples"""
        import pygame
        import math

        current_time = pygame.time.get_ticks()

        # Use grid position for consistent but varied timing
        phase_offset = (grid_x * 1.7 + grid_y * 2.3) * 1.2
        sand_cycle = (current_time * 0.0005 + phase_offset) % (2 * math.pi)

        # Create a 2-phase cycle: wind ripples (60%), calm (40%)
        cycle_progress = (sand_cycle / (2 * math.pi))  # 0 to 1

        if cycle_progress < 0.6:  # Wind ripple phase (60% of cycle)
            # Show gentle sand ripples moving across the surface
            ripple_progress = cycle_progress / 0.6  # 0 to 1 during ripples

            # Create 2-3 horizontal ripple lines that move across the tile
            for i in range(3):
                ripple_y = rect.y + (rect.height // 4) + i * (rect.height // 4)
                ripple_x_offset = (ripple_progress *
                                   rect.width * 1.5) - (rect.width * 0.25)

                # Only draw ripple if it's within the tile bounds
                if 0 <= ripple_x_offset <= rect.width:
                    ripple_x = rect.x + ripple_x_offset

                    # Draw subtle sand ripple line
                    # Slightly lighter than sand
                    ripple_color = (220, 190, 160)
                    start_x = max(rect.x, ripple_x - 8)
                    end_x = min(rect.x + rect.width, ripple_x + 8)

                    if start_x < end_x:
                        pygame.draw.line(screen, ripple_color,
                                         (int(start_x), int(ripple_y)),
                                         (int(end_x), int(ripple_y)), 1)

            # Add occasional small dust particles
            if cycle_progress > 0.3 and cycle_progress < 0.5:  # Brief dust phase
                dust_progress = (cycle_progress - 0.3) / \
                    0.2  # 0 to 1 during dust

                # 1-2 tiny dust particles
                for i in range(2):
                    dust_x = rect.x + rect.width // 3 + i * (rect.width // 3)
                    dust_y = rect.y + rect.height - 3 - dust_progress * 8  # Rise up

                    # Fade out as dust rises
                    dust_alpha = int(255 * (1.0 - dust_progress))
                    dust_color = (200, 170, 140)  # Sandy dust color

                    # Draw tiny dust particle
                    if dust_alpha > 50:  # Only draw if visible enough
                        pygame.draw.circle(screen, dust_color,
                                           (int(dust_x), int(dust_y)), 1)

        # Calm phase (40% of cycle) - no effects, just base sand color

    def _add_subtle_rock_effect(self, screen: pygame.Surface, rect: pygame.Rect, grid_x: int, grid_y: int):
        """Add subtle rock effects with cracks and small stones"""
        import pygame
        import math

        current_time = pygame.time.get_ticks()

        # Use grid position for consistent but varied timing
        phase_offset = (grid_x * 2.7 + grid_y * 1.9) * 0.9
        rock_cycle = (current_time * 0.0002 + phase_offset) % (2 * math.pi)

        # Create a simple cycle: show details (30%), hide (70%)
        cycle_progress = (rock_cycle / (2 * math.pi))  # 0 to 1

        if cycle_progress < 0.3:  # Show rock details 30% of the time
            detail_progress = cycle_progress / 0.3  # 0 to 1 during detail phase

            # Add subtle rock cracks
            for i in range(2):  # 2 small cracks per rock tile
                crack_start_x = rect.x + \
                    (rect.width // 4) + i * (rect.width // 2)
                crack_start_y = rect.y + \
                    (rect.height // 3) + i * (rect.height // 3)

                # Small crack lines
                crack_length = 6 + math.sin(detail_progress * math.pi) * 2
                # Consistent crack direction per tile
                crack_angle = (grid_x + grid_y + i) * 0.8

                crack_end_x = crack_start_x + \
                    math.cos(crack_angle) * crack_length
                crack_end_y = crack_start_y + \
                    math.sin(crack_angle) * crack_length

                # Draw crack with darker gray
                crack_color = (70, 70, 70)  # Darker than base rock color
                pygame.draw.line(screen, crack_color,
                                 (int(crack_start_x), int(crack_start_y)),
                                 (int(crack_end_x), int(crack_end_y)), 1)

            # Add small stone details
            for i in range(3):  # 3 small stones per rock tile
                stone_x = rect.x + (rect.width // 6) + i * (rect.width // 3)
                stone_y = rect.y + (rect.height // 5) + i * (rect.height // 4)

                # Vary stone position slightly based on grid position
                stone_x += (grid_x * 3 + i) % 5 - 2
                stone_y += (grid_y * 2 + i) % 5 - 2

                # Ensure stones stay within tile bounds
                if rect.x <= stone_x <= rect.x + rect.width and rect.y <= stone_y <= rect.y + rect.height:
                    # Draw small stone as a tiny circle
                    stone_color = (130, 130, 130)  # Lighter than base rock
                    pygame.draw.circle(screen, stone_color,
                                       (int(stone_x), int(stone_y)), 1)

        # Rest of cycle (70%) - no effects, just base rock color

    def _add_subtle_pit_effect(self, screen: pygame.Surface, rect: pygame.Rect, grid_x: int, grid_y: int):
        """Add dangerous pit effects with glowing embers and ominous darkness"""
        import pygame
        import math

        current_time = pygame.time.get_ticks()

        # Create a consistent but varied effect based on grid position
        pit_seed = (grid_x * 7 + grid_y * 11) % 1000

        # Add dark center to make pit look deeper and more dangerous
        center_x = rect.x + rect.width // 2
        center_y = rect.y + rect.height // 2

        # Draw concentric dark circles to create depth effect
        for radius in range(3, min(rect.width, rect.height) // 2, 2):
            darkness_level = max(0, 64 - radius * 8)  # Darker towards center
            dark_color = (darkness_level // 4, darkness_level //
                          8, darkness_level // 16)
            pygame.draw.circle(screen, dark_color,
                               (center_x, center_y), radius, 1)

        # Add glowing embers/sparks that float up from the pit
        ember_cycle = 4000  # 4 second cycle for embers
        cycle_position = (current_time + pit_seed * 5) % ember_cycle

        # Create 2-3 embers per pit
        for i in range(3):
            ember_time_offset = (pit_seed + i * 333) % ember_cycle
            ember_progress = ((cycle_position + ember_time_offset) %
                              ember_cycle) / ember_cycle

            # Ember starts at bottom of pit and floats up
            start_x = center_x + ((pit_seed + i * 7) % 10) - 5
            start_y = rect.y + rect.height - 3

            # Float upward with slight horizontal drift
            ember_x = start_x + math.sin(ember_progress * math.pi * 2) * 3
            ember_y = start_y - (ember_progress * rect.height * 1.5)

            # Only draw if ember is within reasonable bounds
            if rect.y - rect.height <= ember_y <= rect.y + rect.height:
                # Ember color changes from bright orange to dim red as it rises
                brightness = max(0, 255 - int(ember_progress * 200))
                ember_color = (brightness, brightness // 3, 0)  # Orange to red

                # Draw ember as small glowing dot
                if brightness > 50:  # Only draw if bright enough
                    pygame.draw.circle(screen, ember_color,
                                       (int(ember_x), int(ember_y)), 1)

        # Add occasional red glow from the depths
        glow_cycle = 6000  # 6 second cycle
        glow_position = (current_time + pit_seed * 3) % glow_cycle

        if glow_position < glow_cycle * 0.2:  # 20% of time show glow
            glow_intensity = int((glow_position / (glow_cycle * 0.2)) * 60)
            glow_color = (glow_intensity, glow_intensity // 4, 0)  # Red glow

            # Draw subtle glow in center
            pygame.draw.circle(screen, glow_color, (center_x, center_y),
                               min(rect.width, rect.height) // 4, 0)

    def draw_path_overlay(self, screen: pygame.Surface):
        """Draw path overlay on top of terrain"""
        if len(self.path) > 1:
            # Draw thick path line
            pygame.draw.lines(screen, (139, 69, 19), False, self.path, 8)
            # Draw path border
            pygame.draw.lines(screen, (100, 50, 15), False, self.path, 12)

    def draw_tower_placement_preview(self, screen: pygame.Surface, mouse_pos: Tuple[int, int],
                                     existing_towers: List, tower_type: str = None):  # type: ignore
        """Draw tower placement preview with terrain-aware feedback and multi-block support"""
        x, y = mouse_pos
        grid_x, grid_y = self.pixel_to_grid(x, y)

        # Get placement info
        placement_info = self.get_placement_info(x, y, tower_type)
        valid_position = self.is_valid_tower_position_pixel(
            x, y, existing_towers, tower_type)

        # Choose color based on validity
        if valid_position:
            color = self.GREEN
        elif not placement_info['can_place']:
            color = self.RED
        elif not placement_info['tower_allowed']:
            color = self.YELLOW  # Tower type not allowed on this terrain
        else:
            color = self.RED

        # Draw pixel-based placement preview with edge-based positioning
        if tower_type:
            # Get tower grid visual size for placement preview (shows actual grid size)
            from .tower_sizes import get_tower_grid_visual_size
            tower_display_radius = get_tower_grid_visual_size(
                tower_type, self.cell_size)

            # Calculate tower center position for edge-based placement
            center_x, center_y = self._calculate_tower_center_from_edge_click(
                x, y, tower_type)

            # Draw tower preview circle at calculated center position (shows grid size)
            pygame.draw.circle(
                screen, color, (int(center_x), int(center_y)), tower_display_radius, 3)

            # Draw range preview if valid position
            if valid_position:
                # Get actual tower range values (corrected to match actual tower classes)
                tower_ranges = {
                    'basic': 80, 'sniper': 250, 'freezer': 80, 'detector': 300,
                    'antiair': 180, 'poison': 120, 'laser': 140, 'cannon': 250,
                    'lightning': 120, 'flame': 90, 'ice': 90, 'explosive': 300,
                    'missile': 400, 'splash': 80, 'destroyer': 500
                }
                base_range = tower_ranges.get(tower_type, 80)

                # Apply terrain effects to preview range
                preview_range = base_range
                center_grid_x, center_grid_y = self.pixel_to_grid(
                    center_x, center_y)
                terrain_type = self.get_terrain_at_grid(
                    center_grid_x, center_grid_y)
                special_rules = get_terrain_property(
                    terrain_type, 'special_rules')

                if special_rules == 'reduced_range':
                    # Forest terrain: reduce range by 20%
                    preview_range = int(base_range * 0.8)
                elif special_rules == 'water_only':
                    # Water terrain: increase range by 10% for water-compatible towers only
                    if tower_type in ['freezer', 'splash', 'destroyer']:
                        preview_range = int(base_range * 1.1)

                # Draw semi-transparent range circle centered on tower center
                range_surface = pygame.Surface(
                    (preview_range * 2, preview_range * 2), pygame.SRCALPHA)
                pygame.draw.circle(
                    range_surface, (*color[:3], 30), (preview_range, preview_range), preview_range)
                screen.blit(range_surface,
                            (int(center_x - preview_range), int(center_y - preview_range)))
        else:
            # Default preview for no tower type selected
            pygame.draw.circle(screen, color, (int(x), int(y)), 15, 3)

        # Draw terrain info tooltip
        if not valid_position:
            self._draw_terrain_tooltip(screen, mouse_pos, placement_info)

    def _draw_terrain_tooltip(self, screen: pygame.Surface, mouse_pos: Tuple[int, int],
                              placement_info: dict):
        """Draw tooltip showing why tower can't be placed"""
        font = pygame.font.Font(None, 24)
        x, y = mouse_pos

        # Create tooltip text
        if not placement_info['can_place']:
            text = f"{placement_info['terrain_name']} - Cannot place towers"
        elif not placement_info['tower_allowed']:
            text = f"{placement_info['terrain_name']} - Tower type not allowed"
        else:
            text = f"{placement_info['terrain_name']} - Too close to other towers"

        # Render text
        text_surface = font.render(text, True, (255, 255, 255))
        text_rect = text_surface.get_rect()

        # Position tooltip
        tooltip_x = min(x + 20, self.screen_width - text_rect.width - 10)
        tooltip_y = max(y - 30, 10)

        # Draw background
        bg_rect = pygame.Rect(tooltip_x - 5, tooltip_y - 5,
                              text_rect.width + 10, text_rect.height + 10)
        pygame.draw.rect(screen, (0, 0, 0), bg_rect)
        pygame.draw.rect(screen, (255, 255, 255), bg_rect, 2)

        # Draw text
        screen.blit(text_surface, (tooltip_x, tooltip_y))

    def draw(self, screen: pygame.Surface, placing_tower: bool = False,
             mouse_pos: Tuple[int, int] = None, existing_towers: List = None,
             tower_type: str = None, removing_rocks: bool = False, can_afford_removal: bool = True,
             placing_water: bool = False, placing_grass: bool = False):  # type: ignore
        """Draw the complete map"""
        # Draw terrain
        self.draw_terrain(screen)

        # Draw path overlay
        self.draw_path_overlay(screen)

        # Draw tower placement preview if placing tower
        if placing_tower and mouse_pos and existing_towers is not None:
            self.draw_tower_placement_preview(
                screen, mouse_pos, existing_towers, tower_type)

        # Draw rock removal preview if in rock removal mode
        if removing_rocks and mouse_pos:
            self.draw_rock_removal_preview(
                screen, mouse_pos, can_afford_removal)

        # Draw terrain placement preview if in placement mode
        if (placing_water or placing_grass) and mouse_pos:
            self.draw_terrain_placement_preview(
                screen, mouse_pos, placing_water, placing_grass)

    def has_rock_at_position(self, pixel_x: int, pixel_y: int) -> bool:
        """Check if there's a rock at the given pixel position"""
        grid_x, grid_y = self.pixel_to_grid(pixel_x, pixel_y)
        return self.has_rock_at_grid(grid_x, grid_y)

    def has_rock_at_grid(self, grid_x: int, grid_y: int) -> bool:
        """Check if there's a rock at the given grid position"""
        if 0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height:
            return self.grid_layout[grid_y][grid_x] == ROCK
        return False

    def remove_rock_at_position(self, pixel_x: int, pixel_y: int) -> bool:
        """Remove a rock at the given pixel position. Returns True if successful."""
        grid_x, grid_y = self.pixel_to_grid(pixel_x, pixel_y)
        return self.remove_rock_at_grid(grid_x, grid_y)

    def remove_rock_at_grid(self, grid_x: int, grid_y: int) -> bool:
        """Remove a rock at the given grid position. Returns True if successful."""
        if self.has_rock_at_grid(grid_x, grid_y):
            # Convert rock to pit terrain
            self.grid_layout[grid_y][grid_x] = PIT
            return True
        return False

    def has_pit_at_position(self, pixel_x: int, pixel_y: int) -> bool:
        """Check if there's a pit at the given pixel position"""
        grid_x, grid_y = self.pixel_to_grid(pixel_x, pixel_y)
        return self.has_pit_at_grid(grid_x, grid_y)

    def has_pit_at_grid(self, grid_x: int, grid_y: int) -> bool:
        """Check if there's a pit at the given grid position"""
        if 0 <= grid_x < self.grid_width and 0 <= grid_y < self.grid_height:
            return self.grid_layout[grid_y][grid_x] == PIT
        return False

    def place_water_at_position(self, pixel_x: int, pixel_y: int) -> bool:
        """Place water at the given pixel position. Returns True if successful."""
        grid_x, grid_y = self.pixel_to_grid(pixel_x, pixel_y)
        return self.place_water_at_grid(grid_x, grid_y)

    def place_water_at_grid(self, grid_x: int, grid_y: int) -> bool:
        """Place water at the given grid position. Returns True if successful."""
        if self.has_pit_at_grid(grid_x, grid_y):
            # Convert pit to water terrain
            self.grid_layout[grid_y][grid_x] = WATER
            return True
        return False

    def place_grass_at_position(self, pixel_x: int, pixel_y: int) -> bool:
        """Place grass at the given pixel position. Returns True if successful."""
        grid_x, grid_y = self.pixel_to_grid(pixel_x, pixel_y)
        return self.place_grass_at_grid(grid_x, grid_y)

    def place_grass_at_grid(self, grid_x: int, grid_y: int) -> bool:
        """Place grass at the given grid position. Returns True if successful."""
        if self.has_pit_at_grid(grid_x, grid_y):
            # Convert pit to grass terrain
            self.grid_layout[grid_y][grid_x] = GRASS
            return True
        return False

    def calculate_rock_removal_cost(self, difficulty_level: int = 50, current_wave: int = 1,
                                    ai_base_cost: int = None, ai_scaling_factor: float = None) -> int:
        """Calculate the cost to remove a rock based on AI-determined difficulty and wave progression"""
        # AI-determined base cost (hefty amounts for strategic decisions)
        if ai_base_cost is None:
            # Much higher base costs - AI determines this
            base_cost = 150 + (difficulty_level * 3)  # 150-450 base cost range
        else:
            base_cost = ai_base_cost

        # AI-determined wave scaling factor
        if ai_scaling_factor is None:
            # Default aggressive scaling - gets very expensive in late game
            # 1.0 to 1.8 scaling per wave
            wave_scaling = 1.0 + ((difficulty_level / 100.0) * 0.8)
        else:
            wave_scaling = ai_scaling_factor

        # Wave progression multiplier - exponential growth for late game
        wave_multiplier = 1.0 + (current_wave - 1) * wave_scaling

        # Difficulty multiplier for overall game challenge
        difficulty_multiplier = 1.0 + \
            (difficulty_level / 100.0) * 1.5  # 1.0 to 2.5 multiplier

        # Calculate final hefty cost
        final_cost = int(base_cost * wave_multiplier * difficulty_multiplier)

        # Ensure meaningful costs - minimum $100, can go into thousands for late game
        return max(100, min(10000, final_cost))

    def draw_rock_removal_preview(self, screen: pygame.Surface, mouse_pos: Tuple[int, int],
                                  can_afford: bool = True):
        """Draw rock removal preview with visual feedback"""
        x, y = mouse_pos
        grid_x, grid_y = self.pixel_to_grid(x, y)

        # Check if there's a rock at this position
        if self.has_rock_at_grid(grid_x, grid_y):
            # Choose color based on affordability
            color = self.GREEN if can_afford else self.RED

            # Draw removal preview
            cell_rect = pygame.Rect(
                self.map_offset_x + grid_x * self.cell_size,
                self.map_offset_y + grid_y * self.cell_size,
                self.cell_size,
                self.cell_size
            )
            pygame.draw.rect(screen, color, cell_rect, 4)

            # Draw "X" symbol to indicate removal
            center_x = self.map_offset_x + grid_x * self.cell_size + self.cell_size // 2
            center_y = self.map_offset_y + grid_y * self.cell_size + self.cell_size // 2
            size = self.cell_size // 4

            # Draw X lines
            pygame.draw.line(screen, color,
                             (center_x - size, center_y - size),
                             (center_x + size, center_y + size), 4)
            pygame.draw.line(screen, color,
                             (center_x + size, center_y - size),
                             (center_x - size, center_y + size), 4)
        else:
            # No rock here - draw red circle to indicate invalid target
            center_x, center_y = self.grid_to_pixel(grid_x, grid_y)
            pygame.draw.circle(screen, self.RED, (center_x, center_y), 15, 3)

    def draw_terrain_placement_preview(self, screen: pygame.Surface, mouse_pos: Tuple[int, int],
                                       placing_water: bool = False, placing_grass: bool = False):
        """Draw terrain placement preview with visual feedback"""
        x, y = mouse_pos
        grid_x, grid_y = self.pixel_to_grid(x, y)

        # Check if there's a pit at this position
        if self.has_pit_at_grid(grid_x, grid_y):
            # Choose color based on placement type
            if placing_water:
                color = (30, 144, 255)  # Water blue
                symbol_color = (255, 255, 255)  # White
            elif placing_grass:
                color = (34, 139, 34)  # Grass green
                symbol_color = (255, 255, 255)  # White
            else:
                color = (128, 128, 128)  # Gray fallback
                symbol_color = (255, 255, 255)

            # Draw placement preview with selection box
            cell_rect = pygame.Rect(
                self.map_offset_x + grid_x * self.cell_size,
                self.map_offset_y + grid_y * self.cell_size,
                self.cell_size,
                self.cell_size
            )

            # Draw selection box around the pit
            selection_rect = pygame.Rect(
                cell_rect.x - 3, cell_rect.y - 3,
                cell_rect.width + 6, cell_rect.height + 6
            )
            # Yellow selection box
            pygame.draw.rect(screen, (255, 255, 0), selection_rect, 4)

            # Draw preview of the terrain color
            pygame.draw.rect(screen, color, cell_rect, 3)

            # Draw placement symbol in center
            center_x = self.map_offset_x + grid_x * self.cell_size + self.cell_size // 2
            center_y = self.map_offset_y + grid_y * self.cell_size + self.cell_size // 2

            if placing_water:
                # Draw water droplet symbol
                pygame.draw.circle(screen, symbol_color,
                                   (center_x, center_y), 8, 2)
                pygame.draw.circle(screen, symbol_color,
                                   (center_x, center_y), 4, 0)
            elif placing_grass:
                # Draw grass symbol (small lines)
                for i in range(3):
                    offset_x = (i - 1) * 6
                    pygame.draw.line(screen, symbol_color,
                                     (center_x + offset_x, center_y + 8),
                                     (center_x + offset_x, center_y - 8), 2)
        else:
            # No pit here - draw red circle to indicate invalid target
            center_x, center_y = self.grid_to_pixel(grid_x, grid_y)
            pygame.draw.circle(screen, self.RED, (center_x, center_y), 15, 3)
