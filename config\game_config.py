"""
Game Configuration Loader

This module loads game configuration from multiple sources:
- Static configuration files (towers, balance) that don't change between games
- Dynamic configuration files (waves, maps) generated by AI-guided systems for each level

This separation allows the AI to focus on generating strategic content while 
maintaining consistent game mechanics.
"""

import json
import os
from typing import Dict, Any, List, Tuple

# Cache for loaded configurations
_config_cache = None
_static_tower_cache = None
_static_balance_cache = None
_current_config_file = 'tower_defense_game.json'  # Default dynamic config file

def set_config_file(filename: str):
    """Set which config file to use and reload"""
    global _config_cache, _current_config_file
    _current_config_file = filename
    _config_cache = None  # Force reload

def get_current_config_file() -> str:
    """Get the currently selected config file name"""
    return _current_config_file

def get_current_config_path() -> str:
    """Get the full path to the currently selected config file"""
    if os.path.isabs(_current_config_file):
        return _current_config_file
    elif _current_config_file.startswith('config/'):
        # Path is relative to project root, not config directory
        project_root = os.path.dirname(os.path.dirname(__file__))
        return os.path.join(project_root, _current_config_file)
    else:
        return os.path.join(os.path.dirname(__file__), _current_config_file)

def list_available_configs() -> List[str]:
    """List all available config files in the config directory"""
    config_dir = os.path.dirname(__file__)
    configs = []
    
    # Exclude utility/static config files that shouldn't be selectable
    excluded_files = {
        'static_tower_config.json',   # Static tower configuration
        'static_balance_config.json'  # Static balance configuration
    }
    
    for file in os.listdir(config_dir):
        if file.endswith('.json') and file not in excluded_files:
            configs.append(file)
    return sorted(configs)

def _load_config() -> Dict[str, Any]:
    """Load configuration from JSON file with caching"""
    global _config_cache
    
    if _config_cache is None:
        # Handle both absolute paths and relative paths from project root
        if os.path.isabs(_current_config_file):
            config_path = _current_config_file
        elif _current_config_file.startswith('config/'):
            # Path is relative to project root, not config directory
            project_root = os.path.dirname(os.path.dirname(__file__))
            config_path = os.path.join(project_root, _current_config_file)
        else:
            # Just a filename, assume it's in the config directory
            config_path = os.path.join(os.path.dirname(__file__), _current_config_file)
        
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            # Process wave configurations
            if 'wave_config' in config:
                wave_config = config['wave_config']
                
                # Process round progression wave ranges
                if 'round_progression' in wave_config:
                    for section_name, section_data in wave_config['round_progression'].items():
                        if isinstance(section_data, dict) and 'wave_ranges' in section_data:
                            processed_ranges = {}
                            for wave_range, value in section_data['wave_ranges'].items():
                                if '-' in wave_range:
                                    start, end = map(int, wave_range.split('-'))
                                    processed_ranges[(start, end)] = value
                                else:
                                    processed_ranges[int(wave_range)] = value
                            section_data['wave_ranges'] = processed_ranges
                
                # Convert string keys to integers for special rounds and boss waves
                if 'round_progression' in wave_config and 'special_rounds' in wave_config['round_progression']:
                    special_rounds = {}
                    for wave_str, data in wave_config['round_progression']['special_rounds'].items():
                        special_rounds[int(wave_str)] = data
                    wave_config['round_progression']['special_rounds'] = special_rounds
                
                if 'boss_waves' in wave_config:
                    wave_config['boss_waves'] = {
                        int(k): v for k, v in wave_config['boss_waves'].items()
                    }
                
                # Process wave compositions
                if 'wave_compositions' in wave_config:
                    processed_compositions = {}
                    for wave_range, composition in wave_config['wave_compositions'].items():
                        if '-' in wave_range:
                            start, end = map(int, wave_range.split('-'))
                            processed_compositions[(start, end)] = composition
                        else:
                            processed_compositions[int(wave_range)] = composition
                    wave_config['wave_compositions'] = processed_compositions
            
            # Process map configurations
            if 'map_config' in config:
                for map_name, map_data in config['map_config'].items():
                    if 'path' in map_data:
                        # Convert path coordinates to tuples for compatibility
                        map_data['path'] = [tuple(coord) for coord in map_data['path']]
            
            _config_cache = config
            
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")
    
    return _config_cache

def get_wave_config() -> Dict[str, Any]:
    """Get wave configuration settings"""
    config = _load_config()
    return config.get('wave_config', {})

def get_map_config() -> Dict[str, Any]:
    """Get map configuration settings"""
    config = _load_config()
    return config.get('map_config', {})

def get_tower_config() -> Dict[str, Any]:
    """Get tower configuration settings from static config file"""
    return _load_static_tower_config()

def get_balance_config() -> Dict[str, Any]:
    """Get balance configuration settings from static config file"""
    return _load_static_balance_config()

def get_game_config() -> Dict[str, Any]:
    """Get game configuration settings"""
    config = _load_config()
    return config.get('game_config', {})

def get_available_maps() -> List[str]:
    """Get list of available map names"""
    map_config = get_map_config()
    return list(map_config.keys())

def get_calculated_difficulty() -> Dict[str, Any]:
    """Get the calculated difficulty score and analysis"""
    config = _load_config()
    return config.get('calculated_difficulty', {})

def get_difficulty_score() -> int:
    """Get just the difficulty score as an integer (0-100)"""
    config = _load_config()
    
    # Check multiple locations for difficulty
    # 1. Check calculated_difficulty.score (existing behavior)
    if 'calculated_difficulty' in config:
        calc_diff = config['calculated_difficulty']
        if isinstance(calc_diff, dict) and 'score' in calc_diff:
            return calc_diff['score']
        elif isinstance(calc_diff, (int, float)):
            return int(calc_diff)
    
    # 2. Check game_config.difficulty
    if 'game_config' in config and 'difficulty' in config['game_config']:
        game_config = config['game_config']
        difficulty_value = game_config['difficulty']
        
        # Convert difficulty level names to scores
        if isinstance(difficulty_value, str):
            difficulty_map = {
                'test': 1,
                'tutorial': 10,
                'easy': 20,
                'casual': 25,
                'normal': 50,
                'medium': 60,
                'hard': 70,
                'very_hard': 85,
                'nightmare': 95,
                'impossible': 100
            }
            return difficulty_map.get(difficulty_value.lower(), 50)
        elif isinstance(difficulty_value, (int, float)):
            return int(difficulty_value)
    
    # 3. Check root level difficulty
    if 'difficulty' in config:
        diff_data = config['difficulty']
        if isinstance(diff_data, dict):
            # Check for score in difficulty object
            if 'score' in diff_data:
                return diff_data['score']
            elif 'level' in diff_data:
                level = diff_data['level']
                if isinstance(level, str):
                    difficulty_map = {
                        'test': 1,
                        'tutorial': 10,
                        'easy': 20,
                        'casual': 25,
                        'normal': 50,
                        'medium': 60,
                        'hard': 70,
                        'very_hard': 85,
                        'nightmare': 95,
                        'impossible': 100
                    }
                    return difficulty_map.get(level.lower(), 50)
                elif isinstance(level, (int, float)):
                    return int(level)
        elif isinstance(diff_data, (int, float)):
            return int(diff_data)
        elif isinstance(diff_data, str):
            difficulty_map = {
                'test': 1,
                'tutorial': 10,
                'easy': 20,
                'casual': 25,
                'normal': 50,
                'medium': 60,
                'hard': 70,
                'very_hard': 85,
                'nightmare': 95,
                'impossible': 100
            }
            return difficulty_map.get(diff_data.lower(), 50)
    
    # Default fallback
    return 50

def get_level_metadata() -> Dict[str, Any]:
    """Get level metadata including description and rewards"""
    config = _load_config()
    
    # Return level metadata if present
    if 'level_metadata' in config:
        return config['level_metadata']
    
    # Generate default metadata from config data
    difficulty_score = get_difficulty_score()
    
    # Calculate victory points using the same formula as GlobalUpgradeSystem
    if difficulty_score <= 1:
        victory_points = 1
    elif difficulty_score >= 100:
        victory_points = 15
    else:
        victory_points = 1 + int((difficulty_score - 1) * (14 / 99))
    
    # Generate basic metadata
    difficulty_name = "Unknown"
    if difficulty_score <= 10:
        difficulty_name = "Very Easy"
    elif difficulty_score <= 30:
        difficulty_name = "Easy"
    elif difficulty_score <= 50:
        difficulty_name = "Normal"
    elif difficulty_score <= 70:
        difficulty_name = "Hard"
    elif difficulty_score <= 85:
        difficulty_name = "Very Hard"
    else:
        difficulty_name = "Nightmare"
    
    return {
        "name": f"Level {difficulty_score}",
        "description": f"A {difficulty_name.lower()} challenge that will test your strategic skills. Prepare your defenses and adapt your tactics to overcome the incoming waves of enemies.",
        "difficulty_rating": difficulty_name,
        "estimated_duration": "30-45 minutes",
        "recommended_for": "all players",
        "special_features": [
            f"Difficulty rating: {difficulty_score}/100",
            "Dynamic enemy scaling",
            "Strategic tower placement required"
        ],
        "victory_rewards": {
            "victory_points": victory_points,
            "victory_points_description": f"Earn {victory_points} Victory Points for completing this level",
            "unlock_requirements": [],
            "completion_bonuses": [
                "Tower upgrade points",
                "Strategic experience",
                "Achievement progress"
            ]
        },
        "tips": [
            "Study enemy types and their weaknesses",
            "Balance offense and economy",
            "Adapt your strategy as waves progress",
            "Use terrain to your advantage"
        ]
    }

def get_level_name() -> str:
    """Get the display name for the current level"""
    metadata = get_level_metadata()
    return metadata.get('name', 'Unknown Level')

def get_level_description() -> str:
    """Get the description for the current level"""
    metadata = get_level_metadata()
    return metadata.get('description', 'A challenging tower defense level awaits.')

def get_level_rewards() -> Dict[str, Any]:
    """Get reward information for the current level"""
    metadata = get_level_metadata()
    return metadata.get('victory_rewards', {
        'victory_points': 1,
        'victory_points_description': 'Earn Victory Points for completing this level',
        'unlock_requirements': [],
        'completion_bonuses': ['Tower upgrade points']
    })

def get_level_tips() -> List[str]:
    """Get tips for the current level"""
    metadata = get_level_metadata()
    return metadata.get('tips', [
        'Study enemy types and their weaknesses',
        'Balance offense and economy',
        'Adapt your strategy as waves progress'
    ])

# For backward compatibility and easy access
def _load_static_tower_config() -> Dict[str, Any]:
    """Load static tower configuration with caching"""
    global _static_tower_cache
    
    if _static_tower_cache is None:
        config_path = os.path.join(os.path.dirname(__file__), 'static', 'static_tower_config.json')
        
        try:
            with open(config_path, 'r') as f:
                _static_tower_cache = json.load(f)
        except FileNotFoundError:
            print(f"⚠️ Static tower config not found: {config_path}")
            print("🔄 Falling back to dynamic config for tower settings")
            # Fallback to dynamic config if static doesn't exist
            dynamic_config = _load_config()
            return dynamic_config.get('tower_config', {})
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in static tower config: {e}")
    
    return _static_tower_cache

def _load_static_balance_config() -> Dict[str, Any]:
    """Load static balance configuration with caching"""
    global _static_balance_cache
    
    if _static_balance_cache is None:
        config_path = os.path.join(os.path.dirname(__file__), 'static', 'static_balance_config.json')
        
        try:
            with open(config_path, 'r') as f:
                _static_balance_cache = json.load(f)
        except FileNotFoundError:
            print(f"⚠️ Static balance config not found: {config_path}")
            print("🔄 Falling back to dynamic config for balance settings")
            # Fallback to dynamic config if static doesn't exist
            dynamic_config = _load_config()
            return dynamic_config.get('balance_config', {})
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in static balance config: {e}")
    
    return _static_balance_cache

def reload_config():
    """Force reload of all configurations from files"""
    global _config_cache, _static_tower_cache, _static_balance_cache
    _config_cache = None
    _static_tower_cache = None
    _static_balance_cache = None
    _load_config() 