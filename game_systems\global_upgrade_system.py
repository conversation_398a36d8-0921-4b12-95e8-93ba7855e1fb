import json
import os
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from enum import Enum


class UpgradeType(Enum):
    DAMAGE = "damage"
    RANGE = "range"
    FIRE_RATE = "fire_rate"
    PROJECTILE_SPEED = "projectile_speed"


# Upgrade limits to prevent towers from becoming too powerful
UPGRADE_LIMITS = {
    UpgradeType.DAMAGE: 10,        # Max +10 damage
    UpgradeType.RANGE: 8,          # Max +40 range
    UpgradeType.FIRE_RATE: 6,      # Max +12 fire rate (lower is faster)
    UpgradeType.PROJECTILE_SPEED: 5  # Max +0.5 projectile speed
}


@dataclass
class TowerUpgrade:
    """Represents a single tower upgrade"""
    tower_type: str
    upgrade_type: UpgradeType
    level: int
    cost: int
    effect_value: float
    description: str


@dataclass
class GlobalUpgradeData:
    """Global upgrade data for a specific tower type"""
    tower_type: str
    damage_level: int = 0
    range_level: int = 0
    fire_rate_level: int = 0
    projectile_speed_level: int = 0

    def get_total_level(self) -> int:
        """Get total upgrade level for this tower"""
        return self.damage_level + self.range_level + self.fire_rate_level + self.projectile_speed_level


@dataclass
class TowerUpgradeData:
    """Upgrade data for a specific tower instance"""
    tower_id: str
    tower_type: str
    damage_level: int = 0
    range_level: int = 0
    fire_rate_level: int = 0
    projectile_speed_level: int = 0

    def get_total_level(self) -> int:
        """Get total upgrade level for this tower"""
        return self.damage_level + self.range_level + self.fire_rate_level + self.projectile_speed_level


class GlobalUpgradeSystem:
    """Manages global tower upgrades that persist between games (per tower type)"""

    def __init__(self, save_file_path: str = "global_upgrades/global_upgrades.json"):
        self.save_file_path = save_file_path
        self.points = 0
        self.total_points_earned = 0
        self.games_won = 0
        # Terrain currency system
        self.terrain_currency = 0
        self.total_terrain_currency_earned = 0
        # XP and Leveling system
        self.player_level = 1
        self.current_xp = 0
        self.total_xp_earned = 0
        # Dynamic XP requirements - no longer fixed
        # Dynamic terrain currency - scales from 25 to 200 based on level
        self.tower_types = [
            'basic', 'sniper', 'freezer', 'detector', 'antiair', 'poison',
            'laser', 'cannon', 'lightning', 'flame', 'ice', 'explosive',
            'missile', 'splash', 'destroyer'
        ]
        self.upgrades: Dict[str, GlobalUpgradeData] = {
            tt: GlobalUpgradeData(tt) for tt in self.tower_types}
        self.load_data()

    def calculate_victory_points(self, difficulty: int) -> int:
        """Calculate points earned from victory based on difficulty"""
        # Base formula: 15 points for difficulty 100, 1 point for difficulty 1
        # Linear interpolation between these points
        if difficulty <= 1:
            return 1
        elif difficulty >= 100:
            return 15
        else:
            # Linear interpolation: 1 + (difficulty - 1) * (14 / 99)
            return 1 + int((difficulty - 1) * (14 / 99))

    def award_victory_points(self, difficulty: int) -> int:
        """Award points for winning a game and save data"""
        points_earned = self.calculate_victory_points(difficulty)
        self.points += points_earned
        self.total_points_earned += points_earned
        self.games_won += 1
        self.save_data()
        return points_earned

    def get_enemy_xp_values(self) -> Dict[str, int]:
        """Get XP values for each enemy type based on difficulty rating"""
        # Base XP values based on enemy difficulty (1-10 scale from difficulty_factors.py)
        enemy_difficulties = {
            "BasicEnemy": 1,
            "FastEnemy": 2,
            "TankEnemy": 3,
            "FlyingEnemy": 4,
            "ShieldedEnemy": 5,
            "InvisibleEnemy": 6,
            "ArmoredEnemy": 4,
            "RegeneratingEnemy": 5,
            "TeleportingEnemy": 6,
            "SplittingEnemy": 7,
            "FireElementalEnemy": 6,
            "ToxicEnemy": 5,
            "PhaseShiftEnemy": 7,
            "BlastProofEnemy": 6,
            "SpectralEnemy": 8,
            "CrystallineEnemy": 8,
            "ToxicMutantEnemy": 7,
            "VoidEnemy": 8,
            "AdaptiveEnemy": 9,
            "SpeedBoss": 6,
            "MegaBoss": 7,
            "NecromancerBoss": 8,
            "TimeLordBoss": 8,
            "ShadowKing": 9,
            "CrystalOverlord": 10
        }

        # Convert difficulty ratings to XP values
        # Adjusted scaling to match new progression requirements
        # Base XP = difficulty * 1.0 (increased from 0.25 to provide more granular XP rewards)
        xp_values = {}
        for enemy_type, difficulty in enemy_difficulties.items():
            # Use floating point values, will be multiplied by difficulty multiplier later
            xp_values[enemy_type] = difficulty * 1.0

        return xp_values

    def get_xp_required_for_level(self, level: int) -> int:
        """Calculate XP required to reach a specific level

        Level 1: Should be achievable with 0.5 difficulty 1 completions
        Level 40: Should require 10 difficulty 100 completions
        Level 40+: Same as level 40

        Args:
            level: The target level (1-based)

        Returns:
            XP required to reach that level from the previous level
        """
        if level <= 1:
            # Level 1 should require 0.5 difficulty 1 completions
            # Difficulty 1 gives ~29 XP, so level 1 should need ~15 XP
            return 15  # Base XP for level 1

        # Calculate realistic XP requirements based on actual expected XP from testing
        # From test results:
        # Difficulty 1: ~29 XP per completion
        # Difficulty 100: ~107484 XP per completion

        difficulty_1_xp = 29  # Actual expected XP from difficulty 1 completion
        difficulty_100_xp = 107484  # Actual expected XP from difficulty 100 completion

        if level <= 40:
            # Level 1: 0.5 difficulty 1 completions = 29 / 2 = ~15 XP
            # Level 40: 10 difficulty 100 completions = 107484 * 10 = ~1074840 XP

            # 0.5 completions means XP = difficulty_1_xp / 2
            level_1_requirement = int(difficulty_1_xp / 2)
            level_40_requirement = int(
                difficulty_100_xp * 10)  # 10 completions needed

            # Exponential scaling from level 1 to 40
            # Using exponential formula: base * (growth_rate ^ (level - 1))
            growth_rate = (level_40_requirement /
                           level_1_requirement) ** (1.0 / 39.0)

            xp_required = int(level_1_requirement *
                              (growth_rate ** (level - 1)))
            return xp_required
        else:
            # Level 40+ uses the same requirement as level 40
            return self.get_xp_required_for_level(40)

    def get_terrain_currency_for_level(self, level: int) -> int:
        """Calculate terrain currency reward for reaching a specific level

        Scales from 25 currency at level 1 to 200 currency at level 40+

        Args:
            level: The level being reached

        Returns:
            Terrain currency reward for that level
        """
        if level <= 1:
            return 25  # Base currency for level 1

        if level <= 40:
            # Linear scaling from 25 to 200 over levels 1-40
            # Formula: 25 + (level - 1) * (175 / 39)
            currency_increase_per_level = 175 / 39  # (200 - 25) / (40 - 1)
            currency = 25 + (level - 1) * currency_increase_per_level
            return int(currency)
        else:
            # Level 40+ gets the maximum currency
            return 200

    def calculate_expected_xp_from_difficulty(self, difficulty: int, player_level: int = 1) -> int:
        """Calculate expected XP gain from completing a level of given difficulty

        This is used to estimate XP rewards and balance the progression system.

        Args:
            difficulty: Game difficulty (1-100)
            player_level: Current player level (affects XP requirements)

        Returns:
            Estimated XP that should be gained from completing this difficulty
        """
        # Realistic calculations based on actual wave and enemy scaling

        # Wave count calculation (from adaptive_config_generator.py)
        # Formula: base_waves = 5 + (difficulty * 0.8) + (difficulty^1.15 * 0.03)
        linear_component = difficulty * 0.8
        exponential_component = (difficulty ** 1.15) * 0.03
        total_waves = max(
            5, int(round(5 + linear_component + exponential_component)))

        # Enemy count per wave scales from 5 to 25
        base_enemies_per_wave = 5 + (20 * (difficulty / 100.0))

        # Wave progression increases enemy count over time
        # Early waves: base count, later waves: much higher
        # Average across all waves is roughly 1.5x base for difficulty 1, 2.5x base for difficulty 100
        wave_progression_factor = 1.5 + (difficulty / 100.0) * 1.0

        estimated_enemies = int(
            base_enemies_per_wave * wave_progression_factor * total_waves)

        # Average enemy difficulty rating of ~5 (mix of basic and advanced enemies)
        avg_enemy_difficulty = 5.0
        base_xp_per_enemy = avg_enemy_difficulty * 1.0  # From get_enemy_xp_values

        # Calculate difficulty multiplier (same as in game.py)
        difficulty_multiplier = 0.1 + (difficulty / 100.0) * 1.9

        # Estimate average wave multiplier (2% per wave, average across all waves)
        avg_wave_number = total_waves / 2.0
        avg_wave_multiplier = 1.0 + (avg_wave_number - 1) * 0.02

        total_multiplier = difficulty_multiplier * avg_wave_multiplier
        estimated_total_xp = int(
            estimated_enemies * base_xp_per_enemy * total_multiplier)

        return estimated_total_xp

    def award_xp(self, enemy_type: str, difficulty_multiplier: float = 1.0) -> tuple[int, bool]:
        """Award XP for killing an enemy and check for level up

        Args:
            enemy_type: The class name of the enemy killed
            difficulty_multiplier: Multiplier based on game difficulty and wave scaling

        Returns:
            tuple: (xp_awarded, level_up_occurred)
        """
        xp_values = self.get_enemy_xp_values()
        # Default to 2 XP if enemy type not found
        base_xp = xp_values.get(enemy_type, 2)

        # Apply difficulty multiplier
        # Ensure at least 1 XP
        xp_awarded = max(1, int(base_xp * difficulty_multiplier))

        # Add XP
        self.current_xp += xp_awarded
        self.total_xp_earned += xp_awarded

        # Check for level up
        level_up_occurred = False
        levels_gained = 0

        # Use dynamic XP requirements per level
        while True:
            next_level = self.player_level + 1
            xp_needed_for_next_level = self.get_xp_required_for_level(
                next_level)

            if self.current_xp >= xp_needed_for_next_level:
                self.current_xp -= xp_needed_for_next_level
                self.player_level += 1
                levels_gained += 1
                level_up_occurred = True

                # Award terrain currency for level up (dynamic based on level)
                currency_reward = self.get_terrain_currency_for_level(
                    self.player_level)
                self.terrain_currency += currency_reward
                self.total_terrain_currency_earned += currency_reward
            else:
                break

        if level_up_occurred:
            self.save_data()

        return xp_awarded, level_up_occurred

    def get_xp_progress(self) -> Dict[str, int]:
        """Get current XP progress information"""
        next_level = self.player_level + 1
        xp_for_next_level = self.get_xp_required_for_level(next_level)
        xp_to_next_level = xp_for_next_level - self.current_xp
        progress_percentage = int(
            (self.current_xp / xp_for_next_level) * 100) if xp_for_next_level > 0 else 0

        return {
            'current_level': self.player_level,
            'current_xp': self.current_xp,
            'xp_per_level': xp_for_next_level,  # Dynamic XP requirement for next level
            'xp_to_next_level': xp_to_next_level,
            'total_xp_earned': self.total_xp_earned,
            'progress_percentage': progress_percentage
        }

    def get_upgrade_cost(self, tower_type: str, upgrade_type: UpgradeType) -> int:
        """Calculate cost for next upgrade level for a specific tower"""
        current_level = self.get_upgrade_level(tower_type, upgrade_type)
        base_cost = 5  # Base cost for first upgrade

        # Exponential cost scaling: base_cost * (1.5 ^ current_level)
        return int(base_cost * (1.5 ** current_level))

    def get_upgrade_level(self, tower_type: str, upgrade_type: UpgradeType) -> int:
        """Get current upgrade level for a specific tower and upgrade type"""
        if tower_type not in self.upgrades:
            return 0

        upgrade_data = self.upgrades[tower_type]
        if upgrade_type == UpgradeType.DAMAGE:
            return upgrade_data.damage_level
        elif upgrade_type == UpgradeType.RANGE:
            return upgrade_data.range_level
        elif upgrade_type == UpgradeType.FIRE_RATE:
            return upgrade_data.fire_rate_level
        elif upgrade_type == UpgradeType.PROJECTILE_SPEED:
            return upgrade_data.projectile_speed_level
        return 0

    def get_upgrade_effect(self, tower_type: str, upgrade_type: UpgradeType) -> float:
        """Get the current effect value for an upgrade"""
        level = self.get_upgrade_level(tower_type, upgrade_type)

        # Effect scaling per level - much smaller effects
        if upgrade_type == UpgradeType.DAMAGE:
            return level * 1  # +1 damage per level
        elif upgrade_type == UpgradeType.RANGE:
            return level * 5  # +5 range per level
        elif upgrade_type == UpgradeType.FIRE_RATE:
            return level * 2  # +2 fire rate per level (lower is faster)
        elif upgrade_type == UpgradeType.PROJECTILE_SPEED:
            return level * 0.1  # +0.1 projectile speed per level
        return 0.0

    def can_afford_upgrade(self, tower_type: str, upgrade_type: UpgradeType) -> bool:
        """Check if player can afford an upgrade for a specific tower"""
        cost = self.get_upgrade_cost(tower_type, upgrade_type)
        return self.points >= cost

    def is_upgrade_at_limit(self, tower_type: str, upgrade_type: UpgradeType) -> bool:
        """Check if an upgrade has reached its maximum level"""
        current_level = self.get_upgrade_level(tower_type, upgrade_type)
        max_level = UPGRADE_LIMITS.get(upgrade_type, 0)
        return current_level >= max_level

    def can_purchase_upgrade(self, tower_type: str, upgrade_type: UpgradeType) -> bool:
        """Check if player can purchase an upgrade (affordable and not at limit)"""
        return self.can_afford_upgrade(tower_type, upgrade_type) and not self.is_upgrade_at_limit(tower_type, upgrade_type)

    def purchase_upgrade(self, tower_type: str, upgrade_type: UpgradeType) -> bool:
        """Purchase an upgrade for a specific tower if affordable and not at limit"""
        if not self.can_purchase_upgrade(tower_type, upgrade_type):
            return False

        cost = self.get_upgrade_cost(tower_type, upgrade_type)
        self.points -= cost

        # Apply the upgrade
        upgrade_data = self.upgrades[tower_type]
        if upgrade_type == UpgradeType.DAMAGE:
            upgrade_data.damage_level += 1
        elif upgrade_type == UpgradeType.RANGE:
            upgrade_data.range_level += 1
        elif upgrade_type == UpgradeType.FIRE_RATE:
            upgrade_data.fire_rate_level += 1
        elif upgrade_type == UpgradeType.PROJECTILE_SPEED:
            upgrade_data.projectile_speed_level += 1

        self.save_data()
        return True

    def can_afford_terrain_placement(self, terrain_type: str) -> bool:
        """Check if player can afford terrain placement"""
        cost = self.get_terrain_placement_cost(terrain_type)
        return self.terrain_currency >= cost

    def get_terrain_placement_cost(self, terrain_type: str) -> int:
        """Get cost for placing terrain"""
        if terrain_type == 'grass':
            return 2
        elif terrain_type == 'water':
            return 1
        return 0

    def spend_terrain_currency(self, terrain_type: str) -> bool:
        """Spend terrain currency for placement. Returns True if successful."""
        if not self.can_afford_terrain_placement(terrain_type):
            return False

        cost = self.get_terrain_placement_cost(terrain_type)
        self.terrain_currency -= cost
        self.save_data()
        return True

    def get_tower_upgrade_info(self, tower_type: str) -> Dict[str, Dict]:
        """Get comprehensive upgrade information for a specific tower"""
        upgrade_data = self.upgrades[tower_type]
        info = {}

        for upgrade_type in UpgradeType:
            level = self.get_upgrade_level(tower_type, upgrade_type)
            cost = self.get_upgrade_cost(tower_type, upgrade_type)
            effect = self.get_upgrade_effect(tower_type, upgrade_type)
            can_afford = self.can_afford_upgrade(tower_type, upgrade_type)
            is_at_limit = self.is_upgrade_at_limit(tower_type, upgrade_type)
            can_purchase = self.can_purchase_upgrade(tower_type, upgrade_type)
            max_level = UPGRADE_LIMITS.get(upgrade_type, 0)

            info[upgrade_type.value] = {
                'level': level,
                'max_level': max_level,
                'cost': cost,
                'effect': effect,
                'can_afford': can_afford,
                'is_at_limit': is_at_limit,
                'can_purchase': can_purchase,
                'description': self.get_upgrade_description(tower_type, upgrade_type, level, effect)
            }

        return info

    def get_upgrade_description(self, tower_type: str, upgrade_type: UpgradeType, level: int, effect: float) -> str:
        """Generate description for an upgrade"""
        max_level = UPGRADE_LIMITS.get(upgrade_type, 0)
        limit_text = f" (MAX)" if level >= max_level else f" (Level {level}/{max_level})"

        if upgrade_type == UpgradeType.DAMAGE:
            return f"Damage +{int(effect)}{limit_text}"
        elif upgrade_type == UpgradeType.RANGE:
            return f"Range +{int(effect)}{limit_text}"
        elif upgrade_type == UpgradeType.FIRE_RATE:
            return f"Fire Rate +{int(effect)}{limit_text}"
        elif upgrade_type == UpgradeType.PROJECTILE_SPEED:
            return f"Projectile Speed +{effect:.1f}{limit_text}"
        return f"Unknown Upgrade{limit_text}"

    def apply_upgrades_to_tower(self, tower) -> None:
        """Apply upgrades to a specific tower instance"""
        if not hasattr(tower, 'tower_type') or tower.tower_type not in self.upgrades:
            return

        upgrade_data = self.upgrades[tower.tower_type]

        # Apply damage upgrade
        if upgrade_data.damage_level > 0:
            damage_bonus = self.get_upgrade_effect(
                tower.tower_type, UpgradeType.DAMAGE)
            tower.damage += int(damage_bonus)

        # Apply range upgrade
        if upgrade_data.range_level > 0:
            range_bonus = self.get_upgrade_effect(
                tower.tower_type, UpgradeType.RANGE)
            tower.range += int(range_bonus)

        # Apply fire rate upgrade (lower is faster)
        if upgrade_data.fire_rate_level > 0:
            fire_rate_bonus = self.get_upgrade_effect(
                tower.tower_type, UpgradeType.FIRE_RATE)
            tower.fire_rate = max(10, tower.fire_rate -
                                  int(fire_rate_bonus))  # Don't go below 10

        # Apply projectile speed upgrade
        if upgrade_data.projectile_speed_level > 0:
            speed_bonus = self.get_upgrade_effect(
                tower.tower_type, UpgradeType.PROJECTILE_SPEED)
            tower.projectile_speed += speed_bonus

    def get_statistics(self) -> Dict:
        """Get player statistics"""
        total_upgrades = sum(data.get_total_level()
                             for data in self.upgrades.values())

        return {
            'points': self.points,
            'total_points_earned': self.total_points_earned,
            'games_won': self.games_won,
            'total_upgrades_purchased': total_upgrades,
            'upgrades_by_tower': {tower_type: data.get_total_level() for tower_type, data in self.upgrades.items()}
        }

    def save_data(self) -> None:
        """Save upgrade data to file"""
        data = {
            'points': self.points,
            'total_points_earned': self.total_points_earned,
            'games_won': self.games_won,
            'terrain_currency': self.terrain_currency,
            'total_terrain_currency_earned': self.total_terrain_currency_earned,
            # XP and Leveling system data
            'player_level': self.player_level,
            'current_xp': self.current_xp,
            'total_xp_earned': self.total_xp_earned,
            # xp_per_level and terrain_currency_per_level are now dynamic, no longer saved
            'upgrades': {tower_type: asdict(upgrade_data) for tower_type, upgrade_data in self.upgrades.items()}
        }

        try:
            # Ensure the directory exists
            import os
            os.makedirs(os.path.dirname(self.save_file_path), exist_ok=True)

            with open(self.save_file_path, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            print(f"Failed to save global upgrade data: {e}")

    def load_data(self) -> None:
        """Load upgrade data from file"""
        if not os.path.exists(self.save_file_path):
            return

        try:
            with open(self.save_file_path, 'r') as f:
                data = json.load(f)

            self.points = data.get('points', 0)
            self.total_points_earned = data.get('total_points_earned', 0)
            self.games_won = data.get('games_won', 0)
            self.terrain_currency = data.get('terrain_currency', 0)
            self.total_terrain_currency_earned = data.get(
                'total_terrain_currency_earned', 0)

            # Load XP and Leveling system data
            self.player_level = data.get('player_level', 1)
            self.current_xp = data.get('current_xp', 0)
            self.total_xp_earned = data.get('total_xp_earned', 0)
            # xp_per_level and terrain_currency_per_level are now dynamic, no longer loaded from save

            # Migrate old per-instance data if present
            upgrades_data = data.get('upgrades', {})
            if upgrades_data and isinstance(list(upgrades_data.values())[0], dict) and 'tower_type' in list(upgrades_data.values())[0]:
                # Per-type data
                for tower_type, upgrade_data in upgrades_data.items():
                    if tower_type in self.tower_types:
                        self.upgrades[tower_type] = GlobalUpgradeData(
                            tower_type=tower_type,
                            damage_level=upgrade_data.get('damage_level', 0),
                            range_level=upgrade_data.get('range_level', 0),
                            fire_rate_level=upgrade_data.get(
                                'fire_rate_level', 0),
                            projectile_speed_level=upgrade_data.get(
                                'projectile_speed_level', 0)
                        )
            else:
                # Per-instance data: migrate to per-type by taking max for each type
                per_type = {tt: GlobalUpgradeData(
                    tt) for tt in self.tower_types}
                for instance_data in upgrades_data.values():
                    tt = instance_data.get('tower_type')
                    if tt in per_type:
                        per_type[tt].damage_level = max(
                            per_type[tt].damage_level, instance_data.get('damage_level', 0))
                        per_type[tt].range_level = max(
                            per_type[tt].range_level, instance_data.get('range_level', 0))
                        per_type[tt].fire_rate_level = max(
                            per_type[tt].fire_rate_level, instance_data.get('fire_rate_level', 0))
                        per_type[tt].projectile_speed_level = max(
                            per_type[tt].projectile_speed_level, instance_data.get('projectile_speed_level', 0))
                self.upgrades = per_type
        except Exception as e:
            print(f"Failed to load global upgrade data: {e}")

    def reset_data(self) -> None:
        """Reset all upgrade data (for testing or fresh start)"""
        self.points = 0
        self.total_points_earned = 0
        self.games_won = 0
        self.upgrades = {tt: GlobalUpgradeData(tt) for tt in self.tower_types}
        self.save_data()

    def get_all_upgraded_towers(self) -> List[Dict]:
        """Get list of all upgraded towers for main menu display"""
        upgraded_towers = []
        for tower_type, data in self.upgrades.items():
            upgraded_towers.append({
                'tower_type': tower_type,
                'tower_id': tower_type,
                'total_level': data.get_total_level(),
                'damage_level': data.damage_level,
                'range_level': data.range_level,
                'fire_rate_level': data.fire_rate_level,
                'projectile_speed_level': data.projectile_speed_level
            })

        # Sort by total upgrade level (highest first)
        upgraded_towers.sort(key=lambda x: x['total_level'], reverse=True)
        return upgraded_towers

    def get_tower_type_summary(self) -> Dict[str, Dict]:
        """Get summary of upgrades by tower type for main menu"""
        summary = {}
        for tower_type in self.tower_types:
            data = self.upgrades[tower_type]
            summary[tower_type] = {
                'total_upgrades': data.get_total_level(),
                'towers_count': 1 if data.get_total_level() > 0 else 0,
                'avg_level': data.get_total_level()
            }

        return summary

    def create_tower_from_menu(self, tower_type: str = 'basic') -> str:
        """Create a new tower entry from the main menu for upgrading"""
        import time

        # Generate unique tower ID
        tower_id = f"{tower_type}_{int(time.time())}"

        # Create tower upgrade data
        self.upgrades[tower_type] = GlobalUpgradeData(
            tower_type=tower_type
        )

        # Save the data
        self.save_data()

        return tower_id
