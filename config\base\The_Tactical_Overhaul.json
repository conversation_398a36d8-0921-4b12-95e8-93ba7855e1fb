{"game_config": {"starting_money": 200, "starting_lives": 18}, "progression_config": {"starting_money": 200, "starting_lives": 18, "economic_scaling": "automatic"}, "tower_config": {"cost_progression": {"early_game_waves": 15, "mid_game_waves": 30, "early_increase_per_wave": 0.024, "mid_increase_per_wave": 0.045, "late_increase_per_wave": 0.09000000000000001, "max_cost_multiplier": 4.5}, "dynamic_cost_increase": {"per_tower_built_multiplier": 0.15, "max_per_tower_multiplier": 20}}, "enemy_buffs": {"max_health_modifier": 1.1, "max_speed_modifier": 1.0, "global_damage_modifier": 1.0, "special_abilities_enabled": true}, "wave_config": {"total_waves": 54, "spawn_config": {"base_enemy_count": 4, "base_spawn_delay": 147, "min_spawn_delay": 44, "boss_enemy_count": 1}, "round_progression": {"enemy_increase_per_round": {"wave_ranges": {"1-5": 1, "6-10": 2, "11-15": 3, "16-20": 4, "21-25": 5, "26-30": 6, "31-35": 7, "36-40": 8, "41-45": 9, "46-50": 10, "51-55": 11, "56-60": 12, "61-65": 13, "66-70": 14, "71-75": 15, "76-80": 16}, "default": 1}, "spawn_delay_reduction_per_round": {"wave_ranges": {"1-10": 4, "11-20": 7, "21-30": 9, "31-40": 10, "41-50": 13, "51-60": 16, "61-70": 18, "71-80": 19}, "default": 4}, "special_rounds": {"10": {"enemy_multiplier": 2.0, "spawn_delay_multiplier": 0.7000000000000001}, "20": {"enemy_multiplier": 2.5, "spawn_delay_multiplier": 0.6000000000000001}, "30": {"enemy_multiplier": 3.0, "spawn_delay_multiplier": 0.5}, "40": {"enemy_multiplier": 3.5, "spawn_delay_multiplier": 0.4}, "50": {"enemy_multiplier": 4.0, "spawn_delay_multiplier": 0.30000000000000004}, "60": {"enemy_multiplier": 4.5, "spawn_delay_multiplier": 0.2}}, "enemy_health_scaling": {}, "enemy_speed_scaling": {}, "global_health_multiplier": 0.8, "global_speed_multiplier": 0.9}, "wave_compositions": {"1-5": [["BasicEnemy", 0.8], ["FastEnemy", 0.13999999999999999]], "6-10": [["AdaptiveEnemy", 0.35000000000000003], ["VoidEnemy", 0.35000000000000003], ["BasicEnemy", 0.1], ["FastEnemy", 0.1], ["TankEnemy", 0.1]], "11-15": [["AdaptiveEnemy", 0.2916666666666667], ["VoidEnemy", 0.2916666666666667], ["BasicEnemy", 0.08333333333333333], ["FastEnemy", 0.08333333333333333], ["TankEnemy", 0.08333333333333333], ["FlyingEnemy", 0.16666666666666669]], "16-20": [["AdaptiveEnemy", 0.21875], ["VoidEnemy", 0.21875], ["BasicEnemy", 0.0625], ["FastEnemy", 0.0625], ["TankEnemy", 0.0625], ["FlyingEnemy", 0.12500000000000003], ["ShieldedEnemy", 0.12500000000000003], ["ArmoredEnemy", 0.12500000000000003]], "21-30": [["AdaptiveEnemy", 0.175], ["VoidEnemy", 0.175], ["BasicEnemy", 0.049999999999999996], ["FastEnemy", 0.049999999999999996], ["TankEnemy", 0.049999999999999996], ["FlyingEnemy", 0.08333333333333333], ["ShieldedEnemy", 0.08333333333333333], ["ArmoredEnemy", 0.08333333333333333], ["InvisibleEnemy", 0.08333333333333333], ["EnergyShieldEnemy", 0.08333333333333333], ["GroundedEnemy", 0.08333333333333333]], "31-40": [["AdaptiveEnemy", 0.2249999999999999], ["VoidEnemy", 0.2249999999999999], ["BasicEnemy", 0.06666666666666664], ["FastEnemy", 0.06666666666666664], ["TankEnemy", 0.06666666666666664], ["FlyingEnemy", 0.03499999999999998], ["ShieldedEnemy", 0.03499999999999998], ["ArmoredEnemy", 0.03499999999999998], ["InvisibleEnemy", 0.03499999999999998], ["EnergyShieldEnemy", 0.03499999999999998], ["GroundedEnemy", 0.03499999999999998], ["RegeneratingEnemy", 0.03499999999999998], ["TeleportingEnemy", 0.03499999999999998], ["FireElementalEnemy", 0.03499999999999998], ["ToxicEnemy", 0.03499999999999998]], "41-50": [["AdaptiveEnemy", 0.22499999999999995], ["VoidEnemy", 0.22499999999999995], ["BasicEnemy", 0.06666666666666665], ["FastEnemy", 0.06666666666666665], ["TankEnemy", 0.06666666666666665], ["FlyingEnemy", 0.026923076923076914], ["ShieldedEnemy", 0.026923076923076914], ["ArmoredEnemy", 0.026923076923076914], ["InvisibleEnemy", 0.026923076923076914], ["EnergyShieldEnemy", 0.026923076923076914], ["GroundedEnemy", 0.026923076923076914], ["RegeneratingEnemy", 0.026923076923076914], ["TeleportingEnemy", 0.026923076923076914], ["FireElementalEnemy", 0.026923076923076914], ["ToxicEnemy", 0.026923076923076914], ["SplittingEnemy", 0.026923076923076914], ["PhaseShiftEnemy", 0.026923076923076914], ["BlastProofEnemy", 0.026923076923076914]], "51-60": [["AdaptiveEnemy", 0.22500000000000006], ["VoidEnemy", 0.22500000000000006], ["BasicEnemy", 0.06666666666666668], ["FastEnemy", 0.06666666666666668], ["TankEnemy", 0.06666666666666668], ["FlyingEnemy", 0.023333333333333334], ["ShieldedEnemy", 0.023333333333333334], ["ArmoredEnemy", 0.023333333333333334], ["InvisibleEnemy", 0.023333333333333334], ["EnergyShieldEnemy", 0.023333333333333334], ["GroundedEnemy", 0.023333333333333334], ["RegeneratingEnemy", 0.023333333333333334], ["TeleportingEnemy", 0.023333333333333334], ["FireElementalEnemy", 0.023333333333333334], ["ToxicEnemy", 0.023333333333333334], ["SplittingEnemy", 0.023333333333333334], ["PhaseShiftEnemy", 0.023333333333333334], ["BlastProofEnemy", 0.023333333333333334], ["SpectralEnemy", 0.023333333333333334], ["CrystallineEnemy", 0.023333333333333334]], "61": [["AdaptiveEnemy", 0.34374999999999994], ["VoidEnemy", 0.34374999999999994], ["BasicEnemy", 0.10416666666666664], ["FastEnemy", 0.10416666666666664], ["TankEnemy", 0.10416666666666664]]}, "boss_waves": {"9": "SpeedBoss", "18": "SpeedBoss", "27": "SpeedBoss", "36": "SpeedBoss", "45": "SpeedBoss", "54": "MegaBoss", "63": "MegaBoss", "72": "MegaBoss", "80": "TimeLordBoss"}, "enemy_scaling": {"health_per_wave": 0.5445, "speed_per_wave": 0.0825, "reward_per_wave": 0.132, "size_per_wave": 0.02, "max_health_multiplier": 54.0, "max_speed_multiplier": 5.0, "max_reward_multiplier": 25.0, "max_size_multiplier": 2.0, "damage_scaling_per_wave": 0.1, "max_damage_multiplier": 4.0}, "money_config": {"normal_wave_bonus": 57, "boss_wave_bonus": 240}, "boss_counts_per_wave": {"9": 1, "18": 1, "27": 1, "36": 1, "45": 1, "54": 1, "63": 1, "72": 1, "80": 2}}, "map_config": {"default_map": {"width": 20, "height": 15, "terrain": [[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], [2, 2, 0, 3, 0, 0, 4, 2, 0, 0, 3, 0, 0, 0, 4, 4, 3, 0, 0, 2], [2, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 3, 0, 2], [2, 0, 0, 2, 0, 0, 0, 0, 3, 4, 3, 0, 3, 0, 0, 2, 0, 0, 2, 2], [2, 0, 0, 0, 3, 4, 4, 0, 0, 0, 0, 0, 0, 0, 0, 4, 2, 3, 0, 2], [2, 0, 0, 0, 4, 0, 1, 0, 0, 0, 0, 3, 2, 0, 3, 0, 0, 0, 0, 2], [2, 4, 0, 4, 0, 0, 4, 3, 1, 0, 0, 0, 1, 0, 3, 4, 0, 1, 0, 2], [2, 1, 4, 0, 0, 4, 0, 4, 0, 0, 2, 0, 2, 0, 1, 0, 2, 0, 1, 2], [2, 0, 0, 1, 0, 4, 0, 2, 0, 2, 2, 1, 0, 4, 0, 2, 1, 2, 0, 2], [2, 2, 3, 0, 0, 1, 0, 0, 4, 1, 0, 0, 4, 1, 3, 2, 4, 0, 0, 2], [2, 0, 0, 1, 0, 0, 4, 3, 0, 2, 4, 2, 2, 3, 1, 0, 3, 0, 0, 2], [2, 0, 0, 0, 2, 0, 4, 1, 4, 4, 2, 1, 1, 0, 0, 3, 3, 2, 0, 2], [2, 0, 2, 2, 1, 4, 3, 4, 0, 1, 1, 3, 2, 0, 0, 2, 2, 0, 2, 2], [2, 0, 0, 0, 0, 0, 1, 0, 1, 2, 0, 4, 0, 0, 0, 0, 4, 0, 2, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "path": [[1, 7], [3, 8], [6, 5], [8, 6], [9, 9], [11, 8], [12, 6], [14, 7], [13, 9], [11, 11], [9, 12], [7, 11], [5, 9], [3, 10], [4, 12], [6, 13], [8, 13], [10, 12], [12, 11], [14, 10], [16, 8], [17, 6], [18, 7]]}}, "_generation_metadata": {"difficulty": 62, "difficulty_factors": {"difficulty": 62, "normalized_difficulty": 0.65, "base_count": 18.0, "base_delay": 61.5, "complexity": 0.7549999999999999, "buildable_space": 0.5075000000000001, "strategic_terrain_density": 0.49, "health_scale": 0.1775, "speed_scale": 0.0625, "obstacle_density": 0.49249999999999994}, "generation_timestamp": null, "algorithm_version": "1.0", "generation_method": "modular_ai_full", "modular_components": {"map_structure": "ai", "framework": "ai_guided_procedural", "economic_system": "ai", "wave_progression": "ai", "terrain_strategy": "ai", "enemy_strategy": "ai", "theme_and_naming": "ai"}, "creation_type": "🧩 MODULAR AI MAP GENERATION", "reliability": "high"}, "_ai_map_strategy": {"path_strategy": "winding", "path_length_target": 40, "path_complexity": 0.5, "terrain_strategy": "balanced", "buildable_space_target": 0.6, "strategic_focus": "adaptation", "layout_reasoning": "The player's declining performance trend and preference for basic strategies suggest a need for adaptation through moderate complexity. A winding path with balanced terrain offers strategic tower placement opportunities, encouraging the player to diversify tactics while maintaining a manageable challenge level. The buildable space allows for strategic planning without overwhelming constraints, fostering growth beyond basic strategies."}, "_ai_metadata": {"economic_focus": "economy_boost", "economic_reasoning": "The player did not win, indicating a struggle at higher difficulty. Therefore, an economic boost is applied to assist progression. Adjustments include increased starting money, wave bonuses, and enemy reward scaling, while controlling tower cost progression to prevent overpowering the player. Enemy spawn rate adjustments target reward efficiency to enhance strategic balance."}, "_original_economic_values": {"starting_money": 200, "normal_wave_bonus": 57, "boss_wave_bonus": 240, "reward_per_wave": 0.132, "max_reward_multiplier": 25.0}, "_ai_wave_strategy": {"total_waves_modifier": 0.9, "enemy_health_scaling_modifier": 1.1, "enemy_speed_scaling_modifier": 1.0, "enemy_reward_scaling_modifier": 1.0, "enemy_size_scaling_modifier": 1.0, "enemy_damage_scaling_modifier": 1.0, "max_health_multiplier_modifier": 0.9, "max_speed_multiplier_modifier": 1.0, "max_reward_multiplier_modifier": 1.0, "max_size_multiplier_modifier": 1.0, "max_damage_multiplier_modifier": 1.0, "enemy_max_health_modifier": 1.1, "enemy_max_speed_modifier": 1.0, "min_spawn_delay_modifier": 1.0, "base_spawn_delay_modifier": 1.0, "spawn_delay_reduction_modifier": 0.9, "boss_wave_frequency_modifier": 1.0, "difficulty_modifier": 0.95, "wave_progression_reasoning": "Due to the player's loss but high score at difficulty 50, a small wave reduction is applied to aid in achieving success. Enemy health scaling is slightly increased to maintain challenge while other factors remain moderate to provide a balanced difficulty increase towards difficulty 70. Boss wave frequency and overall difficulty are slightly decreased to counterbalance the higher enemy health scaling."}, "starting_cash": 600, "cash_per_wave": 60, "_ai_terrain_strategy": {"path_type": "normal_path", "terrain_reasoning": "The player's moderate competence suggests a need for balanced challenge. The normal path will maintain consistent engagement, providing clear strategic points for tower placement without overwhelming complexity. This configuration will allow the player to utilize their tower diversity effectively across the map, fostering strategic depth and encouraging adaptation without the introduction of special mechanics that could increase difficulty unpredictably."}, "_ai_enemy_strategy": {"primary_counter_enemies": ["AdaptiveEnemy", "VoidEnemy"], "strategy_focus": "Advanced counter-strategies to challenge skilled player", "extreme_spawn_preference": "late_game_focus", "spawn_distribution_style": "polarized_extreme"}, "enemy_buff_config": {"description": "AI-generated buff configuration based on general performance", "enabled": true, "scenario_type": "adaptive", "buff_intensity": "low", "custom_spawn_rates": {"wave_ranges": {"1-10": {"base_chance": 0.025, "max_buffs": 1, "allowed_buffs": ["speed_boost", "armor"]}, "11-20": {"base_chance": 0.075, "max_buffs": 2, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration"]}, "21-30": {"base_chance": 0.125, "max_buffs": 3, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive"]}, "31-40": {"base_chance": 0.175, "max_buffs": 4, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance"]}, "41-50": {"base_chance": 0.225, "max_buffs": 5, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "berserker"]}, "51+": {"base_chance": 0.3, "max_buffs": 2, "allowed_buffs": ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive", "spell_resistance", "berserker"]}}, "boss_multipliers": {"mini_boss": 1.0, "boss": 1.5, "super_boss": 2.0}}, "featured_combinations": ["stealth_assassin", "flying_fortress"], "buff_metrics_tracking": {"track_buff_effectiveness": true, "track_tower_counters": true, "track_player_adaptation": true}, "generation_metadata": {"generated_from": "general_performance", "intensity_reasoning": "Based on 77.03333333333333% score and loss", "fallback_config": true}}, "level_name": "The Tactical Overhaul", "level_description": "Prepare for an intricate web of counter-strategies designed to test your advanced tactical prowess and drive performance beyond previous thresholds.", "_adaptive_metadata": {"ai_adjustments": {"difficulty_adjustment": {"new_difficulty": 62, "change": -3, "reasoning": "Initial difficulty based on performance score 77.0%"}, "map_structure_adjustments": {"path_strategy": "winding", "path_length_target": 40, "path_complexity": 0.5, "terrain_strategy": "balanced", "buildable_space_target": 0.6, "strategic_focus": "adaptation", "layout_reasoning": "The player's declining performance trend and preference for basic strategies suggest a need for adaptation through moderate complexity. A winding path with balanced terrain offers strategic tower placement opportunities, encouraging the player to diversify tactics while maintaining a manageable challenge level. The buildable space allows for strategic planning without overwhelming constraints, fostering growth beyond basic strategies."}, "economic_adjustments": {"starting_money_modifier": 1.2, "normal_wave_bonus_modifier": 1.15, "boss_wave_bonus_modifier": 1.2, "enemy_reward_scaling_modifier": 1.1, "max_reward_multiplier_modifier": 1.25, "tower_cost_progression": {"early_increase_per_wave_modifier": 1.2, "mid_increase_per_wave_modifier": 1.5, "late_increase_per_wave_modifier": 1.8, "max_cost_multiplier_modifier": 1.5}, "enemy_spawn_rate_adjustments": {"FastEnemy": 0.7, "SpectralEnemy": 2.0, "VoidEnemy": 0.5, "AdaptiveEnemy": 2.5}, "strategic_focus": "economy_boost", "reasoning": "The player did not win, indicating a struggle at higher difficulty. Therefore, an economic boost is applied to assist progression. Adjustments include increased starting money, wave bonuses, and enemy reward scaling, while controlling tower cost progression to prevent overpowering the player. Enemy spawn rate adjustments target reward efficiency to enhance strategic balance."}, "wave_adjustments": {"total_waves_modifier": 0.9, "enemy_health_scaling_modifier": 1.1, "enemy_speed_scaling_modifier": 1.0, "enemy_reward_scaling_modifier": 1.0, "enemy_size_scaling_modifier": 1.0, "enemy_damage_scaling_modifier": 1.0, "max_health_multiplier_modifier": 0.9, "max_speed_multiplier_modifier": 1.0, "max_reward_multiplier_modifier": 1.0, "max_size_multiplier_modifier": 1.0, "max_damage_multiplier_modifier": 1.0, "enemy_max_health_modifier": 1.1, "enemy_max_speed_modifier": 1.0, "min_spawn_delay_modifier": 1.0, "base_spawn_delay_modifier": 1.0, "spawn_delay_reduction_modifier": 0.9, "boss_wave_frequency_modifier": 1.0, "difficulty_modifier": 0.95, "wave_progression_reasoning": "Due to the player's loss but high score at difficulty 50, a small wave reduction is applied to aid in achieving success. Enemy health scaling is slightly increased to maintain challenge while other factors remain moderate to provide a balanced difficulty increase towards difficulty 70. Boss wave frequency and overall difficulty are slightly decreased to counterbalance the higher enemy health scaling."}, "terrain_adjustments": {"path_type": "normal_path", "terrain_reasoning": "The player's moderate competence suggests a need for balanced challenge. The normal path will maintain consistent engagement, providing clear strategic points for tower placement without overwhelming complexity. This configuration will allow the player to utilize their tower diversity effectively across the map, fostering strategic depth and encouraging adaptation without the introduction of special mechanics that could increase difficulty unpredictably."}, "enemy_adjustments": {"primary_counter_enemies": ["AdaptiveEnemy", "VoidEnemy"], "strategy_focus": "Advanced counter-strategies to challenge skilled player", "extreme_spawn_preference": "late_game_focus", "spawn_distribution_style": "polarized_extreme"}, "reasoning": "Complete AI-driven generation: Map structure, economic system, wave progression, terrain, enemies, and theming all designed by AI for performance score 77.0% (Final difficulty: 62)", "boss_configuration": {"boss_waves": {"9": "SpeedBoss", "18": "SpeedBoss", "27": "SpeedBoss", "36": "SpeedBoss", "45": "SpeedBoss", "54": "MegaBoss", "63": "MegaBoss", "72": "MegaBoss", "80": "TimeLordBoss"}, "boss_counts": {"9": 1, "18": 1, "27": 1, "36": 1, "45": 1, "54": 1, "63": 1, "72": 1, "80": 2}, "boss_intensity": "minimal", "boss_frequency_modifier": 1.5, "reasoning": "Boss strategy: minimal intensity based on 77.03333333333333% score and loss"}, "dynamic_parameters": {"spawn_config": {"base_enemy_count": 4, "enemy_count_increment": 1, "base_spawn_delay": 147, "min_spawn_delay": 44}, "economic_config": {"starting_cash": 600, "cash_per_wave": 60, "economic_tightness": "very_generous"}, "enemy_scaling": {"health_multiplier": 0.8, "speed_multiplier": 0.9, "scaling_intensity": "reduced"}, "reasoning": "Dynamic scaling: reduced intensity, very_generous economy based on 77.03333333333333% score and loss"}, "buff_system_adjustments": {"enabled": true, "intensity": "low", "reasoning": "AI-generated buff configuration"}}, "generation_timestamp": "2025-07-18T20:22:46.326895", "generation_method": "modular_ai_multi_game", "creation_type": "🧩 MODULAR AI GENERATION", "multi_game_context": {"games_analyzed": 5, "avg_score": 77.03333333333333, "win_rate": 60.0, "trend": "declining", "difficulty_progression": [50, 80, 1, 1, 1], "strategic_patterns": {"tower_effectiveness": {"laser": {"win_rate": 0.0, "avg_usage": 2.5, "games_used": 2}, "ice": {"win_rate": 0.0, "avg_usage": 10.0, "games_used": 2}, "detector": {"win_rate": 0.0, "avg_usage": 2.0, "games_used": 2}, "poison": {"win_rate": 0.0, "avg_usage": 2.5, "games_used": 2}, "antiair": {"win_rate": 0.0, "avg_usage": 5.0, "games_used": 2}, "flame": {"win_rate": 0.0, "avg_usage": 4.0, "games_used": 2}, "splash": {"win_rate": 0.0, "avg_usage": 2.0, "games_used": 2}, "lightning": {"win_rate": 0.0, "avg_usage": 10.5, "games_used": 2}, "explosive": {"win_rate": 0.0, "avg_usage": 2.0, "games_used": 1}, "basic": {"win_rate": 75.0, "avg_usage": 2.0, "games_used": 4}, "freezer": {"win_rate": 0.0, "avg_usage": 2.0, "games_used": 1}, "cannon": {"win_rate": 0.0, "avg_usage": 4.0, "games_used": 1}}, "most_preferred_strategy": "basic", "strategy_consistency": 0.6, "economic_patterns": {"avg_economic_efficiency": 11.875601379183896, "avg_resource_management": 43.66746643295913, "economic_trend": "improving"}, "tower_diversity_trend": [9, 11, 1, 1, 1], "games_analyzed": 5}, "performance_trends": {"score_trend": "strongly_declining", "win_rate": 60.0, "avg_score": 77.03333333333333, "avg_wave_progression": 100.0, "problem_areas": [], "strengths": ["high_scores", "strong_progression"], "consistency": 0.42000000000000004, "recent_performance": {"last_3_scores": [42.0, 44.0, 100], "last_3_wins": [false, false, true], "improvement_rate": -11.6}}, "performance_summaries": [{"score": 42.0, "win_flag": false, "lives_remaining": 0, "starting_lives": 17, "towers_built": {"laser": 3, "ice": 5, "detector": 2, "poison": 2, "antiair": 7, "flame": 1, "splash": 2, "lightning": 9, "explosive": 2}, "tower_diversity": 9, "wave_reached": 16, "final_wave": 16, "economic_efficiency": 1.4688558769872295, "resource_management_score": 40.840872804683336, "most_built_tower_type": "lightning", "config_difficulty_score": 70, "previous_config_details": {}}, {"score": 44.0, "win_flag": false, "lives_remaining": 0, "starting_lives": 20, "towers_built": {"basic": 2, "freezer": 2, "detector": 2, "antiair": 3, "flame": 7, "poison": 3, "ice": 15, "lightning": 12, "splash": 2, "cannon": 4, "laser": 2}, "tower_diversity": 11, "wave_reached": 17, "final_wave": 17, "economic_efficiency": 1.3948653046465378, "resource_management_score": 43.00933125972006, "most_built_tower_type": "ice", "config_difficulty_score": 1, "previous_config_details": {}}, {"score": 100, "win_flag": true, "lives_remaining": 24, "starting_lives": 24, "towers_built": {"basic": 1}, "tower_diversity": 1, "wave_reached": 4, "final_wave": 4, "economic_efficiency": 25.8, "resource_management_score": 42.321083172147006, "most_built_tower_type": "basic", "config_difficulty_score": null, "previous_config_details": {}}, {"score": 99.16666666666667, "win_flag": true, "lives_remaining": 23, "starting_lives": 24, "towers_built": {"basic": 4}, "tower_diversity": 1, "wave_reached": 4, "final_wave": 4, "economic_efficiency": 5.214285714285714, "resource_management_score": 49.817708333333336, "most_built_tower_type": "basic", "config_difficulty_score": null, "previous_config_details": {}}, {"score": 100, "win_flag": true, "lives_remaining": 24, "starting_lives": 24, "towers_built": {"basic": 1}, "tower_diversity": 1, "wave_reached": 4, "final_wave": 4, "economic_efficiency": 25.5, "resource_management_score": 42.34833659491194, "most_built_tower_type": "basic", "config_difficulty_score": null, "previous_config_details": {}}]}}, "_analytical_balancing": {"applied": true, "problematic_waves": 4, "waves_analyzed": 20, "tower_catalog_size": 14, "path_length": 52.84543294639616, "enemy_speed": 1.0}}