import json
import os
import random
import sys
import time
from datetime import datetime
from typing import List, <PERSON><PERSON>, Dict, Any

import pygame

from enemies import Enemy
from towers import Tower
from projectiles import Projectile
from game_systems import Map, WaveManager, UIManager, TowerManager
from game_systems.tower_upgrade_system import TowerUpgradeSystem
from game_systems.upgrade_ui import UpgradeUI
from game_systems.rock_removal_rewards import RockRemovalRewards
from game_systems.spatial_partitioning import SpatialGrid, PerformanceOptimizer
from game_systems.performance_monitor import PerformanceMonitor, SectionTimer
from game_systems.global_upgrade_system import GlobalUpgradeSystem
from game_systems.global_upgrade_ui import GlobalUpgradeUI
from ai.performance_analysis import PerformanceData


class Game:
    """Main game controller - coordinates between all game systems"""

    def __init__(self, skip_config_selection=False, launched_from_menu=False):
        pygame.init()

        # Track if game was launched from the main menu launcher
        self.launched_from_menu = launched_from_menu

        # Let player choose config before starting (unless called from launcher)
        if not skip_config_selection:
            self.choose_game_config()

        # Load game configuration
        from config.game_config import _load_config, get_current_config_path
        self.full_config = _load_config()  # Load the entire config
        self.game_config = self.full_config.get(
            'game_config', {})  # Extract game_config section
        self.config_file_path = get_current_config_path()  # Track which config we're using

        # Screen setup with fullscreen support
        self.fullscreen = False
        self.SCREEN_WIDTH = 1200
        self.SCREEN_HEIGHT = 800

        # Get display info for fullscreen
        info = pygame.display.Info()
        self.fullscreen_width = info.current_w
        self.fullscreen_height = info.current_h

        self.screen = pygame.display.set_mode(
            (self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
        pygame.display.set_caption("Tower Defense Game")
        self.clock = pygame.time.Clock()
        self.FPS = 60

        # Game state using config values
        self.running = True
        self.paused = False
        self.game_over = False
        self.victory = False
        self.money = self.game_config.get('starting_money', 20)
        self.lives = self.game_config.get('starting_lives', 20)

        # Performance tracking for adaptive AI
        self.starting_money = self.money
        self.starting_lives = self.lives
        self.total_money_spent = 0
        self.total_money_earned = self.starting_money  # Include starting money as earned

        # Game timing for accurate duration tracking
        self.game_start_time = time.time()
        self.tower_build_order = []  # Track order of tower construction

        # Speed control
        self.game_speed = 1  # 1 = normal speed, 2 = double speed
        self.speed_options = [1, 2]  # Available speed options
        self.current_speed_index = 0  # Index in speed_options

        # Victory/Game Over state
        self.show_victory_screen = False
        self.show_game_over_screen = False
        self.restart_requested = False
        self.exit_to_menu_requested = False

        # Wave completion tracking
        self.completed_wave_number = 0  # Track which wave was just completed

        # Performance monitoring
        self.fps_counter = 0
        self.fps_timer = 0
        self.current_fps = 60
        self.frame_time_samples = []
        self.max_frame_time_samples = 60  # Track last 60 frames
        self._last_frame_time = time.time()  # Initialize frame time tracking

        # Game objects
        self.enemies: List[Enemy] = []
        self.towers: List[Tower] = []
        self.projectiles: List[Projectile] = []

        # Initialize game systems
        self.map = Map(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.wave_manager = WaveManager(self.map.get_path())
        self.tower_manager = TowerManager()
        # Initialize tower costs for wave 1
        self.tower_manager.set_current_wave(1)
        self.ui_manager = UIManager(
            self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.tower_manager)
        self.upgrade_system = TowerUpgradeSystem()
        self.upgrade_ui = UpgradeUI(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)

        # Initialize performance optimizer
        from game_systems.performance_optimizer import PerformanceOptimizer
        from game_systems.render_optimizer import RenderOptimizer
        self.performance_optimizer = PerformanceOptimizer(
            self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.performance_optimizer.set_optimization_level(
            'auto')  # Start with auto optimization
        self.render_optimizer = RenderOptimizer(
            self.SCREEN_WIDTH, self.SCREEN_HEIGHT)

        # Initialize spatial partitioning system for performance optimization
        self.spatial_grid = SpatialGrid(
            self.SCREEN_WIDTH, self.SCREEN_HEIGHT, cell_size=80)

        # Initialize performance monitoring
        self.performance_monitor = PerformanceMonitor()
        self.performance_monitor.initialize_font()

        # Initialize sprite manager for enhanced graphics
        from game_systems.sprite_manager import SpriteManager
        self.sprite_manager = SpriteManager()

        # Load sprites immediately at game startup
        self.sprite_manager.load_all_sprites()

        # Set sprite manager for wave manager (for enemy sprites)
        self.wave_manager.set_sprite_manager(self.sprite_manager)

        # Ensure any existing towers get the sprite manager
        for tower in self.towers:
            if hasattr(tower, 'set_sprite_manager'):
                tower.set_sprite_manager(self.sprite_manager)

        # UI state
        self.show_wave_complete = False
        self.wave_complete_timer = 0
        self.wave_bonus = 0

        # Level up notification state
        self.show_level_up_notification = False
        self.level_up_timer = 0
        self.terrain_currency_gained = 0

        # Rock removal system
        self.rock_removal_cost = self.calculate_rock_removal_cost()

        # Rock removal rewards system
        self.rock_removal_rewards = RockRemovalRewards()

        # Health immunity system (from rock removal rewards)
        self.health_immunity_active = False
        self.health_immunity_timer = 0

        # Global upgrade system
        # Use existing system if passed from launcher, otherwise create new one
        if not hasattr(self, 'global_upgrade_system'):
            self.global_upgrade_system = GlobalUpgradeSystem()
        self.global_upgrade_ui = GlobalUpgradeUI(
            self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.global_upgrade_system)

        # Victory screen state
        self.show_victory_points = False
        self.victory_points_earned = 0
        self.victory_difficulty = 0

        # Tower ID counter for global upgrades
        self.tower_id_counter = 0

        self.screenshake_intensity = 0
        self.screenshake_timer = 0
        self.screenshake_offset = (0, 0)

    def choose_game_config(self):
        """Let player choose which config file to use"""
        from config.game_config import list_available_configs, set_config_file, get_current_config_file

        configs = list_available_configs()
        if len(configs) <= 1:
            # Only one config available, use default
            return

        print("=== Tower Defense Game ===")
        print("Choose your game configuration:")
        print()

        for i, config in enumerate(configs, 1):
            # Try to get a readable name from the config
            config_name = config.replace('.json', '').replace('_', ' ').title()
            if 'demo' in config.lower():
                config_name += " (Demo)"
            elif 'adaptive' in config.lower():
                config_name += " (AI-Generated)"
            elif 'nightmare' in config.lower():
                config_name += " (Very Hard)"
            elif 'hard' in config.lower():
                config_name += " (Hard)"
            elif 'normal' in config.lower():
                config_name += " (Normal)"
            elif 'tutorial' in config.lower():
                config_name += " (Easy)"
            elif 'casual' in config.lower():
                config_name += " (Very Easy)"

            print(f"   {i}. {config_name}")

        print(f"   {len(configs) + 1}. Use default ({get_current_config_file()})")
        print()

        while True:
            try:
                choice = input(
                    f"Select configuration (1-{len(configs) + 1}): ").strip()

                if choice == str(len(configs) + 1):
                    # Use default
                    break

                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(configs):
                    selected_config = configs[choice_idx]
                    set_config_file(selected_config)
                    print(f"Selected: {selected_config}")
                    break
                else:
                    print("❌ Invalid choice. Please try again.")

            except ValueError:
                print("❌ Please enter a valid number.")
            except KeyboardInterrupt:
                print("\nUsing default configuration.")
                break

        print("Starting game...")
        print()

    def handle_events(self):
        """Handle all game events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            # Handle enemy lookup events first (they can consume events)
            elif self.ui_manager.handle_enemy_lookup_event(event):
                continue  # Event was handled by enemy lookup system

            elif event.type == pygame.KEYDOWN:
                self.handle_key_press(event.key)

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    self.handle_mouse_click(event.pos)
                elif event.button == 3:  # Right click
                    self.handle_right_click(event.pos)
                elif event.button == 4:  # Mouse wheel up
                    self.ui_manager.handle_scroll(-1)
                elif event.button == 5:  # Mouse wheel down
                    self.ui_manager.handle_scroll(1)

            elif event.type == pygame.MOUSEWHEEL:
                # Handle newer pygame wheel events
                self.ui_manager.handle_scroll(event.y)

            elif event.type == pygame.MOUSEMOTION:
                # Update mouse position for UI hover effects
                self.ui_manager.update_mouse_pos(event.pos)
                self.upgrade_ui.update_mouse_pos(event.pos)

    def handle_key_press(self, key):
        """Handle keyboard input - only essential keys"""
        if key == pygame.K_SPACE:
            # Handle victory points screen
            if self.show_victory_points:
                self.show_victory_points = False
                return

            # Check if we can start the next wave first (manual wave start)
            if self.wave_manager.can_start_next_wave():
                wave_info = self.wave_manager.start_next_wave_manual()
                if wave_info:
                    # Update tower costs for the new wave
                    current_wave = wave_info.get('wave_number', 1)
                    self.tower_manager.set_current_wave(current_wave)
                return
            else:
                # Normal pause/unpause if not waiting for wave start
                self.paused = not self.paused

        elif key == pygame.K_ESCAPE:
            if self.game_over or self.victory:
                self.running = False
            elif self.global_upgrade_ui.is_open:
                self.global_upgrade_ui.close_menu()
            else:
                self.tower_manager.cancel_placement()

        elif key == pygame.K_r:
            if self.game_over or self.victory:
                self.restart_requested = True

        elif key == pygame.K_F1:
            self.toggle_fullscreen()

        elif key == pygame.K_TAB:
            self.toggle_game_speed()

        elif key == pygame.K_o:  # Toggle optimization level
            self.toggle_optimization_level()

        elif key == pygame.K_p:  # Toggle performance overlay
            self.performance_monitor.toggle_overlay()

        # Rock removal rewards debug controls
        elif key == pygame.K_F2:
            # Toggle debug mode
            if self.rock_removal_rewards.debug_mode:
                self.rock_removal_rewards.disable_debug_mode()
            else:
                self.rock_removal_rewards.enable_debug_mode()

        elif key == pygame.K_r and self.rock_removal_rewards.debug_mode:
            # Force rock removal reward (random)
            import random
            reward_types = ['money_bonus', 'tower_boost', 'enemy_boost',
                            'shield_boost', 'freeze_enemies', 'super_speed']
            reward_type = random.choice(reward_types)
            self.rock_removal_rewards.force_reward(reward_type)

        elif key == pygame.K_1 and self.rock_removal_rewards.debug_mode:
            # Force money bonus
            self.rock_removal_rewards.force_reward('money_bonus')

        elif key == pygame.K_2 and self.rock_removal_rewards.debug_mode:
            # Force tower boost
            self.rock_removal_rewards.force_reward('tower_boost')

        elif key == pygame.K_3 and self.rock_removal_rewards.debug_mode:
            # Force enemy boost
            self.rock_removal_rewards.force_reward('enemy_boost')

        elif key == pygame.K_4 and self.rock_removal_rewards.debug_mode:
            # Force shield boost
            self.rock_removal_rewards.force_reward('shield_boost')

        elif key == pygame.K_5 and self.rock_removal_rewards.debug_mode:
            # Force freeze enemies
            self.rock_removal_rewards.force_reward('freeze_enemies')

        elif key == pygame.K_6 and self.rock_removal_rewards.debug_mode:
            # Force super speed
            self.rock_removal_rewards.force_reward('super_speed')

        elif key == pygame.K_0 and self.rock_removal_rewards.debug_mode:
            # Disable test mode
            self.rock_removal_rewards.disable_test_mode()

        elif key == pygame.K_t and self.rock_removal_rewards.debug_mode:
            # Test rock removal rewards without removing rocks
            self.test_rock_removal_rewards()

    def toggle_fullscreen(self):
        """Toggle between fullscreen and windowed mode"""
        # Store old map parameters for coordinate scaling
        old_map = self.map
        old_cell_size = old_map.cell_size
        old_map_offset_x = old_map.map_offset_x
        old_map_offset_y = old_map.map_offset_y

        self.fullscreen = not self.fullscreen

        if self.fullscreen:
            # Switch to fullscreen
            self.screen = pygame.display.set_mode(
                (self.fullscreen_width, self.fullscreen_height), pygame.FULLSCREEN)
            # Update screen dimensions for game systems
            self.SCREEN_WIDTH = self.fullscreen_width
            self.SCREEN_HEIGHT = self.fullscreen_height
        else:
            # Switch to windowed
            self.screen = pygame.display.set_mode((1200, 800))
            # Restore original dimensions
            self.SCREEN_WIDTH = 1200
            self.SCREEN_HEIGHT = 800

        # Reinitialize systems with new screen dimensions
        self.map = Map(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)
        self.ui_manager = UIManager(
            self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.tower_manager)
        self.upgrade_ui = UpgradeUI(self.SCREEN_WIDTH, self.SCREEN_HEIGHT)

        # Scale existing tower coordinates to new resolution
        self._scale_tower_coordinates(old_cell_size, old_map_offset_x, old_map_offset_y,
                                      self.map.cell_size, self.map.map_offset_x, self.map.map_offset_y)

        # Scale existing enemy coordinates and update their path reference
        self._scale_enemy_coordinates(old_cell_size, old_map_offset_x, old_map_offset_y,
                                      self.map.cell_size, self.map.map_offset_x, self.map.map_offset_y)

        # Update wave manager path without resetting wave progression
        self._update_wave_manager_path()

    def _scale_tower_coordinates(self, old_cell_size, old_offset_x, old_offset_y,
                                 new_cell_size, new_offset_x, new_offset_y):
        """Scale tower coordinates from old resolution to new resolution"""
        if not self.towers:
            return

        # Calculate scaling factors
        for tower in self.towers:
            # Convert tower position from old pixel coordinates to grid coordinates
            old_grid_x = (tower.x - old_offset_x) / \
                old_cell_size if old_cell_size > 0 else 0
            old_grid_y = (tower.y - old_offset_y) / \
                old_cell_size if old_cell_size > 0 else 0

            # Convert grid coordinates to new pixel coordinates
            tower.x = new_offset_x + old_grid_x * new_cell_size
            tower.y = new_offset_y + old_grid_y * new_cell_size

            # Update grid position for terrain effects
            if hasattr(tower, 'set_grid_position'):
                grid_x = int(old_grid_x)
                grid_y = int(old_grid_y)
                tower.set_grid_position(grid_x, grid_y)

    def _scale_enemy_coordinates(self, old_cell_size, old_offset_x, old_offset_y,
                                 new_cell_size, new_offset_x, new_offset_y):
        """Scale enemy coordinates from old resolution to new resolution and update path reference"""
        if not self.enemies:
            return

        for enemy in self.enemies:
            # Convert enemy position from old pixel coordinates to grid coordinates
            old_grid_x = (enemy.x - old_offset_x) / \
                old_cell_size if old_cell_size > 0 else 0
            old_grid_y = (enemy.y - old_offset_y) / \
                old_cell_size if old_cell_size > 0 else 0

            # Convert grid coordinates to new pixel coordinates
            enemy.x = new_offset_x + old_grid_x * new_cell_size
            enemy.y = new_offset_y + old_grid_y * new_cell_size

            # Update enemy's path reference to the new scaled path
            enemy.path = self.map.get_path()

            # Recalculate distance traveled based on current path index and position
            if hasattr(enemy, 'path_index') and enemy.path_index < len(enemy.path):
                # Calculate distance from start of path to current position
                total_distance = 0
                for i in range(enemy.path_index):
                    if i + 1 < len(enemy.path):
                        dx = enemy.path[i + 1][0] - enemy.path[i][0]
                        dy = enemy.path[i + 1][1] - enemy.path[i][1]
                        total_distance += (dx * dx + dy * dy) ** 0.5

                # Add distance from last waypoint to current position
                if enemy.path_index < len(enemy.path):
                    current_waypoint = enemy.path[enemy.path_index]
                    dx = enemy.x - current_waypoint[0]
                    dy = enemy.y - current_waypoint[1]
                    total_distance += (dx * dx + dy * dy) ** 0.5

                enemy.distance_traveled = total_distance

    def _update_wave_manager_path(self):
        """Update wave manager path without resetting wave progression"""
        # Store current wave state
        old_wave_number = self.wave_manager.wave_number
        old_enemies_spawned = self.wave_manager.enemies_spawned
        old_spawn_timer = self.wave_manager.spawn_timer
        old_wave_complete = self.wave_manager.wave_complete
        old_waiting_for_next_wave = self.wave_manager.waiting_for_next_wave
        old_next_wave_ready = self.wave_manager.next_wave_ready
        old_wave_in_progress = self.wave_manager.wave_in_progress
        old_sprite_manager = self.wave_manager.sprite_manager

        # Update the path in the existing wave manager
        self.wave_manager.path = self.map.get_path()

        # Restore wave state
        self.wave_manager.wave_number = old_wave_number
        self.wave_manager.enemies_spawned = old_enemies_spawned
        self.wave_manager.spawn_timer = old_spawn_timer
        self.wave_manager.wave_complete = old_wave_complete
        self.wave_manager.waiting_for_next_wave = old_waiting_for_next_wave
        self.wave_manager.next_wave_ready = old_next_wave_ready
        self.wave_manager.wave_in_progress = old_wave_in_progress
        self.wave_manager.sprite_manager = old_sprite_manager

    def toggle_game_speed(self):
        """Toggle between different game speeds"""
        self.current_speed_index = (
            self.current_speed_index + 1) % len(self.speed_options)
        self.game_speed = self.speed_options[self.current_speed_index]

    def toggle_optimization_level(self):
        """Toggle between optimization levels"""
        levels = ['auto', 'low', 'medium', 'high']
        current_index = levels.index(
            self.performance_optimizer.optimization_level)
        next_index = (current_index + 1) % len(levels)
        new_level = levels[next_index]

        self.performance_optimizer.set_optimization_level(new_level)
        print(f"Optimization level: {new_level}")

    def handle_right_click(self, pos):
        """Handle right mouse clicks - cancels all selections"""
        # Don't handle right clicks during game over/victory or when paused
        if self.game_over or self.victory or self.paused:
            return

        # Handle right clicks in global upgrade UI
        if self.global_upgrade_ui.is_open:
            self.global_upgrade_ui.handle_click(pos, button=3)
            return

        # Cancel all selections
        self.clear_all_selections()

    def clear_all_selections(self, except_mode=None):
        """Clear all UI selections and modes, optionally preserving one mode"""
        # Clear tower bar selection
        self.ui_manager.clear_tower_selection()

        # Clear placed tower selection
        self.ui_manager.selected_placed_tower = None

        # Clear upgrade UI selection
        self.upgrade_ui.clear_selection()

        # Cancel tower placement
        self.tower_manager.cancel_placement()

        # Clear placement modes selectively
        if except_mode != 'rock_removal':
            self.ui_manager.rock_removal_mode = False
        if except_mode != 'water_placement':
            self.ui_manager.water_placement_mode = False
        if except_mode != 'grass_placement':
            self.ui_manager.grass_placement_mode = False

    def handle_mouse_click(self, pos):
        """Handle mouse clicks"""
        # Handle global upgrade UI clicks first
        if self.global_upgrade_ui.is_open:
            self.global_upgrade_ui.handle_click(pos)
            return

        # Handle restart and exit to menu button clicks for victory/game over screens
        if self.game_over or self.victory:
            restart_clicked = self.handle_restart_button_click(pos)
            exit_clicked = self.handle_exit_to_menu_button_click(pos)

            if restart_clicked:
                self.restart_requested = True
            elif exit_clicked:
                self.exit_to_menu_requested = True
            return

        if self.paused:
            return

        # Check for upgrade UI clicks first (highest priority)
        click_result = self.upgrade_ui.handle_click(
            pos, self.upgrade_system, self.money)
        if click_result['action'] == 'remove_tower':
            self.remove_tower(click_result['tower'])
            return
        elif click_result['action'] == 'upgrade':
            if click_result['success']:
                # Deduct money cost from player
                money_cost = click_result.get('money_cost', 0)
                self.money -= money_cost
                self.total_money_spent += money_cost  # Track for performance analysis
            return

        # Check for speed button click
        if self.ui_manager.handle_speed_button_click(pos):
            self.clear_all_selections()
            self.toggle_game_speed()
            return

        # Check for rock removal button click
        if self.ui_manager.handle_rock_removal_button_click(pos):
            self.clear_all_selections(except_mode='rock_removal')
            return

        # Check for water placement button click
        if self.ui_manager.handle_water_placement_button_click(pos, self.global_upgrade_system):
            self.clear_all_selections(except_mode='water_placement')
            return

        # Check for grass placement button click
        if self.ui_manager.handle_grass_placement_button_click(pos, self.global_upgrade_system):
            self.clear_all_selections(except_mode='grass_placement')
            return

        # Check for enemy info button click
        if self.ui_manager.handle_enemy_info_button_click(pos):
            self.clear_all_selections()
            return

        # Check for upgrade all button click
        if self.ui_manager.handle_upgrade_all_button_click(pos):
            self.clear_all_selections()
            self.handle_upgrade_all_towers()
            return

        # Check for tower bar clicks
        clicked_tower_index = self.ui_manager.handle_tower_bar_click(pos)
        if clicked_tower_index is not None:
            # Clear other selections but preserve the tower selection that was just made
            self.ui_manager.selected_placed_tower = None
            self.upgrade_ui.clear_selection()
            self.tower_manager.cancel_placement()
            self.ui_manager.clear_placement_modes()

            tower_type = self.ui_manager.get_selected_tower_type()
            if tower_type:
                self.tower_manager.select_tower_type(tower_type)
            return

        # Check for tower clicks (for upgrade panel)
        if self.ui_manager.handle_tower_click(pos, self.towers):
            # Get the selected tower BEFORE clearing selections
            selected_tower = self.ui_manager.selected_placed_tower

            # Clear other selections but preserve the tower selection
            self.ui_manager.clear_tower_selection()
            self.tower_manager.cancel_placement()
            self.ui_manager.clear_placement_modes()

            # Set the selected tower for upgrades
            self.upgrade_ui.set_selected_tower(selected_tower)
            return
        else:
            # Clear upgrade selection if clicking elsewhere
            self.upgrade_ui.clear_selection()

        # Handle rock removal, terrain placement, or tower placement
        if self.ui_manager.is_rock_removal_mode():
            # Try to remove a rock
            if self.attempt_rock_removal(pos):
                pass  # Rock was successfully removed
        elif self.ui_manager.is_water_placement_mode():
            # Try to place water in a pit
            if self.attempt_water_placement(pos):
                pass  # Water was successfully placed
        elif self.ui_manager.is_grass_placement_mode():
            # Try to place grass in a pit
            if self.attempt_grass_placement(pos):
                pass  # Grass was successfully placed
        elif self.tower_manager.placing_tower:
            self.attempt_tower_placement(pos)

    def handle_restart_button_click(self, mouse_pos) -> bool:
        """Check if restart button was clicked"""
        # Victory/Game Over screen restart button (left side)
        if self.show_victory_screen or self.show_game_over_screen or self.game_over or self.victory:
            # These coordinates MUST match the UI renderer exactly
            button_width = 180
            button_height = 50
            button_spacing = 20

            if self.victory or self.show_victory_screen:
                # Victory screen button position
                panel_height = 300
                panel_y = (self.SCREEN_HEIGHT - panel_height) // 2
                button_y = panel_y + 200
            else:
                # Game over screen button position
                panel_height = 250
                panel_y = (self.SCREEN_HEIGHT - panel_height) // 2
                button_y = panel_y + 170

            # Position restart button on the left
            button_x = (self.SCREEN_WIDTH -
                        (2 * button_width + button_spacing)) // 2

            if (button_x <= mouse_pos[0] <= button_x + button_width and
                    button_y <= mouse_pos[1] <= button_y + button_height):
                return True

        return False

    def handle_exit_to_menu_button_click(self, mouse_pos) -> bool:
        """Check if exit to menu button was clicked"""
        # Victory/Game Over screen exit to menu button (right side)
        if self.show_victory_screen or self.show_game_over_screen or self.game_over or self.victory:
            # These coordinates MUST match the UI renderer exactly
            button_width = 180
            button_height = 50
            button_spacing = 20

            if self.victory or self.show_victory_screen:
                # Victory screen button position
                panel_height = 300
                panel_y = (self.SCREEN_HEIGHT - panel_height) // 2
                button_y = panel_y + 200
            else:
                # Game over screen button position
                panel_height = 250
                panel_y = (self.SCREEN_HEIGHT - panel_height) // 2
                button_y = panel_y + 170

            # Position exit to menu button on the right
            button_x = (self.SCREEN_WIDTH - (2 * button_width +
                        button_spacing)) // 2 + button_width + button_spacing

            if (button_x <= mouse_pos[0] <= button_x + button_width and
                    button_y <= mouse_pos[1] <= button_y + button_height):
                return True

        return False

    def attempt_tower_placement(self, pos):
        """Try to place a tower at the given position"""
        success, tower, cost = self.tower_manager.attempt_tower_placement(
            pos, self.money, self.towers, self.map
        )

        if success and tower:
            # Assign unique tower ID for global upgrades
            self.tower_id_counter += 1
            if hasattr(tower, 'tower_type'):
                # type: ignore
                tower.tower_id = f"{tower.tower_type}_{self.tower_id_counter}"
                # Track tower build order for performance analysis
                self.tower_build_order.append(tower.tower_type)
            # Apply global upgrades to the tower
            if hasattr(tower, 'apply_upgrades_to_tower'):
                self.global_upgrade_system.apply_upgrades_to_tower(tower)
            # Set upgrade system reference for currency generation
            if hasattr(tower, 'set_upgrade_system_reference'):
                tower.set_upgrade_system_reference(
                    self.upgrade_system)  # type: ignore
            # Set sprite manager for enhanced graphics
            if hasattr(tower, 'set_sprite_manager'):
                tower.set_sprite_manager(self.sprite_manager)
            self.towers.append(tower)
            self.money -= cost
            self.total_money_spent += cost  # Track money spent for performance analysis

            # Apply current tower boost if active
            self.apply_boost_to_new_tower(tower)

    def remove_tower(self, tower):
        """Remove a tower and refund 50% of its cost"""
        if tower not in self.towers:
            return

        # Calculate refund (50% of current tower cost)
        tower_type = tower.tower_type
        current_cost = self.tower_manager.get_tower_cost(tower_type)
        refund = int(current_cost * 0.5)

        # Remove tower from list
        self.towers.remove(tower)

        # Give refund to player
        self.money += refund

        # Decrease tower count for dynamic pricing
        if tower_type in self.tower_manager.towers_built_count:
            self.tower_manager.towers_built_count[tower_type] = max(0,
                                                                    self.tower_manager.towers_built_count[tower_type] - 1)

        # Clear upgrade UI selection
        self.upgrade_ui.clear_selection()

    def handle_shadow_king_tower_destruction(self, destruction_data):
        """Handle Shadow King's tower destruction ability"""
        import math
        import random

        shadow_x = destruction_data['x']
        shadow_y = destruction_data['y']
        destruction_range = destruction_data['range']
        towers_to_destroy = destruction_data['count']

        # Find towers within range
        towers_in_range = []
        for tower in self.towers:
            distance = math.sqrt((tower.x - shadow_x) **
                                 2 + (tower.y - shadow_y)**2)
            if distance <= destruction_range:
                towers_in_range.append(tower)

        # Randomly select towers to destroy (up to the specified count)
        towers_to_remove = random.sample(
            towers_in_range,
            min(towers_to_destroy, len(towers_in_range))
        )

        # Remove selected towers (no refund - this is a boss ability)
        for tower in towers_to_remove:
            if tower in self.towers:
                self.towers.remove(tower)
                # Decrease tower count for dynamic pricing
                tower_type = tower.tower_type
                if tower_type in self.tower_manager.towers_built_count:
                    self.tower_manager.towers_built_count[tower_type] = max(0,
                                                                            self.tower_manager.towers_built_count[tower_type] - 1)

        # Trigger screenshake for dramatic effect
        if towers_to_remove:
            self.trigger_screenshake(intensity=20, duration=30)
            print(f"Shadow King destroyed {len(towers_to_remove)} towers!")

    def handle_upgrade_all_towers(self):
        """Handle upgrade all towers button click"""
        if not self.towers:
            return  # No towers to upgrade

        # Use the upgrade UI's upgrade all functionality
        upgrade_result = self.upgrade_ui.upgrade_all_towers(
            self.towers, self.upgrade_system, self.money)

        if upgrade_result['success']:
            # Deduct money spent
            money_spent = upgrade_result['total_money_spent']
            self.money -= money_spent
            self.total_money_spent += money_spent  # Track for performance analysis

            # Show feedback to player
            towers_upgraded = upgrade_result['towers_upgraded']
            print(f"Upgraded {towers_upgraded} towers for ${money_spent}")

            # If we have any specific UI feedback system, we could add it here
            # For example, showing a brief notification or updating visual indicators
        else:
            print(
                "No towers could be upgraded (insufficient resources or all towers are max level)")

    def update_enemies(self):
        """Update all enemies with spatial grid optimization"""
        enemies_to_add = []

        # Rebuild enemy spatial grid every few frames for performance
        if not hasattr(self, '_enemy_grid_frame_counter'):
            self._enemy_grid_frame_counter = 0

        self._enemy_grid_frame_counter += 1
        if self._enemy_grid_frame_counter >= 3:  # Rebuild every 3 frames
            self.spatial_grid.rebuild_enemy_grid(self.enemies)
            self._enemy_grid_frame_counter = 0

        for enemy in self.enemies[:]:
            # Store old position for spatial grid updates
            old_x, old_y = enemy.x, enemy.y

            # Optimized enemy update
            if hasattr(enemy, 'update_with_speed'):
                enemy.update_with_speed(self.game_speed)
            else:
                enemy.update()

            # Update spatial grid if enemy moved significantly
            if abs(enemy.x - old_x) > 5 or abs(enemy.y - old_y) > 5:
                self.spatial_grid.update_enemy_position(
                    enemy, old_x, old_y, enemy.x, enemy.y)

            # Check for Shadow King tower destruction ability
            if hasattr(enemy, 'get_pending_tower_destruction'):
                destruction_data = enemy.get_pending_tower_destruction()
                if destruction_data:
                    self.handle_shadow_king_tower_destruction(destruction_data)

            # Handle burn damage effects (moved from flame tower to prevent multiple processing)
            burn_timer = getattr(enemy, 'burn_timer', 0)
            if burn_timer > 0:
                enemy.burn_timer -= self.game_speed
                # Apply burn damage every 1/3 second (20 frames at normal speed)
                if enemy.burn_timer % 20 <= self.game_speed:
                    burn_damage = getattr(enemy, 'burn_damage', 0)
                    if burn_damage > 0:
                        enemy.take_damage(burn_damage, 'flame')

            # Handle poison effects (adjust timer based on speed)
            if hasattr(enemy, 'poison_timer') and enemy.poison_timer > 0:
                enemy.poison_timer -= self.game_speed
                if hasattr(enemy, 'poison_damage_timer'):
                    enemy.poison_damage_timer += self.game_speed
                    if enemy.poison_damage_timer >= 60:  # Every second
                        enemy.take_damage(enemy.poison_damage)
                        enemy.poison_damage_timer = 0

            # Handle boss minion spawning (while boss is alive)
            if hasattr(enemy, 'should_spawn_minions') and enemy.should_spawn_minions():
                # Use the boss's own spawn_minions method instead of hardcoded BasicEnemy
                spawned_minions = enemy.spawn_minions()  # type: ignore
                for minion in spawned_minions:
                    if hasattr(minion, 'set_map_reference'):
                        minion.set_map_reference(self.map)  # type: ignore
                    enemies_to_add.append(minion)

            # Handle Crystal Overlord shard spawning (when shards expire without being destroyed)
            if hasattr(enemy, 'spawn_crystalline_enemies_from_shard'):
                spawned_crystallines = enemy.spawn_crystalline_enemies_from_shard()  # type: ignore
                for crystalline in spawned_crystallines:
                    if hasattr(crystalline, 'set_map_reference'):
                        crystalline.set_map_reference(self.map)  # type: ignore
                    enemies_to_add.append(crystalline)

            # Handle TimeLord Boss echo spawning
            if hasattr(enemy, 'should_spawn_echoes') and callable(getattr(enemy, 'should_spawn_echoes', None)):
                spawnable_rifts = enemy.should_spawn_echoes()  # type: ignore
                for rift in spawnable_rifts:
                    from enemies import BasicEnemy
                    echo = BasicEnemy(self.map.get_path())
                    echo.x = rift['x']
                    echo.y = rift['y']
                    if hasattr(echo, 'health'):
                        echo.health = echo.health * 0.5  # type: ignore
                    if hasattr(echo, 'color'):
                        echo.color = (150, 0, 255)  # type: ignore
                    if hasattr(echo, 'set_map_reference'):
                        echo.set_map_reference(self.map)  # type: ignore
                    enemies_to_add.append(echo)

            # Handle Necromancer Boss undead summoning
            if hasattr(enemy, 'should_summon_undead'):
                summoned_enemies = enemy.should_summon_undead()
                if summoned_enemies:
                    for summoned_enemy in summoned_enemies:
                        if hasattr(summoned_enemy, 'set_map_reference'):
                            summoned_enemy.set_map_reference(
                                self.map)  # type: ignore
                        enemies_to_add.append(summoned_enemy)

            # Handle Necromancer Boss death aura effects
            if hasattr(enemy, 'apply_death_aura_to_enemies'):
                enemy.apply_death_aura_to_enemies(self.enemies)

            # Handle enemy death and removal
            if enemy.health <= 0:
                # Notify Necromancer bosses when their summoned minions die
                if hasattr(enemy, 'color') and enemy.color == (100, 50, 100):  # Summoned minion color
                    for boss in self.enemies:
                        if hasattr(boss, 'minion_died'):
                            boss.minion_died()

                # Check if enemy reached end or was killed
                if not enemy.reached_end:
                    # Enemy was killed - award money
                    self.money += enemy.reward
                    # Track money earned for performance analysis
                    self.total_money_earned += enemy.reward

                    # Award XP for killing the enemy
                    enemy_type = type(enemy).__name__
                    difficulty = self.get_current_difficulty()
                    current_wave = self.wave_manager.get_wave_info().get('wave_number', 1)

                    # Calculate difficulty multiplier based on game difficulty and wave progression
                    # Base multiplier from difficulty (0.1 to 2.0 range)
                    difficulty_multiplier = 0.1 + (difficulty / 100.0) * 1.9

                    # Add wave progression multiplier (slight increase per wave)
                    # 2% increase per wave
                    wave_multiplier = 1.0 + (current_wave - 1) * 0.02

                    # Combine multipliers
                    total_multiplier = difficulty_multiplier * wave_multiplier

                    # Award XP and check for level up
                    xp_awarded, level_up = self.global_upgrade_system.award_xp(
                        enemy_type, total_multiplier)

                    # Store level up info for UI display
                    if level_up:
                        self.show_level_up_notification = True
                        self.level_up_timer = 180  # Show for 3 seconds
                        # Get the dynamic terrain currency for the current level
                        self.terrain_currency_gained = self.global_upgrade_system.get_terrain_currency_for_level(
                            self.global_upgrade_system.player_level)

                    # Handle splitting enemies or other on_death mechanics
                    if hasattr(enemy, 'on_death'):
                        spawned_enemies = enemy.on_death()
                        if spawned_enemies:
                            for spawned_enemy in spawned_enemies:
                                if hasattr(spawned_enemy, 'set_map_reference'):
                                    spawned_enemy.set_map_reference(
                                        self.map)  # type: ignore
                                    enemies_to_add.append(spawned_enemy)
                else:
                    # Enemy reached end - lose lives unless health immunity is active
                    if not self.health_immunity_active:
                        # Default to 1 if no damage property
                        enemy_damage = getattr(enemy, 'damage', 1)
                        self.lives -= enemy_damage
                        # Stronger screenshake for higher damage enemies
                        screenshake_intensity = min(
                            30, 16 + (enemy_damage - 1) * 4)
                        screenshake_duration = min(
                            40, 22 + (enemy_damage - 1) * 6)
                        self.trigger_screenshake(
                            intensity=screenshake_intensity, duration=screenshake_duration)
                    if self.lives <= 0:
                        self.game_over = True
                        self.show_game_over_screen = True
                        # Save performance data for adaptive AI
                        self.save_performance_data()

                self.enemies.remove(enemy)
            elif enemy.reached_end:
                # Enemy reached the end - lose lives unless health immunity is active
                if not self.health_immunity_active:
                    # Default to 1 if no damage property
                    enemy_damage = getattr(enemy, 'damage', 1)
                    self.lives -= enemy_damage
                    # Stronger screenshake for higher damage enemies
                    screenshake_intensity = min(
                        30, 16 + (enemy_damage - 1) * 4)
                    screenshake_duration = min(40, 22 + (enemy_damage - 1) * 6)
                    self.trigger_screenshake(
                        intensity=screenshake_intensity, duration=screenshake_duration)
                if self.lives <= 0:
                    self.game_over = True
                    self.show_game_over_screen = True
                    # Save performance data for adaptive AI
                    self.save_performance_data()
                self.enemies.remove(enemy)

        # Add any spawned enemies
        self.enemies.extend(enemies_to_add)

    def update_towers(self):
        """Update all towers with spatial partitioning optimization"""
        # Rebuild spatial grid for towers (only when towers change)
        if not hasattr(self, '_towers_grid_built') or len(self.towers) != getattr(self, '_last_tower_count', 0):
            self.spatial_grid.rebuild_tower_grid(self.towers)
            self._towers_grid_built = True
            self._last_tower_count = len(self.towers)

        # First, clear all detection flags before detector towers update them
        for enemy in self.enemies:
            if hasattr(enemy, 'detected_by_detector'):
                enemy.detected_by_detector = False

        # Separate towers by type to ensure detector towers update first
        detector_towers = [
            tower for tower in self.towers if tower.tower_type == 'detector']
        other_towers = [
            tower for tower in self.towers if tower.tower_type != 'detector']

        # Update detector towers first
        for tower in detector_towers:
            # Pass spatial grid for optimized targeting
            if hasattr(tower, 'update_with_speed_optimized_spatial'):
                tower.update_with_speed_optimized_spatial(
                    self.enemies, self.projectiles, self.game_speed, self.spatial_grid)
            elif hasattr(tower, 'update_with_speed_optimized'):
                tower.update_with_speed_optimized(
                    self.enemies, self.projectiles, self.game_speed)
            elif hasattr(tower, 'update_with_speed'):
                tower.update_with_speed(
                    self.enemies, self.projectiles, self.game_speed)
            else:
                tower.update(self.enemies, self.projectiles)

        # Then update other towers with batching for performance
        # Initialize tower update batching
        if not hasattr(self, '_tower_batch_index'):
            self._tower_batch_index = 0

        # Process towers in batches to spread load across frames
        # Process 1/4 of towers per frame minimum 3
        batch_size = max(3, len(other_towers) // 4)
        start_idx = self._tower_batch_index
        end_idx = min(start_idx + batch_size, len(other_towers))

        towers_to_update = other_towers[start_idx:end_idx]

        for tower in towers_to_update:
            # Track damage before update (skip towers that handle their own currency)
            track_damage = tower.tower_type not in ['laser']
            if track_damage:
                previous_damage = tower.total_damage_dealt

            # Optimized tower update with spatial partitioning
            if hasattr(tower, 'update_with_speed_optimized_spatial'):
                tower.update_with_speed_optimized_spatial(
                    self.enemies, self.projectiles, self.game_speed, self.spatial_grid)
            elif hasattr(tower, 'update_with_speed_optimized'):
                tower.update_with_speed_optimized(
                    self.enemies, self.projectiles, self.game_speed)
            elif hasattr(tower, 'update_with_speed'):
                tower.update_with_speed(
                    self.enemies, self.projectiles, self.game_speed)
            else:
                tower.update(self.enemies, self.projectiles)

            # Check if tower dealt damage directly (not through projectiles)
            if track_damage:
                damage_this_frame = tower.total_damage_dealt - previous_damage
                if damage_this_frame > 0:
                    # Use centralized currency generation (already includes 5/100 nerf)
                    tower.track_damage_and_generate_currency(damage_this_frame)

        # Update batch index for next frame
        self._tower_batch_index = end_idx if end_idx < len(other_towers) else 0

    def update_projectiles(self):
        """Update all projectiles"""
        for projectile in self.projectiles[:]:
            # Optimized projectile update with cached method checks
            if hasattr(projectile, 'update_homing_with_speed'):
                projectile.update_homing_with_speed(
                    self.enemies, self.game_speed)  # type: ignore
            elif hasattr(projectile, 'update_homing'):
                projectile.update_homing(self.enemies)  # type: ignore
            elif hasattr(projectile, 'update_with_speed'):
                # Check if update method needs enemies parameter
                try:
                    projectile.update_with_speed(
                        self.enemies, self.game_speed)  # type: ignore
                except TypeError:
                    projectile.update_with_speed(
                        self.game_speed)  # type: ignore
            elif hasattr(projectile, 'update'):
                # Check if update method needs enemies parameter
                try:
                    projectile.update(self.enemies)  # type: ignore
                except TypeError:
                    projectile.update()  # type: ignore

            # Check for Crystal Overlord barrier collisions first
            barrier_blocked = False
            for enemy in self.enemies:
                if hasattr(enemy, 'check_barrier_collision'):
                    barrier_result = enemy.check_barrier_collision(projectile)
                    if barrier_result['hit']:
                        # Barrier blocked the projectile - 30% chance to destroy the tower that fired it
                        import random
                        tower_id = barrier_result['tower_id']
                        if tower_id and random.random() < 0.3:  # 30% chance
                            tower_to_destroy = self._find_tower_by_id(tower_id)
                            if tower_to_destroy and tower_to_destroy in self.towers:
                                # Remove the tower (no refund - this is a boss ability)
                                self.towers.remove(tower_to_destroy)
                                # Decrease tower count for dynamic pricing
                                tower_type = tower_to_destroy.tower_type
                                if tower_type in self.tower_manager.towers_built_count:
                                    self.tower_manager.towers_built_count[tower_type] = max(0,
                                                                                            self.tower_manager.towers_built_count[tower_type] - 1)
                                # Trigger visual effect
                                self.trigger_screenshake(
                                    intensity=8, duration=15)
                                print(
                                    f"Crystal Overlord destroyed {tower_type} tower!")
                        elif tower_id:
                            # Barrier blocked but tower survived
                            print(
                                "Crystal Overlord barrier blocked attack but tower survived!")

                        # Remove the projectile
                        projectile.should_remove = True
                        barrier_blocked = True
                        break

            # Check for Crystal Overlord shard collisions if barrier didn't block
            shard_hit = False
            if not barrier_blocked:
                for enemy in self.enemies:
                    if hasattr(enemy, 'check_shard_collision'):
                        shard_result = enemy.check_shard_collision(projectile)
                        if shard_result['hit']:
                            # Shard was hit - remove projectile and track damage
                            projectile.should_remove = True
                            shard_hit = True

                            # Track damage for currency generation
                            tower_id = getattr(
                                projectile, 'source_tower_id', None)
                            if tower_id:
                                tower = self._find_tower_by_id(tower_id)
                                if tower and shard_result['damage'] > 0:
                                    tower.track_damage_and_generate_currency(
                                        shard_result['damage'])

                            if shard_result['destroyed']:
                                print(f"Crystal Overlord shard destroyed!")
                                self.trigger_screenshake(
                                    intensity=6, duration=10)
                            break

            # Only check enemy collisions if neither barrier nor shard blocked the projectile
            if not barrier_blocked and not shard_hit and hasattr(projectile, 'check_collision'):
                collision_result = projectile.check_collision(self.enemies)

                # Handle damage tracking for currency generation
                if isinstance(collision_result, dict) and collision_result.get('hit'):
                    damage_dealt = collision_result.get('damage', 0)
                    tower_id = collision_result.get('tower_id')

                    # Trigger screenshake for explosive tower only
                    if hasattr(projectile, 'tower_type'):
                        if projectile.tower_type in ['explosive']:
                            # Large explosion screenshake
                            self.trigger_screenshake(intensity=14, duration=20)

                    # Ensure damage_dealt is not None and is a number
                    if damage_dealt is None:
                        damage_dealt = 0

                    if tower_id:
                        # Find the tower and use centralized currency generation
                        tower = self._find_tower_by_id(tower_id)
                        if tower:
                            if damage_dealt > 0:
                                # Use centralized damage tracking and currency generation
                                tower.track_damage_and_generate_currency(
                                    damage_dealt)
                            else:
                                # Support towers get minimal currency for successful hits
                                tower.track_utility_hit()

            # Check for screenshake flags set by projectiles (for explosions that bypass collision system)
            if hasattr(projectile, 'should_trigger_screenshake') and projectile.should_trigger_screenshake:
                if hasattr(projectile, 'tower_type') and projectile.tower_type in ['explosive']:
                    # Large explosion screenshake
                    self.trigger_screenshake(intensity=14, duration=20)
                # Reset the flag
                projectile.should_trigger_screenshake = False

            if hasattr(projectile, 'should_remove') and projectile.should_remove:
                self.projectiles.remove(projectile)

    def _find_tower_by_id(self, tower_id: str):
        """Find a tower by its unique ID"""
        for tower in self.towers:
            if hasattr(tower, 'tower_id') and tower.tower_id == tower_id:
                return tower
        return None

    def update_waves(self):
        """Update wave management"""
        # Spawn new enemies (adjusted for speed)
        new_enemy = self.wave_manager.spawn_enemy(self.game_speed)
        if new_enemy:
            # Set map reference for terrain effects
            if hasattr(new_enemy, 'set_map_reference'):
                new_enemy.set_map_reference(self.map)  # type: ignore

            # Apply current enemy boost if active
            if hasattr(self, 'apply_boost_to_new_enemy'):
                self.apply_boost_to_new_enemy(new_enemy)  # type: ignore

            # Trigger screenshake for boss spawns
            if hasattr(new_enemy, 'damage_reduction') and new_enemy.damage_reduction > 0.2:
                self.trigger_screenshake(intensity=18, duration=25)

            self.enemies.append(new_enemy)

        # Update rock removal cost when wave number changes
        current_wave = self.wave_manager.get_wave_info().get('wave_number', 1)
        if not hasattr(self, '_last_wave_for_cost') or self._last_wave_for_cost != current_wave:
            self.rock_removal_cost = self.calculate_rock_removal_cost()
            self._last_wave_for_cost = current_wave

        # Check for wave completion
        wave_info = self.wave_manager.update(self.enemies)
        if wave_info:
            if wave_info.get('game_completed', False):
                # Player has beaten the final wave!
                self.victory = True
                self.show_victory_screen = True

                # Calculate and award victory points based on difficulty
                difficulty = self.full_config.get(
                    'game_config', {}).get('difficulty', 50)
                points_earned = self.global_upgrade_system.award_victory_points(
                    difficulty)

                # Show victory points screen
                self.show_victory_points = True
                self.victory_points_earned = points_earned
                self.victory_difficulty = difficulty

                # Save performance data for adaptive AI
                self.save_performance_data()
            elif wave_info.get('wave_completed', False):
                # Normal wave completion - store the completed wave number BEFORE incrementing
                self.completed_wave_number = wave_info['wave_number']
                self.money += wave_info['money_bonus']
                # Track wave bonus for performance analysis
                self.total_money_earned += wave_info['money_bonus']
                self.wave_bonus = wave_info['money_bonus']
                self.show_wave_complete = True
                self.wave_complete_timer = 180  # Show for 3 seconds

                # Update rock removal cost for new wave (hefty late-game scaling)
                self.rock_removal_cost = self.calculate_rock_removal_cost()

                # Reset rock removal counter for new wave
                self.rock_removal_rewards.reset_wave_counter()

                # Manual wave start: Don't auto-start the next wave
                # Player must press SPACE to start the next wave

    def update_ui_state(self):
        """Update UI-related timers and state"""
        if self.show_wave_complete:
            self.wave_complete_timer -= self.game_speed
            if self.wave_complete_timer <= 0:
                self.show_wave_complete = False

        if self.show_level_up_notification:
            self.level_up_timer -= self.game_speed
            if self.level_up_timer <= 0:
                self.show_level_up_notification = False

    def update(self):
        """Update all game systems - FIXED: No longer runs multiple times per frame"""
        # Handle restart request
        if self.restart_requested:
            self.restart_game()
            return

        # Handle exit to menu request
        if self.exit_to_menu_requested:
            self.running = False
            return

        # Don't update if game is over, won, or exiting to menu
        if self.paused or self.game_over or self.victory or self.exit_to_menu_requested:
            return

        # Update rock removal effects
        self.rock_removal_rewards.update_effects(
            self.game_speed, self.towers, self.enemies, self)

        # Update health immunity timer
        if self.health_immunity_active:
            self.health_immunity_timer -= self.game_speed
            if self.health_immunity_timer <= 0:
                self.remove_health_immunity()

        # Single update pass with performance monitoring
        with SectionTimer(self.performance_monitor, 'enemies'):
            self.update_enemies()

        with SectionTimer(self.performance_monitor, 'towers'):
            self.update_towers()

        with SectionTimer(self.performance_monitor, 'projectiles'):
            self.update_projectiles()

        with SectionTimer(self.performance_monitor, 'total'):
            self.update_waves()
            self.update_ui_state()

        # Check for scheduled level generation (only check every 60 frames to avoid spam)
        if not hasattr(self, '_generation_check_timer'):
            self._generation_check_timer = 0
        self._generation_check_timer += 1
        if self._generation_check_timer >= 60:  # Check once per second at 60 FPS
            self.check_scheduled_level_generation()
            self._generation_check_timer = 0

    def check_scheduled_level_generation(self):
        """Check if it's time for scheduled daily level generation"""
        try:
            # Import here to avoid circular imports
            from game_systems.daily_level_generator import DailyLevelGenerator

            # Create or get daily level generator instance
            if not hasattr(self, '_daily_level_generator'):
                self._daily_level_generator = DailyLevelGenerator()

            # Check for generation opportunities
            generated = self._daily_level_generator.check_and_generate_if_needed()

            if generated:
                # Add UI message if available
                if hasattr(self, 'ui_manager') and hasattr(self.ui_manager, 'add_message'):
                    self.ui_manager.add_message(
                        "🕐 Daily level generated!", (0, 255, 0))
                print("🎉 Daily level generated during gameplay!")

        except Exception as e:
            print(f"Error in scheduled level generation check: {e}")

    def draw_game_objects(self, surface):
        """Draw all game objects with frustum culling optimization"""
        # Get screen bounds for culling
        screen_rect = pygame.Rect(0, 0, self.SCREEN_WIDTH, self.SCREEN_HEIGHT)

        # Draw enemies (with culling)
        enemies_drawn = 0
        for enemy in self.enemies:
            # Simple frustum culling - only draw if enemy is on screen
            if (enemy.x >= -50 and enemy.x <= self.SCREEN_WIDTH + 50 and
                    enemy.y >= -50 and enemy.y <= self.SCREEN_HEIGHT + 50):
                enemy.draw(surface)
                enemies_drawn += 1

        # Draw towers with selection state (towers are usually static, less culling needed)
        selected_tower = self.ui_manager.selected_placed_tower
        for tower in self.towers:
            is_selected = (tower == selected_tower)
            tower.draw(surface, selected=is_selected)

        # Draw projectiles (with culling)
        projectiles_drawn = 0
        for projectile in self.projectiles:
            # Cull projectiles that are far off screen
            if (hasattr(projectile, 'x') and hasattr(projectile, 'y') and
                projectile.x >= -100 and projectile.x <= self.SCREEN_WIDTH + 100 and
                    projectile.y >= -100 and projectile.y <= self.SCREEN_HEIGHT + 100):
                projectile.draw(surface)
                projectiles_drawn += 1

    def get_game_state(self) -> dict:
        """Get current game state for UI rendering"""
        wave_info = self.wave_manager.get_wave_info()
        placement_state = self.tower_manager.get_placement_state()

        # Add total waves information to wave_info for UI display
        total_waves = self.full_config.get('wave_config', {}).get(
            'total_waves', 80)  # Default to 80 if not found
        wave_info['total_waves'] = total_waves

        return {
            'money': self.money,
            'lives': self.lives,
            'wave_info': wave_info,
            'paused': self.paused,
            'game_over': self.game_over,
            'victory': self.victory,
            'show_victory_screen': self.show_victory_screen,
            'show_game_over_screen': self.show_game_over_screen,
            'selected_tower': placement_state['selected_tower_type'],
            'show_wave_complete': self.show_wave_complete,
            'completed_wave_number': self.completed_wave_number,
            'wave_bonus': self.wave_bonus,
            'show_level_up_notification': self.show_level_up_notification,
            'terrain_currency_gained': self.terrain_currency_gained,
            'xp_progress': self.global_upgrade_system.get_xp_progress(),
            'towers': self.towers,
            'game_speed': self.game_speed,
            'performance': self.get_performance_info(),
            'adaptive_performance': self.get_adaptive_performance_data(),
            'waiting_for_next_wave': self.wave_manager.waiting_for_next_wave,
            'can_start_wave': self.wave_manager.can_start_next_wave(),
            'rock_removal_cost': self.rock_removal_cost,
            'terrain_currency': self.global_upgrade_system.terrain_currency
        }

    def update_performance_metrics(self):
        """Update performance tracking metrics"""
        current_time = time.time()

        # Calculate individual frame time
        frame_time = current_time - self._last_frame_time
        self.frame_time_samples.append(frame_time)
        self._last_frame_time = current_time

        # Keep only recent samples
        if len(self.frame_time_samples) > self.max_frame_time_samples:
            self.frame_time_samples.pop(0)

        # Update FPS counter
        self.fps_counter += 1
        self.fps_timer += 1

        # Calculate FPS every second (reduced frequency for performance optimizer updates)
        if self.fps_timer >= 60:  # 60 frames at 60 FPS = 1 second
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.fps_timer = 0

            # Update performance optimizer with current metrics (less frequent)
            total_entities = len(self.enemies) + \
                len(self.towers) + len(self.projectiles)
            self.performance_optimizer.update_performance_metrics(
                self.current_fps, total_entities)

    def get_performance_info(self) -> dict:
        """Get performance information for display"""
        avg_frame_time = 0
        if self.frame_time_samples:
            avg_frame_time = sum(self.frame_time_samples) / \
                len(self.frame_time_samples)

        # Get optimization stats
        optimization_stats = self.performance_optimizer.get_optimization_stats()

        return {
            'fps': self.current_fps,
            'avg_frame_time_ms': avg_frame_time * 1000,  # Convert to milliseconds
            'entity_counts': {
                'enemies': len(self.enemies),
                'towers': len(self.towers),
                'projectiles': len(self.projectiles)
            },
            'optimization': {
                'level': self.performance_optimizer.optimization_level,
                'object_pooling': self.performance_optimizer.enable_object_pooling,
                'spatial_partitioning': self.performance_optimizer.enable_spatial_partitioning,
                'batch_processing': self.performance_optimizer.enable_batch_processing,
                'adaptive_updates': self.performance_optimizer.enable_adaptive_updates
            },
            'pool_stats': optimization_stats.get('pool_stats', {}),
            'spatial_grid_cells': optimization_stats.get('spatial_grid_cells', 0)
        }

    def get_tower_diversity_count(self) -> int:
        """Get the number of different tower types built"""
        tower_types_built = set()
        for tower in self.towers:
            tower_types_built.add(tower.tower_type)
        return len(tower_types_built)

    def get_adaptive_performance_data(self) -> dict:
        """Get performance data for the adaptive config generator"""
        # Calculate tower diversity
        towers_built_count = {}
        for tower in self.towers:
            tower_type = tower.tower_type
            towers_built_count[tower_type] = towers_built_count.get(
                tower_type, 0) + 1

        return {
            'win_flag': self.victory,
            'lives_remaining': self.lives,
            'starting_lives': self.starting_lives,
            'towers_built': towers_built_count,
            'tower_diversity': self.get_tower_diversity_count()
        }

    def _extract_enemy_types_from_config(self, config: dict) -> list:
        """Extract actual enemy types used from the configuration"""
        enemy_types = set()

        # Method 1: Extract from wave_compositions (most common)
        wave_config = config.get('wave_config', {})
        wave_compositions = wave_config.get('wave_compositions', {})

        for wave_range, compositions in wave_compositions.items():
            if isinstance(compositions, list):
                for composition in compositions:
                    if isinstance(composition, list) and len(composition) >= 1:
                        # First element is enemy type
                        enemy_type = composition[0]
                        if isinstance(enemy_type, str) and enemy_type.endswith('Enemy'):
                            enemy_types.add(enemy_type)

        # Method 2: Extract from spawn_config if it contains enemy-specific configs
        spawn_config = wave_config.get('spawn_config', {})
        for key in spawn_config.keys():
            if isinstance(key, str) and key.endswith('Enemy'):
                enemy_types.add(key)

        # Method 3: Extract from boss_waves
        boss_waves = wave_config.get('boss_waves', {})
        for wave_num, boss_config in boss_waves.items():
            if isinstance(boss_config, dict):
                boss_type = boss_config.get(
                    'boss_type') or boss_config.get('enemy_type')
                if boss_type and isinstance(boss_type, str):
                    enemy_types.add(boss_type)
            elif isinstance(boss_config, str):
                enemy_types.add(boss_config)

        # Method 4: Look for enemy scaling configs (enemy-specific scaling)
        enemy_scaling = wave_config.get('enemy_scaling', {})
        if isinstance(enemy_scaling, dict):
            for key in enemy_scaling.keys():
                if isinstance(key, str) and key.endswith('Enemy'):
                    enemy_types.add(key)

        # Convert to sorted list for consistent output
        result = sorted(list(enemy_types))

        # If no enemy types found, provide basic fallback
        if not result:
            # Look for any configuration that might indicate enemy types
            if 'BasicEnemy' in str(config):
                result = ['BasicEnemy']
            else:
                result = ['Unknown']

        return result

    def get_current_difficulty(self) -> int:
        """Get the current game difficulty level"""
        difficulty_level = 50  # Default difficulty

        if hasattr(self, 'full_config'):
            config = self.full_config

            # Check multiple locations for difficulty (same logic as game_config.py)
            if '_generation_metadata' in config:
                difficulty_level = config['_generation_metadata'].get(
                    'difficulty', 50)
            elif '_adaptive_metadata' in config:
                difficulty_level = config['_adaptive_metadata']['ai_adjustments']['difficulty_adjustment'].get(
                    'new_difficulty', 50)
            elif 'calculated_difficulty' in config:
                # Check if it's a dict with 'score' field (like tower_defense_game.json)
                calc_diff = config.get('calculated_difficulty')
                if isinstance(calc_diff, dict):
                    difficulty_level = calc_diff.get('score', 50)
                else:
                    difficulty_level = calc_diff
            elif 'game_config' in config and 'difficulty' in config['game_config']:
                # Check for difficulty in game_config section
                game_config = config['game_config']
                difficulty_value = game_config.get('difficulty')

                # Convert difficulty level names to scores
                if isinstance(difficulty_value, str):
                    difficulty_map = {
                        'test': 1,
                        'tutorial': 10,
                        'easy': 20,
                        'casual': 25,
                        'normal': 50,
                        'medium': 60,
                        'hard': 70,
                        'very_hard': 85,
                        'nightmare': 95,
                        'impossible': 100
                    }
                    difficulty_level = difficulty_map.get(
                        difficulty_value.lower(), 50)
                elif isinstance(difficulty_value, (int, float)):
                    difficulty_level = int(difficulty_value)
            elif 'difficulty' in config:
                # Check for difficulty at root level
                diff_data = config.get('difficulty')
                if isinstance(diff_data, dict):
                    # Check for score in difficulty object
                    if 'score' in diff_data:
                        difficulty_level = diff_data['score']
                    elif 'level' in diff_data:
                        level = diff_data['level']
                        if isinstance(level, str):
                            difficulty_map = {
                                'test': 1,
                                'tutorial': 10,
                                'easy': 20,
                                'casual': 25,
                                'normal': 50,
                                'medium': 60,
                                'hard': 70,
                                'very_hard': 85,
                                'nightmare': 95,
                                'impossible': 100
                            }
                            difficulty_level = difficulty_map.get(
                                level.lower(), 50)
                        elif isinstance(level, (int, float)):
                            difficulty_level = int(level)
                elif isinstance(diff_data, (int, float)):
                    difficulty_level = int(diff_data)
                elif isinstance(diff_data, str):
                    difficulty_map = {
                        'test': 1,
                        'tutorial': 10,
                        'easy': 20,
                        'casual': 25,
                        'normal': 50,
                        'medium': 60,
                        'hard': 70,
                        'very_hard': 85,
                        'nightmare': 95,
                        'impossible': 100
                    }
                    difficulty_level = difficulty_map.get(
                        diff_data.lower(), 50)

        return difficulty_level

    def calculate_rock_removal_cost(self) -> int:
        """Calculate the cost to remove rocks based on AI-determined difficulty and current wave"""
        # Get difficulty from the config using comprehensive checking
        difficulty_level = self.get_current_difficulty()

        # Get current wave number for scaling
        current_wave = self.wave_manager.get_wave_info().get('wave_number', 1)

        # Get AI-determined rock removal parameters if available
        ai_base_cost = None
        ai_scaling_factor = None
        if hasattr(self, 'full_config') and 'rock_removal_config' in self.full_config:
            rock_config = self.full_config['rock_removal_config']
            ai_base_cost = rock_config.get('base_cost')
            ai_scaling_factor = rock_config.get('wave_scaling_factor')

        # Use the map's calculation method with wave progression
        return self.map.calculate_rock_removal_cost(difficulty_level, current_wave, ai_base_cost, ai_scaling_factor)

    def attempt_rock_removal(self, pos: Tuple[int, int]) -> bool:
        """Try to remove a rock at the given position and apply rewards"""
        x, y = pos

        # Check if there's a rock at this position
        if self.map.has_rock_at_position(x, y):
            # Check if player can afford it
            if self.money >= self.rock_removal_cost:
                # Remove the rock and charge the player
                if self.map.remove_rock_at_position(x, y):
                    self.money -= self.rock_removal_cost
                    self.total_money_spent += self.rock_removal_cost

                    # Track rock removal for rewards
                    self.rock_removal_rewards.increment_rock_removed(
                        self.wave_manager, self.towers, self.enemies, self)

                    return True
        return False

    def attempt_water_placement(self, pos: Tuple[int, int]) -> bool:
        """Try to place water in a pit at the given position"""
        x, y = pos

        # Check if player can afford water placement
        if not self.global_upgrade_system.can_afford_terrain_placement('water'):
            return False

        # Check if there's a pit at this position
        if self.map.has_pit_at_position(x, y):
            # Place water in the pit
            if self.map.place_water_at_position(x, y):
                # Spend terrain currency
                self.global_upgrade_system.spend_terrain_currency('water')
                return True
        return False

    def attempt_grass_placement(self, pos: Tuple[int, int]) -> bool:
        """Try to place grass in a pit at the given position"""
        x, y = pos

        # Check if player can afford grass placement
        if not self.global_upgrade_system.can_afford_terrain_placement('grass'):
            return False

        # Check if there's a pit at this position
        if self.map.has_pit_at_position(x, y):
            # Place grass in the pit
            if self.map.place_grass_at_position(x, y):
                # Spend terrain currency
                self.global_upgrade_system.spend_terrain_currency('grass')
                return True
        return False

    def apply_boost_to_new_tower(self, tower):
        """Apply current tower boost to a newly created tower"""
        self.rock_removal_rewards.apply_boost_to_new_tower(tower)

    def apply_boost_to_new_enemy(self, enemy):
        """Apply current enemy boost to a newly spawned enemy"""
        self.rock_removal_rewards.apply_boost_to_new_enemy(enemy)

    def apply_super_speed(self, multiplier):
        """Apply super speed effect to the game"""
        # Store original game speed if not already stored
        if not hasattr(self, '_original_game_speed'):
            self._original_game_speed = self.game_speed
        self.game_speed = self._original_game_speed * multiplier
        print(f"Super speed applied: {multiplier}x speed")

    def remove_super_speed(self):
        """Remove super speed effect from the game"""
        if hasattr(self, '_original_game_speed'):
            self.game_speed = self._original_game_speed
            print("Super speed removed")

    def test_rock_removal_rewards(self):
        """Test rock removal rewards without actually removing rocks"""
        if not self.rock_removal_rewards.debug_mode:
            return

        print("🧪 Testing rock removal rewards...")
        print(self.rock_removal_rewards.get_debug_info())

        # Test applying rewards
        rewards_activated = self.rock_removal_rewards.check_wave_rewards(
            self.wave_manager, self.towers, self.enemies, self
        )

        print(f"🧪 Rewards activated: {rewards_activated}")

        # Add test message
        self.rock_removal_rewards.add_message(
            "🧪 Test completed - check console for details", (255, 255, 0))

    def draw(self):
        """Draw everything"""
        # Start render optimization frame
        self.render_optimizer.start_frame()

        # Update screenshake first
        self.update_screenshake()
        offset_x, offset_y = self.screenshake_offset

        # Use screen directly if no screenshake, otherwise use temp surface
        if offset_x == 0 and offset_y == 0:
            draw_surface = self.screen
        else:
            # Only create temp surface when needed for screenshake
            if not hasattr(self, '_temp_surface') or self._temp_surface.get_size() != (self.SCREEN_WIDTH, self.SCREEN_HEIGHT):
                self._temp_surface = pygame.Surface(
                    (self.SCREEN_WIDTH, self.SCREEN_HEIGHT))
            draw_surface = self._temp_surface

        draw_surface.fill((0, 0, 0))

        # Cache frequently accessed values
        mouse_pos = pygame.mouse.get_pos()
        removing_rocks = self.ui_manager.is_rock_removal_mode()
        can_afford_removal = self.money >= self.rock_removal_cost

        # Draw map (includes background and path)
        placing_water = self.ui_manager.is_water_placement_mode()
        placing_grass = self.ui_manager.is_grass_placement_mode()
        self.map.draw(
            draw_surface,
            self.tower_manager.placing_tower,
            mouse_pos,
            self.towers,
            self.tower_manager.selected_tower_type,
            removing_rocks,
            can_afford_removal,
            placing_water,
            placing_grass
        )

        # Draw game objects
        self.draw_game_objects(draw_surface)

        # Draw UI
        game_state = self.get_game_state()
        self.ui_manager.draw_complete_ui(draw_surface, game_state)

        # Draw upgrade UI
        self.upgrade_ui.draw_upgrade_panel(draw_surface, self.upgrade_system)

        # Draw rock removal effects
        self.rock_removal_rewards.draw_effects(draw_surface)

        # Draw global upgrade UI if open
        if self.global_upgrade_ui.is_open:
            self.global_upgrade_ui.draw(draw_surface)

        # Draw victory points screen
        if self.show_victory_points:
            self.global_upgrade_ui.draw_victory_screen(
                draw_surface, self.victory_difficulty, self.victory_points_earned)

        # Blit temp surface to screen only if screenshake is active
        if offset_x != 0 or offset_y != 0:
            self.screen.blit(draw_surface, (offset_x, offset_y))

        # Draw performance overlay (after everything else)
        self.performance_monitor.draw_overlay(self.screen)

        # End render optimization frame
        self.render_optimizer.end_frame()

        pygame.display.flip()

    def restart_game(self):
        """Restart the game to initial state - COMPLETE RESET"""

        # Reset core game state
        self.game_over = False
        self.victory = False
        self.paused = False
        self.money = self.game_config.get(
            'starting_money', 20)  # Reset to config amount
        self.lives = self.game_config.get(
            'starting_lives', 20)  # Reset to config amount

        # Reset performance tracking
        self.starting_money = self.money
        self.starting_lives = self.lives
        self.total_money_spent = 0
        self.total_money_earned = self.starting_money

        # Reset game timing and tower tracking
        self.game_start_time = time.time()
        self.tower_build_order = []

        # Reset game speed to normal
        self.game_speed = 1
        self.current_speed_index = 0

        # Reset UI state completely
        self.show_victory_screen = False
        self.show_game_over_screen = False
        self.show_wave_complete = False
        self.show_level_up_notification = False
        self.restart_requested = False
        self.exit_to_menu_requested = False
        self.completed_wave_number = 0
        self.wave_bonus = 0
        self.wave_complete_timer = 0
        self.level_up_timer = 0
        self.terrain_currency_gained = 0

        # Clear all game objects
        self.enemies.clear()
        self.towers.clear()
        self.projectiles.clear()

        # Reset all managers to initial state
        self.wave_manager = WaveManager(self.map.get_path())
        self.wave_manager.set_sprite_manager(self.sprite_manager)
        self.wave_manager.reset_introductions()  # Reset enemy introductions

        self.tower_manager = TowerManager()
        self.tower_manager.set_current_wave(1)  # Reset to wave 1 costs
        self.tower_manager.reset_ui_state()     # Reset tower placement UI state

        # Reset upgrade system
        self.upgrade_system = TowerUpgradeSystem()

        # Recreate UI manager with new tower manager reference
        self.ui_manager = UIManager(
            self.SCREEN_WIDTH, self.SCREEN_HEIGHT, self.tower_manager)
        self.upgrade_ui.reset_ui_state()        # Reset upgrade panel completely

        # Reset global upgrade UI state
        self.global_upgrade_ui.close_menu()
        self.show_victory_points = False

        # Reset performance tracking
        self.fps_counter = 0
        self.fps_timer = 0
        self.current_fps = 60
        self.frame_time_samples.clear()
        self._last_frame_time = time.time()  # Reset frame time tracking

        # Reset rock removal cost
        self.rock_removal_cost = self.calculate_rock_removal_cost()

        # Reset rock removal rewards system
        self.rock_removal_rewards = RockRemovalRewards()
        self.rocks_removed_this_wave = 0
        self.total_rocks_removed = 0

        # Reset tower boost system
        self.tower_boost_active = False
        self.tower_boost_timer = 0
        self.tower_boost_duration = 600  # 10 seconds at 60 FPS
        self.tower_boost_multiplier = 1.5  # 50% damage increase

        # Reset enemy boost system
        self.enemy_boost_active = False
        self.enemy_boost_timer = 0
        self.enemy_boost_duration = 300  # 5 seconds at 60 FPS
        self.enemy_boost_multiplier = 1.3  # 30% speed increase

    def run(self):
        """Main game loop with performance monitoring"""
        while self.running:
            # Start frame timing
            self.performance_monitor.start_frame()

            self.handle_events()
            self.update()

            with SectionTimer(self.performance_monitor, 'rendering'):
                self.draw()

            self.update_performance_metrics()
            self.clock.tick(self.FPS)

        # Only quit pygame and exit if not launched from menu
        # If launched from menu, just return to let launcher handle the rest
        if not self.launched_from_menu:
            pygame.quit()
            sys.exit()

    def trigger_screenshake(self, intensity=10, duration=20):
        """Trigger a screenshake effect with given intensity and duration (in frames)"""
        self.screenshake_intensity = max(self.screenshake_intensity, intensity)
        self.screenshake_timer = max(self.screenshake_timer, duration)

    def update_screenshake(self):
        """Update screenshake offset and decay the effect each frame"""
        if self.screenshake_timer > 0:
            import random
            shake_x = random.randint(-self.screenshake_intensity,
                                     self.screenshake_intensity)
            shake_y = random.randint(-self.screenshake_intensity,
                                     self.screenshake_intensity)
            self.screenshake_offset = (shake_x, shake_y)
            self.screenshake_timer -= 1
            # Gradually reduce intensity
            if self.screenshake_timer < 10:
                self.screenshake_intensity = max(
                    0, self.screenshake_intensity - 1)
        else:
            self.screenshake_offset = (0, 0)

    def _analyze_strategic_context(self, towers_built: Dict[str, int], waves_completed: int, total_waves: int) -> Dict[str, Any]:
        """Analyze strategic context and decision patterns from the game"""
        analysis = {}

        # Determine defeat cause if game was lost
        if not self.victory:
            if self.lives <= 0:
                if waves_completed < total_waves * 0.2:
                    analysis['defeat_cause'] = 'early_game_economy'
                elif waves_completed < total_waves * 0.5:
                    analysis['defeat_cause'] = 'mid_game_scaling'
                elif waves_completed < total_waves * 0.8:
                    analysis['defeat_cause'] = 'late_game_pressure'
                else:
                    analysis['defeat_cause'] = 'final_waves_difficulty'
            else:
                analysis['defeat_cause'] = 'unknown'

        # Analyze tower effectiveness based on what was built
        tower_effectiveness = {}
        total_towers = sum(towers_built.values())

        if total_towers > 0:
            for tower_type, count in towers_built.items():
                usage_percentage = (count / total_towers) * 100
                tower_effectiveness[tower_type] = {
                    'count': count,
                    'usage_percentage': usage_percentage,
                    'effectiveness_rating': 'high' if usage_percentage > 30 else 'medium' if usage_percentage > 15 else 'low'
                }

        analysis['tower_effectiveness_analysis'] = tower_effectiveness

        # Identify critical decision points based on game progression
        critical_points = []

        if waves_completed > 0:
            # Early game decisions
            if waves_completed >= 5:
                critical_points.append({
                    'phase': 'early_game',
                    'wave_range': '1-5',
                    'decision': 'initial_tower_selection',
                    'outcome': 'successful' if waves_completed > 5 else 'struggled'
                })

            # Mid game decisions
            if waves_completed >= total_waves * 0.3:
                critical_points.append({
                    'phase': 'mid_game',
                    'wave_range': f'6-{int(total_waves * 0.3)}',
                    'decision': 'scaling_strategy',
                    'outcome': 'successful' if waves_completed > total_waves * 0.3 else 'struggled'
                })

            # Late game decisions
            if waves_completed >= total_waves * 0.7:
                critical_points.append({
                    'phase': 'late_game',
                    'wave_range': f'{int(total_waves * 0.3)}-{int(total_waves * 0.7)}',
                    'decision': 'endgame_preparation',
                    'outcome': 'successful' if self.victory else 'struggled'
                })

        analysis['critical_decision_points'] = critical_points

        # Analyze adaptation patterns based on tower diversity and build order
        adaptation_patterns = {}

        if hasattr(self, 'tower_build_order') and self.tower_build_order:
            build_order = self.tower_build_order
            tower_diversity = len(set(build_order))

            adaptation_patterns['tower_diversity'] = tower_diversity
            adaptation_patterns['build_pattern'] = 'diverse' if tower_diversity > 3 else 'focused' if tower_diversity > 1 else 'single_strategy'

            # Check for adaptation (changing tower types during game)
            if len(build_order) > 1:
                type_changes = sum(1 for i in range(
                    1, len(build_order)) if build_order[i] != build_order[i-1])
                adaptation_patterns['strategy_changes'] = type_changes
                adaptation_patterns['adaptability'] = 'high' if type_changes > len(
                    build_order) * 0.3 else 'medium' if type_changes > 0 else 'low'

        analysis['adaptation_patterns'] = adaptation_patterns

        # Identify potential strategic mistakes based on common patterns
        strategic_mistakes = []

        if not self.victory:
            if total_towers < waves_completed * 0.5:
                strategic_mistakes.append('insufficient_tower_count')

            if len(towers_built) == 1:
                strategic_mistakes.append('lack_of_tower_diversity')

            if self.money > 100 and not self.victory:
                strategic_mistakes.append('unused_resources')

            if waves_completed < total_waves * 0.2:
                strategic_mistakes.append('poor_early_game_economy')

        analysis['strategic_mistakes'] = strategic_mistakes

        # Identify successful strategies
        successful_strategies = []

        if self.victory or waves_completed > total_waves * 0.7:
            if len(towers_built) > 3:
                successful_strategies.append('diverse_tower_strategy')

            if self.money < 50:  # Efficient resource usage
                successful_strategies.append('efficient_resource_management')

            if waves_completed == total_waves:
                successful_strategies.append('complete_victory')

            # Check for economic efficiency
            if hasattr(self, 'total_money_earned') and hasattr(self, 'total_money_spent'):
                if self.total_money_spent > 0 and (self.total_money_earned / self.total_money_spent) > 1.2:
                    successful_strategies.append('strong_economic_efficiency')

        analysis['successful_strategies'] = successful_strategies

        # Note: enemy_type_struggles would require more detailed enemy tracking
        # For now, we'll leave it as None and could enhance it later
        analysis['enemy_type_struggles'] = None

        return analysis

    def save_performance_data(self):
        """Save performance data to ai/performance_data/ as 1.json-5.json (circular buffer), only on victory or defeat."""
        # Only save if game is over (victory or defeat)
        if not (self.victory or self.game_over):
            return

        # Prepare enhanced performance data
        towers_built = {}
        for tower in self.towers:
            ttype = getattr(tower, 'tower_type', 'unknown')
            towers_built[ttype] = towers_built.get(ttype, 0) + 1

        tower_diversity = len(towers_built)

        # Find most built tower type
        most_built_tower_type = None
        if towers_built:
            most_built_tower_type = max(
                towers_built.items(), key=lambda x: x[1])[0]

        # Calculate economic metrics
        economic_efficiency = 0.0
        if self.total_money_spent > 0:
            economic_efficiency = self.total_money_earned / self.total_money_spent

        # Calculate resource management score (0-100)
        resource_management_score = 0.0
        if self.starting_money > 0:
            # Score based on money utilization and survival
            money_utilization = min(
                1.0, self.total_money_spent / (self.total_money_earned + 1))
            survival_rate = self.lives / self.starting_lives
            resource_management_score = (
                money_utilization * 0.6 + survival_rate * 0.4) * 100

        # Extract difficulty context from current config
        config_difficulty_score = None
        config_difficulty_description = None
        config_difficulty_components = None
        level_metadata = None

        if hasattr(self, 'full_config') and self.full_config:
            calc_diff = self.full_config.get('calculated_difficulty', {})
            if calc_diff:
                config_difficulty_score = calc_diff.get('score')
                config_difficulty_description = calc_diff.get('description')
                config_difficulty_components = calc_diff.get(
                    'formula_components', {})

            # Also extract level metadata for additional context
            level_metadata = self.full_config.get('level_metadata', {})

        # Calculate wave progression
        total_waves = self.full_config.get(
            'wave_config', {}).get('total_waves', 80)
        waves_completed = getattr(self, 'completed_wave_number', 1)
        progression_percentage = (
            waves_completed / total_waves) * 100 if total_waves > 0 else 0.0

        # Calculate actual game duration
        actual_duration = time.time() - getattr(self, 'game_start_time', time.time())

        # Analyze strategic context
        strategic_analysis = self._analyze_strategic_context(
            towers_built, waves_completed, total_waves)

        # Calculate average FPS
        avg_fps = getattr(self, 'current_fps', 60.0)

        perf = PerformanceData(
            win_flag=self.victory,
            lives_remaining=self.lives,
            starting_lives=self.starting_lives,
            towers_built=towers_built,
            tower_diversity=tower_diversity,
            wave_reached=waves_completed,
            final_wave=waves_completed,
            previous_config=None,
            previous_config_details=None,

            # Enhanced performance tracking
            starting_money=self.starting_money,
            money_remaining=self.money,
            total_money_spent=getattr(self, 'total_money_spent', 0),
            total_money_earned=getattr(self, 'total_money_earned', 0),
            game_duration_seconds=actual_duration,
            average_fps=avg_fps,

            # Strategic decision tracking
            most_built_tower_type=most_built_tower_type,
            tower_build_order=getattr(self, 'tower_build_order', []),
            economic_efficiency=economic_efficiency,
            resource_management_score=resource_management_score,

            # Difficulty context from config
            config_difficulty_score=config_difficulty_score,
            config_difficulty_description=config_difficulty_description,
            config_difficulty_components=config_difficulty_components,
            level_metadata=level_metadata,

            # Wave progression details
            waves_completed_successfully=waves_completed,
            total_waves_in_config=total_waves,
            progression_percentage=progression_percentage,

            # Enhanced strategic context
            defeat_cause=strategic_analysis.get('defeat_cause'),
            tower_effectiveness_analysis=strategic_analysis.get(
                'tower_effectiveness_analysis'),
            critical_decision_points=strategic_analysis.get(
                'critical_decision_points'),
            adaptation_patterns=strategic_analysis.get('adaptation_patterns'),
            enemy_type_struggles=strategic_analysis.get(
                'enemy_type_struggles'),
            strategic_mistakes=strategic_analysis.get('strategic_mistakes'),
            successful_strategies=strategic_analysis.get(
                'successful_strategies')
        )
        data = perf.to_dict()
        # Add config file path for loader
        data['config_file_path'] = getattr(
            self, 'config_file_path', 'tower_defense_game.json')

        # Determine next file in circular buffer (1.json-5.json)
        perf_dir = os.path.join(os.path.dirname(
            __file__), 'ai', 'performance_data')
        os.makedirs(perf_dir, exist_ok=True)
        # Find the oldest or first available slot
        slots = [os.path.join(perf_dir, f'{i}.json') for i in range(1, 6)]
        slot_times = [(f, os.path.getmtime(f))
                      for f in slots if os.path.exists(f)]
        if len(slot_times) < 5:
            # Use first available slot
            for f in slots:
                if not os.path.exists(f):
                    target_file = f
                    break
        else:
            # Overwrite the oldest
            target_file = min(slot_times, key=lambda x: x[1])[0]
        # Write the performance file
        with open(target_file, 'w') as f:
            json.dump(data, f, indent=2)
        # Write completion marker for launcher
        marker_path = os.path.join(perf_dir, 'last_completion.txt')
        with open(marker_path, 'w') as f:
            f.write(
                f'{datetime.utcnow().isoformat()}Z\n{os.path.basename(target_file)}\n')
        print(f'Performance data saved to {target_file}')

    def activate_health_immunity(self, duration: int):
        """Activate health immunity for the player"""
        self.health_immunity_active = True
        self.health_immunity_timer = duration

    def remove_health_immunity(self):
        """Remove health immunity from the player"""
        self.health_immunity_active = False
        self.health_immunity_timer = 0


if __name__ == "__main__":
    game = Game()
    game.run()
