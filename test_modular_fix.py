#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test the modular AI generator fix for difficulty-based wave calculation
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_modular_wave_calculation():
    """Test that modular AI generator uses difficulty-based wave calculation"""
    print("Testing modular AI generator wave calculation fix...")
    
    try:
        from ai.modular_ai_generator import ModularAIGenerator
        
        generator = ModularAIGenerator()
        
        # Test the helper method directly
        print("\nTesting _calculate_base_waves_from_difficulty method:")
        test_difficulties = [1, 10, 25, 50, 75, 100]
        
        for difficulty in test_difficulties:
            base_waves = generator._calculate_base_waves_from_difficulty(difficulty)
            print(f"  Difficulty {difficulty:3d}: {base_waves:3d} base waves")
            
            # Verify key targets
            if difficulty == 1 and base_waves <= 10:
                print(f"    GOOD: Difficulty 1 gives {base_waves} waves (target: <= 10)")
            elif difficulty == 1:
                print(f"    BAD: Difficulty 1 gives {base_waves} waves (should be <= 10)")
                
            if difficulty == 50 and 40 <= base_waves <= 60:
                print(f"    GOOD: Difficulty 50 gives {base_waves} waves (target: 40-60)")
            elif difficulty == 50:
                print(f"    BAD: Difficulty 50 gives {base_waves} waves (should be 40-60)")
        
        return True
        
    except Exception as e:
        print(f"Error testing modular AI generator: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_formula_consistency():
    """Test that both generators use the same formula"""
    print("\nTesting formula consistency between generators...")
    
    try:
        from ai.modular_ai_generator import ModularAIGenerator
        from ai.adaptive_config_generator import AdaptiveConfigGenerator
        
        modular_gen = ModularAIGenerator()
        adaptive_gen = AdaptiveConfigGenerator()
        
        print("Comparing wave calculations:")
        print("Difficulty | Modular | Adaptive | Match?")
        print("-" * 40)
        
        all_match = True
        for difficulty in [1, 5, 10, 25, 50, 75, 100]:
            modular_waves = modular_gen._calculate_base_waves_from_difficulty(difficulty)
            adaptive_waves = adaptive_gen._calculate_base_waves_from_difficulty(difficulty)
            
            match = modular_waves == adaptive_waves
            all_match = all_match and match
            
            print(f"{difficulty:8d} | {modular_waves:7d} | {adaptive_waves:8d} | {'YES' if match else 'NO'}")
        
        if all_match:
            print("\nGOOD: Both generators use the same formula")
        else:
            print("\nBAD: Generators use different formulas")
            
        return all_match
        
    except Exception as e:
        print(f"Error testing formula consistency: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing Modular AI Generator Fix...")
    
    # Test the calculation method
    success1 = test_modular_wave_calculation()
    
    # Test consistency between generators
    success2 = test_formula_consistency()
    
    if success1 and success2:
        print("\nAll tests passed! The fix should work correctly.")
    else:
        print("\nSome tests failed. There may still be issues.")
