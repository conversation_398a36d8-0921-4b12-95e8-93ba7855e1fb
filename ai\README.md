# AI Configuration Generator for Tower Defense Game

This AI system automatically generates balanced tower defense game configurations. There are two main systems:

1. **Standard Config Generator**: Rule-based generation using difficulty parameters
2. **Adaptive Config Generator**: AI-guided generation with procedural reliability based on player performance

## Overview

### Standard Config Generator
Takes a difficulty parameter `D ∈ [0,100]` and generates complete level configurations including:
- **Maps**: Procedurally generated paths and terrain layouts
- **Wave Configurations**: Enemy compositions, spawn rates, and scaling
- **Difficulty Balancing**: Automatic parameter tuning for optimal gameplay

### Adaptive Config Generator (AI-Guided)
Uses Azure OpenAI to analyze player performance and guide configuration generation:
- **Performance Analysis**: Analyzes win rate, resource efficiency, tower diversity
- **AI Guidance**: Uses GPT-4 to suggest strategic adjustments and enemy compositions
- **Procedural Reliability**: AI provides strategy, procedural systems ensure playable results
- **Smart Fallback**: Works even if AI API is unavailable

## Algorithm Implementation

The system follows the exact algorithm structure provided:

### 1. Difficulty Factor Derivation
```python
complexity = D/100
obstacle_density = 0.1 + 0.4*(D/100)
base_count = 5 + 25*(D/100)
base_delay = 120 - 100*(D/100)
health_scale = 1 + 0.25*(D/100)
speed_scale = 1 + 0.05*(D/100)
```

### 2. Path Generation
- Uses DFS-backtracker maze algorithm
- Biases straight vs turn decisions by complexity factor
- Retries until achieving sufficient path length
- Records path as list of (x,y) coordinates

### 3. Terrain Grid Building
- Initializes with grass terrain
- Marks path cells as roads
- Places obstacles based on obstacle_density
- Creates strategic variety with water, forest, sand, and rocks

### 4. Wave Parameter Computation
For each wave `i = 1...N`:
```python
count = base_count + lookup_enemy_increase(i)*(1+0.5*(D/100))*(i-1)
delay = max(min_delay, base_delay - lookup_delay_reduction(i)*(1+0.5*(D/100)))
hp_mult = min(max_hp, 1+health_scale*(i-1))
spd_mult = min(max_spd, 1+speed_scale*(i-1))
```

## Setup

### Prerequisites

Install required dependencies:
```bash
pip install -r requirements.txt
```

This will install:
- `pygame>=2.0.0` - For the game engine
- `openai>=1.0.0` - For AI-powered adaptive generation

### Azure OpenAI Setup (For Adaptive Generator)

To use the adaptive config generator, you need an Azure OpenAI API key:

1. Get an Azure OpenAI API key from the [Azure Portal](https://portal.azure.com/)
2. Set the environment variable:

**Windows:**
```cmd
set AZURE_OPENAI_API_KEY=your_api_key_here
```

**Linux/macOS:**
```bash
export AZURE_OPENAI_API_KEY=your_api_key_here
```

**Or alternatively, you can use a regular OpenAI API key:**
```bash
export OPENAI_API_KEY=your_api_key_here
```

## Files

### Standard Config Generator
- `make_specific_config.py` - Rule-based configuration generator
- `generate_config.py` - Basic generation examples

### Adaptive Config Generator
- `adaptive_config_generator.py` - AI-guided adaptive configuration generator
- `performance_data/` - Sample performance data for testing

### Legacy Files
- `ai_game.py` - AI gameplay analysis (existing)
- `path_model.py` - Path analysis models (existing)

## Usage

### Standard Config Generator

```python
from make_specific_config import ConfigGenerator

generator = ConfigGenerator()

# Generate configuration for difficulty 50
config = generator.generate_config(difficulty=50)

# Save to file
config = generator.generate_config(
    difficulty=75, 
    output_path="config/hard_level.json"
)
```

### Adaptive Config Generator

#### Interactive Mode (Recommended)

```bash
cd ai
python adaptive_config_generator.py
```

This will start an interactive session where you can:
1. Load performance data from files
2. Enter performance data manually
3. Generate personalized configurations

#### Programmatic Usage

```python
from adaptive_config_generator import AdaptiveConfigGenerator, PlayerPerformance

# Create performance data
performance = PlayerPerformance(
    win_flag=False,
    lives_remaining=5,
    starting_lives=20,
    resources_spent=800,
    resources_earned=1000,
    towers_built={"basic": 4, "cannon": 2, "sniper": 1},
    previous_config=previous_game_config
)

# Generate adaptive configuration
generator = AdaptiveConfigGenerator()
config = generator.generate_adaptive_config(
    performance, 
    output_path="config/adaptive_level.json"
)
```

### Performance Data Format

Performance data should be saved as JSON files in the `performance_data/` directory:

```json
{
  "win_flag": false,
  "lives_remaining": 5,
  "starting_lives": 20,
  "resources_spent": 800,
  "resources_earned": 1000,
  "towers_built": {
    "basic": 4,
    "cannon": 2,
    "sniper": 1
  },
  "config_file_path": "../config/tower_defense_game.json",
  "timestamp": "2024-06-25T15:30:00",
  "final_wave": 12
}
```

### Batch Generation for Different Scenarios

```python
# Generate configurations for different difficulty levels
difficulties = [10, 30, 50, 70, 90]

for difficulty in difficulties:
    config = generator.generate_config(
        difficulty=difficulty,
        output_path=f"config/level_d{difficulty}.json"
    )
    print(f"Generated level with complexity {config['_generation_metadata']['difficulty_factors']['complexity']}")
```

## Configuration Structure

The generated configurations follow the same JSON schema as the main game config:

```json
{
  "game_config": {
    "starting_money": 20,
    "starting_lives": 20
  },
  "wave_config": {
    "spawn_config": { "base_enemy_count": 5, "base_spawn_delay": 120 },
    "wave_compositions": { "1-5": [["BasicEnemy", 0.8], ["FastEnemy", 0.2]] },
    "boss_waves": { "20": "TimeLordBoss", "25": "ShadowKing" }
  },
  "map_config": {
    "default_map": {
      "width": 20, "height": 15,
      "terrain": [[2,2,2,...], [2,0,0,...]],
      "path": [[1,5], [2,5], [3,5], ...]
    }
  },
  "_generation_metadata": {
    "difficulty": 50,
    "difficulty_factors": { "complexity": 0.5, "obstacle_density": 0.3 }
  }
}
```

## Difficulty Scaling Examples

| Difficulty | Complexity | Obstacle Density | Base Count | Path Length Range |
|------------|------------|------------------|------------|-------------------|
| 0 (Easy)   | 0.0        | 0.1              | 5          | 30-35             |
| 25         | 0.25       | 0.2              | 11.25      | 35-40             |
| 50 (Normal)| 0.5        | 0.3              | 17.5       | 40-45             |
| 75         | 0.75       | 0.4              | 23.75      | 45-50             |
| 100 (Hard) | 1.0        | 0.5              | 30         | 50-55             |

## Features

### Dynamic Path Generation
- **DFS-Backtracker**: Creates organic, strategic paths
- **Complexity Biasing**: Higher difficulty = more turns and tactical positioning
- **Length Optimization**: Ensures sufficient strategic depth

### Balanced Wave Progression
- **Enemy Type Introduction**: Gradual introduction of new enemy types
- **Scaling Formulas**: Mathematically balanced difficulty curves
- **Boss Distribution**: Strategic placement of ultimate bosses

### Terrain Variety
- **Obstacle Placement**: Random but balanced terrain features
- **Strategic Elements**: Water, forest, sand provide tactical variety
- **Tower Positioning**: Ensures sufficient buildable space

### Metadata Tracking
- **Generation Parameters**: Full record of generation settings
- **Quality Metrics**: Built-in configuration quality assessment
- **Reproducibility**: Deterministic generation with known parameters

## Integration with Game

To use generated configurations in the main game:

1. Generate configuration file using the AI system
2. Copy to `config/` directory
3. Modify game startup to load the generated config
4. The game will automatically use the new map and wave settings

## Future Enhancements

The system is designed to be extensible for:
- **Machine Learning Optimization**: Training AI to optimize difficulty curves
- **Player Behavior Analysis**: Adapting generation based on player performance
- **A/B Testing**: Generating multiple variants for testing
- **Real-time Adjustment**: Dynamic difficulty adjustment during gameplay

This AI configuration generator provides a solid foundation for creating balanced, engaging tower defense experiences across all difficulty levels. 