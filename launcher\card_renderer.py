"""
Card Renderer for Tower Defense Game Launcher
Handles drawing of configuration cards
"""

import pygame
from typing import Dict, List, Any


class CardRenderer:
    """Handles drawing of configuration cards"""
    
    def __init__(self, colors, fonts):
        """Initialize with color scheme and fonts"""
        self.colors = colors
        self.fonts = fonts
    
    def draw_level_cards(self, screen, navigation_manager, config_manager, event_handler, start_y: int, available_height: int):
        """Draw level selection cards in an elegant grid"""
        card_width = 250
        card_height = 120
        padding = 20
        margin = 40
        
        # Get current configs to display
        current_configs = config_manager.get_current_config_list(navigation_manager)
        
        # Calculate grid layout
        cards_per_row = (screen.get_width() - 2 * margin) // (card_width + padding)
        rows_visible = available_height // (card_height + padding)
        
        # Draw visible cards
        for i in range(min(len(current_configs), cards_per_row * rows_visible)):
            config_index = i + navigation_manager.scroll_offset
            if config_index >= len(current_configs):
                break
                
            config = current_configs[config_index]
            
            # Card position
            row = i // cards_per_row
            col = i % cards_per_row
            
            x = margin + col * (card_width + padding)
            y = start_y + row * (card_height + padding)
            
            # Check if this card is being pressed
            is_pressed = event_handler.pressed_card == config_index
            
            # Apply press effect (smaller size and offset)
            press_offset = 2 if is_pressed else 0
            actual_x = x + press_offset
            actual_y = y + press_offset
            actual_width = card_width - (press_offset * 2)
            actual_height = card_height - (press_offset * 2)
            
            # Card background
            card_color = self.colors['CARD_BG']
            if is_pressed:
                # Darker color when pressed
                card_color = (int(self.colors['CARD_BG'][0] * 0.9), int(self.colors['CARD_BG'][1] * 0.9), int(self.colors['CARD_BG'][2] * 0.9))
            
            if config_index == navigation_manager.selected_config:
                # Draw selection highlight
                pygame.draw.rect(screen, self.colors['SELECTED_BG'], (x-2, y-2, card_width+4, card_height+4))
                pygame.draw.rect(screen, self.colors['ACCENT_BLUE'], (x-2, y-2, card_width+4, card_height+4), 2)
            
            pygame.draw.rect(screen, card_color, (actual_x, actual_y, actual_width, actual_height))
            pygame.draw.rect(screen, self.colors['BORDER_LIGHT'], (actual_x, actual_y, actual_width, actual_height), 1)
            
            # Level name
            name_text = self.fonts['menu'].render(config['name'][:25] + ("..." if len(config['name']) > 25 else ""), True, self.colors['TEXT_PRIMARY'])
            screen.blit(name_text, (actual_x + 15, actual_y + 15))
            
            # Difficulty indicator
            difficulty_text = self.fonts['small'].render(f"Difficulty: {config['difficulty']}", True, self.colors['TEXT_SECONDARY'])
            screen.blit(difficulty_text, (actual_x + 15, actual_y + 40))
            
            # Difficulty bar (modern style)
            bar_x, bar_y = actual_x + 15, actual_y + 55
            bar_width, bar_height = actual_width - 30, 6
            self._draw_difficulty_bar(screen, bar_x, bar_y, config['difficulty'], bar_width, bar_height)
            
            # AI/Variant indicators
            badge_x = actual_x + actual_width - 50
            badge_y = actual_y + 10
            
            if config.get('is_variant', False):
                # Variant badge (purple)
                pygame.draw.rect(screen, (138, 43, 226), (badge_x, badge_y, 40, 18), border_radius=9)
                variant_text = self.fonts['small'].render("VAR", True, self.colors['CARD_BG'])
                variant_rect = variant_text.get_rect(center=(badge_x + 20, badge_y + 9))
                screen.blit(variant_text, variant_rect)
                
                # Completed indicator if applicable
                if config.get('variant_completed', False):
                    completed_x = badge_x - 45
                    pygame.draw.rect(screen, self.colors['ACCENT_GREEN'], (completed_x, badge_y, 35, 18), border_radius=9)
                    check_text = self.fonts['small'].render("✓", True, self.colors['CARD_BG'])
                    check_rect = check_text.get_rect(center=(completed_x + 17, badge_y + 9))
                    screen.blit(check_text, check_rect)
                    
            elif config['is_adaptive']:
                # AI badge (blue)
                pygame.draw.rect(screen, self.colors['ACCENT_BLUE'], (badge_x, badge_y, 35, 18), border_radius=9)
                ai_text = self.fonts['small'].render("AI", True, self.colors['CARD_BG'])
                ai_rect = ai_text.get_rect(center=(badge_x + 17, badge_y + 9))
                screen.blit(ai_text, ai_rect)
            
            # Description (truncated)
            desc_text = self.fonts['small'].render(config['description'][:35] + ("..." if len(config['description']) > 35 else ""), True, self.colors['TEXT_SECONDARY'])
            screen.blit(desc_text, (actual_x + 15, actual_y + 80))
            
            # Show variant count indicator for main menu base levels
            if navigation_manager.current_view == "main" and not config.get('is_variant'):
                # Check if this base level has variants
                base_level = config_manager.get_base_level_for_config(config)
                has_variants = base_level in config_manager.variants and len(config_manager.variants[base_level]) > 0
                
                if has_variants:
                    # Show variant count in bottom right
                    variant_count = len(config_manager.variants[base_level])
                    count_text = self.fonts['small'].render(f"{variant_count} variants", True, self.colors['TEXT_SECONDARY'])
                    count_rect = count_text.get_rect(right=actual_x + actual_width - 15, bottom=actual_y + actual_height - 10)
                    screen.blit(count_text, count_rect)
            elif navigation_manager.current_view == "variants":
                # In variants view, show play button
                play_btn_x = actual_x + actual_width - 60
                play_btn_y = actual_y + actual_height - 35
                pygame.draw.rect(screen, self.colors['ACCENT_GREEN'], (play_btn_x, play_btn_y, 45, 25), border_radius=12)
                play_text = self.fonts['small'].render("PLAY", True, self.colors['CARD_BG'])
                play_rect = play_text.get_rect(center=(play_btn_x + 22, play_btn_y + 12))
                screen.blit(play_text, play_rect)
    
    def _draw_difficulty_bar(self, screen, x: int, y: int, difficulty: int, width: int = 100, height: int = 6):
        """Draw a modern, sleek difficulty bar"""
        if not isinstance(difficulty, (int, float)):
            difficulty = 50
        difficulty = max(0, min(100, int(difficulty)))
        
        # Background bar
        pygame.draw.rect(screen, self.colors['BORDER_LIGHT'], (x, y, width, height), border_radius=height//2)
        
        # Progress fill
        fill_width = int((difficulty / 100) * width)
        if fill_width > 0:
            if difficulty <= 30:
                color = self.colors['ACCENT_GREEN']
            elif difficulty <= 70:
                color = self.colors['ACCENT_ORANGE']
            else:
                color = (220, 53, 69)  # Red for high difficulty
            
            pygame.draw.rect(screen, color, (x, y, fill_width, height), border_radius=height//2)
