"""
Wave Configuration Module for Tower Defense Config Generation

Contains wave composition and enemy configuration logic.
"""

from typing import List, Tuple, Dict
import random


class EnemyTiers:
    """Enemy availability by wave tiers"""

    TIERS = {
        1: ["BasicEnemy", "FastEnemy"],
        6: ["TankEnemy"],
        11: ["FlyingEnemy"],
        16: ["ShieldedEnemy", "ArmoredEnemy"],
        21: ["InvisibleEnemy", "EnergyShieldEnemy", "GroundedEnemy"],
        31: ["RegeneratingEnemy", "TeleportingEnemy", "FireElementalEnemy", "ToxicEnemy"],
        41: ["SplittingEnemy", "PhaseShiftEnemy", "BlastProofEnemy"],
        51: ["SpectralEnemy", "CrystallineEnemy"],
        61: ["ToxicMutantEnemy", "VoidEnemy", "AdaptiveEnemy"]
    }


def get_standard_first_5_waves_config() -> List[Tuple[str, float]]:
    """
    Returns the standardized configuration for waves 1-5 that should be used 
    regardless of difficulty or AI modifications.

    Returns:
        List of (EnemyType, probability) tuples for waves 1-5
    """
    return [
        ("BasicEnemy", 0.8),
        ("FastEnemy", 0.2)
    ]


def assign_enemy_compositions(wave_num: int, difficulty_factors: Dict[str, float]) -> List[Tuple[str, float]]:
    """
    Assign enemy compositions for a wave

    Args:
        wave_num: Wave number
        difficulty_factors: Derived difficulty factors

    Returns:
        List of (EnemyType, probability) tuples
    """
    # STANDARDIZED: Always use the same configuration for waves 1-5
    if wave_num <= 5:
        return get_standard_first_5_waves_config()

    D = difficulty_factors['normalized_difficulty']

    # Get available enemy types for this wave
    available_enemies = []
    for tier_wave, enemies in EnemyTiers.TIERS.items():
        if wave_num >= tier_wave:
            available_enemies.extend(enemies)

    if not available_enemies:
        available_enemies = ["BasicEnemy", "FastEnemy"]

    # Base composition weights for waves 6+
    if wave_num <= 10:
        base_weights = {"BasicEnemy": 0.6, "FastEnemy": 0.3, "TankEnemy": 0.1}
    elif wave_num <= 15:
        base_weights = {"BasicEnemy": 0.4, "FastEnemy": 0.3,
                        "TankEnemy": 0.2, "FlyingEnemy": 0.1}
    elif wave_num <= 20:
        base_weights = {"BasicEnemy": 0.25, "FastEnemy": 0.2, "TankEnemy": 0.2,
                        "FlyingEnemy": 0.15, "ShieldedEnemy": 0.15, "ArmoredEnemy": 0.05}
    else:
        # Late game: distribute weight among all available enemies
        equal_weight = 1.0 / len(available_enemies)
        base_weights = {enemy: equal_weight for enemy in available_enemies}

    # Bias heavier types upward by difficulty
    final_weights = {}
    heavy_enemies = ["TankEnemy", "ArmoredEnemy", "SpectralEnemy", "CrystallineEnemy",
                     "ToxicMutantEnemy", "VoidEnemy", "AdaptiveEnemy"]

    for enemy in available_enemies:
        weight = base_weights.get(enemy, 0.05)
        if enemy in heavy_enemies:
            # Increase heavy enemy weight by difficulty
            weight *= (1 + D * 0.5)
        final_weights[enemy] = weight

    # Normalize weights
    total_weight = sum(final_weights.values())
    normalized_weights = [(enemy, weight / total_weight)
                          for enemy, weight in final_weights.items()]

    return normalized_weights


def group_wave_compositions(total_waves: int, difficulty_factors: Dict[str, float]) -> Dict[str, List[Tuple[str, float]]]:
    """
    Group waves with similar compositions into efficient ranges

    Args:
        total_waves: Total number of waves
        difficulty_factors: Derived difficulty factors

    Returns:
        Dictionary with wave range keys and composition values
    """
    wave_compositions = {}

    # STANDARDIZED: Always use the same configuration for waves 1-5
    wave_compositions["1-5"] = get_standard_first_5_waves_config()

    # Define logical wave groupings based on enemy availability (starting from wave 6)
    wave_ranges = [
        (6, 10),   # Add Tank
        (11, 15),  # Add Flying
        (16, 20),  # Add Shielded + Armored
        (21, 30),  # Add Invisible + Energy Shield + Grounded
        (31, 40),  # Add Regen + Teleport + Fire + Toxic
        (41, 50),  # Add Splitting + Phase + Blast Proof
        (51, 60),  # Add Spectral + Crystalline
        (61, 80)   # Add Toxic Mutant + Void + Adaptive
    ]

    for start_wave, end_wave in wave_ranges:
        # Adjust end wave if total waves is less than 80
        if start_wave > total_waves:
            break
        actual_end = min(end_wave, total_waves)

        # Use the middle wave of the range for composition calculation
        representative_wave = (start_wave + actual_end) // 2
        composition = assign_enemy_compositions(
            representative_wave, difficulty_factors)

        range_key = f"{start_wave}-{actual_end}" if start_wave != actual_end else str(
            start_wave)
        wave_compositions[range_key] = composition

    return wave_compositions


def generate_boss_waves(total_waves: int) -> Dict[str, str]:
    """
    Generate boss wave configuration based on total wave count

    Boss waves only spawn boss enemies (no regular enemies).
    Boss count is determined by AI generator based on difficulty and performance.

    Args:
        total_waves: Total number of waves

    Returns:
        Dictionary mapping wave numbers to boss types
    """
    boss_types = ["SpeedBoss", "MegaBoss", "TimeLordBoss", "ShadowKing",
                  "NecromancerBoss", "CrystalOverlord"]
    boss_waves = {}

    # Scale boss frequency based on total waves
    if total_waves <= 30:
        # Fewer waves: bosses every 10 waves
        boss_frequency = 10
    elif total_waves <= 60:
        # Medium waves: bosses every 8-10 waves
        boss_frequency = 8
    else:
        # Many waves: bosses every 5-8 waves
        boss_frequency = 5

    # Place bosses at strategic intervals
    boss_wave_numbers = []
    for i in range(boss_frequency, total_waves + 1, boss_frequency):
        boss_wave_numbers.append(i)

    # Always have a final boss
    if total_waves not in boss_wave_numbers:
        boss_wave_numbers.append(total_waves)

    # Assign boss types
    for i, wave_num in enumerate(boss_wave_numbers):
        boss_type = boss_types[i % len(boss_types)]
        # Final wave always gets TimeLordBoss (strongest)
        if wave_num == total_waves:
            boss_type = "TimeLordBoss"
        boss_waves[str(wave_num)] = boss_type

    return boss_waves


def generate_special_rounds(total_waves: int) -> Dict[str, Dict[str, float]]:
    """
    Generate special round configurations

    Args:
        total_waves: Total number of waves

    Returns:
        Dictionary mapping wave numbers to special round multipliers
    """
    special_rounds = {}

    # Scale special round frequency based on total waves
    if total_waves <= 30:
        special_frequency = 10
    elif total_waves <= 60:
        special_frequency = 8
    else:
        special_frequency = 5

    # Generate special rounds
    for wave_num in range(special_frequency, total_waves + 1, special_frequency):
        # Intensity increases with wave number
        intensity = 1.0 + (wave_num / total_waves) * 2.0

        special_rounds[str(wave_num)] = {
            "enemy_multiplier": 1.5 + (wave_num // special_frequency) * 0.5,
            "spawn_delay_multiplier": max(0.2, 0.8 - (wave_num // special_frequency) * 0.1)
        }

    return special_rounds


def generate_wave_progression_config(total_waves: int) -> Dict[str, Dict]:
    """
    Generate wave progression configuration based on total wave count

    Args:
        total_waves: Total number of waves

    Returns:
        Dictionary containing wave progression settings
    """
    # Scale progression based on total waves
    wave_chunk_size = max(5, total_waves // 8)  # Divide into ~8 chunks

    # Enemy increase per round
    enemy_increase_ranges = {}
    for i in range(0, total_waves, wave_chunk_size):
        start_wave = i + 1
        end_wave = min(i + wave_chunk_size, total_waves)
        increase_amount = (i // wave_chunk_size) + 1

        range_key = f"{start_wave}-{end_wave}" if start_wave != end_wave else str(
            start_wave)
        enemy_increase_ranges[range_key] = increase_amount

    # Spawn delay reduction per round
    delay_reduction_ranges = {}
    for i in range(0, total_waves, wave_chunk_size):
        start_wave = i + 1
        end_wave = min(i + wave_chunk_size, total_waves)
        reduction_amount = 5 + (i // wave_chunk_size) * \
            3  # Progressive reduction

        range_key = f"{start_wave}-{end_wave}" if start_wave != end_wave else str(
            start_wave)
        delay_reduction_ranges[range_key] = reduction_amount

    return {
        "enemy_increase_per_round": {
            "wave_ranges": enemy_increase_ranges,
            "default": 1
        },
        "spawn_delay_reduction_per_round": {
            "wave_ranges": delay_reduction_ranges,
            "default": 5
        },
        "special_rounds": generate_special_rounds(total_waves)
    }
