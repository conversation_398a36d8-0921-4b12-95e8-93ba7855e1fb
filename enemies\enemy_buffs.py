"""
Enemy Buff System - Stackable and configurable buffs for enemies
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Set
from enum import Enum
import pygame
import math

class BuffType(Enum):
    """Types of buffs that can be applied to enemies"""
    INVISIBILITY = "invisibility"
    ANTI_EXPLOSIVE = "anti_explosive"
    FLYING = "flying"
    ARMOR = "armor"
    SPEED_BOOST = "speed_boost"
    REGENERATION = "regeneration"
    SPELL_RESISTANCE = "spell_resistance"
    DAMAGE_REFLECTION = "damage_reflection"
    TELEPORTATION = "teleportation"
    SPLIT_ON_DEATH = "split_on_death"
    TOXICITY = "toxicity"
    ENERGY_SHIELD = "energy_shield"
    FIRE_IMMUNITY = "fire_immunity"
    FREEZE_IMMUNITY = "freeze_immunity"
    POISON_IMMUNITY = "poison_immunity"
    GROUND_IMMUNITY = "ground_immunity"
    BERSERKER = "berserker"
    CAMOUFLAGE = "camouflage"
    PHASE_SHIFT = "phase_shift"
    ADAPTIVE_RESISTANCE = "adaptive_resistance"

class EnemyBuff(ABC):
    """Base class for all enemy buffs"""
    
    def __init__(self, buff_type: BuffType, config: Dict[str, Any]):
        self.buff_type = buff_type
        self.config = config
        self.name = config.get('name', buff_type.value)
        self.description = config.get('description', f'{buff_type.value} buff')
        self.icon = config.get('icon', '🔮')
        self.stackable = config.get('stackable', False)
        self.max_stacks = config.get('max_stacks', 1)
        self.current_stacks = 1
        self.duration = config.get('duration', -1)  # -1 = permanent
        self.remaining_duration = self.duration
        self.active = True
        
        # Visual effects
        self.color = tuple(config.get('color', [255, 255, 255]))
        self.glow_radius = config.get('glow_radius', 0)
        self.animation_speed = config.get('animation_speed', 1.0)
        self.animation_timer = 0
    
    @abstractmethod
    def apply_to_enemy(self, enemy):
        """Apply the buff effects to an enemy"""
        pass
    
    @abstractmethod
    def remove_from_enemy(self, enemy):
        """Remove the buff effects from an enemy"""
        pass
    
    def update(self, enemy):
        """Update buff state and duration"""
        self.animation_timer += self.animation_speed
        
        if self.duration > 0:
            self.remaining_duration -= 1
            if self.remaining_duration <= 0:
                self.active = False
                return False
        
        return True
    
    def can_stack_with(self, other_buff) -> bool:
        """Check if this buff can stack with another buff"""
        return (self.buff_type == other_buff.buff_type and 
                self.stackable and 
                self.current_stacks < self.max_stacks)
    
    def add_stack(self):
        """Add a stack to this buff"""
        if self.current_stacks < self.max_stacks:
            self.current_stacks += 1
            # Reset duration on new stack
            if self.duration > 0:
                self.remaining_duration = self.duration
            return True
        return False
    
    def draw_effect(self, screen: pygame.Surface, enemy):
        """Draw visual effects for this buff"""
        if not self.active:
            return
        
        # Draw glow effect
        if self.glow_radius > 0:
            glow_alpha = int(128 + 127 * math.sin(self.animation_timer * 0.1))
            glow_surface = pygame.Surface((self.glow_radius * 2, self.glow_radius * 2), pygame.SRCALPHA)
            pygame.draw.circle(glow_surface, (*self.color, glow_alpha), 
                             (self.glow_radius, self.glow_radius), self.glow_radius)
            screen.blit(glow_surface, (enemy.x - self.glow_radius, enemy.y - self.glow_radius))
    
    def draw_icon(self, screen: pygame.Surface, x: int, y: int, size: int = 16):
        """Draw buff icon at specified position"""
        if not self.active:
            return
        
        # Draw icon background
        pygame.draw.circle(screen, self.color, (x, y), size // 2)
        pygame.draw.circle(screen, (0, 0, 0), (x, y), size // 2, 2)
        
        # Draw stack indicator if stackable
        if self.stackable and self.current_stacks > 1:
            font = pygame.font.Font(None, 12)
            text = font.render(str(self.current_stacks), True, (255, 255, 255))
            screen.blit(text, (x - 3, y - 3))

class InvisibilityBuff(EnemyBuff):
    """Makes enemy invisible to most towers"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.INVISIBILITY, config)
        self.detection_range = config.get('detection_range', 80)
        self.transparency = config.get('transparency', 0.3)
    
    def apply_to_enemy(self, enemy):
        enemy.invisible = True
        enemy.detection_range = self.detection_range
        enemy.transparency = self.transparency
    
    def remove_from_enemy(self, enemy):
        enemy.invisible = False
        if hasattr(enemy, 'detection_range'):
            delattr(enemy, 'detection_range')
        if hasattr(enemy, 'transparency'):
            delattr(enemy, 'transparency')

class AntiExplosiveBuff(EnemyBuff):
    """Provides resistance or immunity to explosive damage"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.ANTI_EXPLOSIVE, config)
        self.damage_reduction = config.get('damage_reduction', 0.8)  # 80% reduction
        self.explosive_towers = config.get('explosive_towers', 
                                         ['explosive', 'cannon', 'missile', 'destroyer'])
    
    def apply_to_enemy(self, enemy):
        if not hasattr(enemy, 'damage_resistances'):
            enemy.damage_resistances = {}
        
        for tower_type in self.explosive_towers:
            enemy.damage_resistances[tower_type] = self.damage_reduction * self.current_stacks
    
    def remove_from_enemy(self, enemy):
        if hasattr(enemy, 'damage_resistances'):
            for tower_type in self.explosive_towers:
                enemy.damage_resistances.pop(tower_type, None)

class FlyingBuff(EnemyBuff):
    """Makes enemy fly and only targetable by anti-air towers"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.FLYING, config)
        self.altitude = config.get('altitude', 10)
        self.hover_speed = config.get('hover_speed', 0.2)
        self.allowed_towers = config.get('allowed_towers', ['antiair', 'missile', 'destroyer'])
    
    def apply_to_enemy(self, enemy):
        enemy.flying = True
        enemy.altitude = self.altitude
        enemy.hover_speed = self.hover_speed
        enemy.hover_offset = 0
        enemy.allowed_towers = self.allowed_towers
    
    def remove_from_enemy(self, enemy):
        enemy.flying = False
        for attr in ['altitude', 'hover_speed', 'hover_offset', 'allowed_towers']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)

class ArmorBuff(EnemyBuff):
    """Provides damage reduction against all attacks"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.ARMOR, config)
        self.damage_reduction = config.get('damage_reduction', 0.3)  # 30% reduction
    
    def apply_to_enemy(self, enemy):
        if not hasattr(enemy, 'armor_reduction'):
            enemy.armor_reduction = 0
        enemy.armor_reduction = min(0.9, enemy.armor_reduction + self.damage_reduction * self.current_stacks)
    
    def remove_from_enemy(self, enemy):
        if hasattr(enemy, 'armor_reduction'):
            enemy.armor_reduction = max(0, enemy.armor_reduction - self.damage_reduction * self.current_stacks)

class SpeedBoostBuff(EnemyBuff):
    """Increases enemy movement speed"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.SPEED_BOOST, config)
        self.speed_multiplier = config.get('speed_multiplier', 1.5)
    
    def apply_to_enemy(self, enemy):
        if not hasattr(enemy, 'speed_multipliers'):
            enemy.speed_multipliers = []
        enemy.speed_multipliers.append(self.speed_multiplier)
        enemy.speed *= self.speed_multiplier
        enemy.base_speed *= self.speed_multiplier
    
    def remove_from_enemy(self, enemy):
        if hasattr(enemy, 'speed_multipliers'):
            if self.speed_multiplier in enemy.speed_multipliers:
                enemy.speed_multipliers.remove(self.speed_multiplier)
                enemy.speed /= self.speed_multiplier
                enemy.base_speed /= self.speed_multiplier

class RegenerationBuff(EnemyBuff):
    """Regenerates health over time"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.REGENERATION, config)
        self.heal_rate = config.get('heal_rate', 1)  # HP per second
        self.heal_interval = config.get('heal_interval', 60)  # frames
        self.heal_timer = 0
    
    def apply_to_enemy(self, enemy):
        enemy.regenerating = True
        enemy.regen_rate = self.heal_rate * self.current_stacks
        enemy.regen_interval = self.heal_interval
        enemy.regen_timer = 0
    
    def remove_from_enemy(self, enemy):
        enemy.regenerating = False
        for attr in ['regen_rate', 'regen_interval', 'regen_timer']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)
    
    def update(self, enemy):
        result = super().update(enemy)
        if hasattr(enemy, 'regen_timer'):
            enemy.regen_timer += 1
            if enemy.regen_timer >= enemy.regen_interval:
                enemy.health = min(enemy.max_health, enemy.health + enemy.regen_rate)
                enemy.regen_timer = 0
        return result

class SpellResistanceBuff(EnemyBuff):
    """Provides resistance to magical tower types"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.SPELL_RESISTANCE, config)
        self.resistance_amount = config.get('resistance_amount', 0.5)  # 50% resistance
        self.magic_towers = config.get('magic_towers', ['laser', 'lightning', 'ice', 'flame'])
    
    def apply_to_enemy(self, enemy):
        if not hasattr(enemy, 'damage_resistances'):
            enemy.damage_resistances = {}
        for tower_type in self.magic_towers:
            enemy.damage_resistances[tower_type] = self.resistance_amount * self.current_stacks
    
    def remove_from_enemy(self, enemy):
        if hasattr(enemy, 'damage_resistances'):
            for tower_type in self.magic_towers:
                enemy.damage_resistances.pop(tower_type, None)

class DamageReflectionBuff(EnemyBuff):
    """Reflects a portion of damage back to towers"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.DAMAGE_REFLECTION, config)
        self.reflection_percentage = config.get('reflection_percentage', 0.2)  # 20% reflection
        self.reflection_range = config.get('reflection_range', 100)
    
    def apply_to_enemy(self, enemy):
        enemy.damage_reflection = True
        enemy.reflection_percentage = self.reflection_percentage * self.current_stacks
        enemy.reflection_range = self.reflection_range
    
    def remove_from_enemy(self, enemy):
        enemy.damage_reflection = False
        for attr in ['reflection_percentage', 'reflection_range']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)

class TeleportationBuff(EnemyBuff):
    """Allows enemy to teleport short distances"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.TELEPORTATION, config)
        self.teleport_distance = config.get('teleport_distance', 80)
        self.teleport_cooldown = config.get('teleport_cooldown', 180)  # 3 seconds
    
    def apply_to_enemy(self, enemy):
        enemy.can_teleport = True
        enemy.teleport_distance = self.teleport_distance
        enemy.teleport_cooldown = self.teleport_cooldown
        enemy.teleport_timer = 0
    
    def remove_from_enemy(self, enemy):
        enemy.can_teleport = False
        for attr in ['teleport_distance', 'teleport_cooldown', 'teleport_timer']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)

class SplitOnDeathBuff(EnemyBuff):
    """Enemy splits into smaller enemies on death"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.SPLIT_ON_DEATH, config)
        self.split_count = config.get('split_count', 2)
        self.split_health_ratio = config.get('split_health_ratio', 0.5)
    
    def apply_to_enemy(self, enemy):
        enemy.splits_on_death = True
        enemy.split_count = self.split_count * self.current_stacks
        enemy.split_health_ratio = self.split_health_ratio
    
    def remove_from_enemy(self, enemy):
        enemy.splits_on_death = False
        for attr in ['split_count', 'split_health_ratio']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)

class ToxicityBuff(EnemyBuff):
    """Enemy deals poison damage to nearby towers"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.TOXICITY, config)
        self.toxic_damage = config.get('toxic_damage', 2)
        self.toxic_range = config.get('toxic_range', 60)
        self.toxic_interval = config.get('toxic_interval', 90)  # 1.5 seconds
    
    def apply_to_enemy(self, enemy):
        enemy.is_toxic = True
        enemy.toxic_damage = self.toxic_damage * self.current_stacks
        enemy.toxic_range = self.toxic_range
        enemy.toxic_interval = self.toxic_interval
        enemy.toxic_timer = 0
    
    def remove_from_enemy(self, enemy):
        enemy.is_toxic = False
        for attr in ['toxic_damage', 'toxic_range', 'toxic_interval', 'toxic_timer']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)

class EnergyShieldBuff(EnemyBuff):
    """Provides a regenerating energy shield"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.ENERGY_SHIELD, config)
        self.shield_strength = config.get('shield_strength', 50)
        self.shield_regen_rate = config.get('shield_regen_rate', 1)
        self.shield_regen_delay = config.get('shield_regen_delay', 120)  # 2 seconds
    
    def apply_to_enemy(self, enemy):
        enemy.has_energy_shield = True
        enemy.shield_health = self.shield_strength * self.current_stacks
        enemy.max_shield_health = enemy.shield_health
        enemy.shield_regen_rate = self.shield_regen_rate
        enemy.shield_regen_delay = self.shield_regen_delay
        enemy.shield_regen_timer = 0
    
    def remove_from_enemy(self, enemy):
        enemy.has_energy_shield = False
        for attr in ['shield_health', 'max_shield_health', 'shield_regen_rate', 'shield_regen_delay', 'shield_regen_timer']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)

class FireImmunityBuff(EnemyBuff):
    """Complete immunity to fire damage"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.FIRE_IMMUNITY, config)
    
    def apply_to_enemy(self, enemy):
        if not hasattr(enemy, 'damage_immunities'):
            enemy.damage_immunities = set()
        enemy.damage_immunities.add('flame')
        enemy.fire_immune = True
    
    def remove_from_enemy(self, enemy):
        if hasattr(enemy, 'damage_immunities'):
            enemy.damage_immunities.discard('flame')
        enemy.fire_immune = False

class FreezeImmunityBuff(EnemyBuff):
    """Complete immunity to freeze effects"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.FREEZE_IMMUNITY, config)
    
    def apply_to_enemy(self, enemy):
        if not hasattr(enemy, 'status_immunities'):
            enemy.status_immunities = set()
        enemy.status_immunities.add('freeze')
        enemy.freeze_immune = True
    
    def remove_from_enemy(self, enemy):
        if hasattr(enemy, 'status_immunities'):
            enemy.status_immunities.discard('freeze')
        enemy.freeze_immune = False

class PoisonImmunityBuff(EnemyBuff):
    """Complete immunity to poison effects"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.POISON_IMMUNITY, config)
    
    def apply_to_enemy(self, enemy):
        if not hasattr(enemy, 'damage_immunities'):
            enemy.damage_immunities = set()
        enemy.damage_immunities.add('poison')
        enemy.poison_immune = True
    
    def remove_from_enemy(self, enemy):
        if hasattr(enemy, 'damage_immunities'):
            enemy.damage_immunities.discard('poison')
        enemy.poison_immune = False

class GroundImmunityBuff(EnemyBuff):
    """Immunity to ground-based attacks (like spikes, mines)"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.GROUND_IMMUNITY, config)
    
    def apply_to_enemy(self, enemy):
        if not hasattr(enemy, 'terrain_immunities'):
            enemy.terrain_immunities = set()
        enemy.terrain_immunities.add('ground_effects')
        enemy.ground_immune = True
    
    def remove_from_enemy(self, enemy):
        if hasattr(enemy, 'terrain_immunities'):
            enemy.terrain_immunities.discard('ground_effects')
        enemy.ground_immune = False

class BerserkerBuff(EnemyBuff):
    """Increases speed and damage as health decreases"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.BERSERKER, config)
        self.speed_bonus_per_missing_health = config.get('speed_bonus_per_missing_health', 0.02)
        self.damage_bonus_per_missing_health = config.get('damage_bonus_per_missing_health', 0.03)
    
    def apply_to_enemy(self, enemy):
        enemy.is_berserker = True
        enemy.berserker_speed_bonus = self.speed_bonus_per_missing_health * self.current_stacks
        enemy.berserker_damage_bonus = self.damage_bonus_per_missing_health * self.current_stacks
    
    def remove_from_enemy(self, enemy):
        enemy.is_berserker = False
        for attr in ['berserker_speed_bonus', 'berserker_damage_bonus']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)

class CamouflageBuff(EnemyBuff):
    """Enemy becomes harder to target (reduced accuracy)"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.CAMOUFLAGE, config)
        self.miss_chance = config.get('miss_chance', 0.25)  # 25% miss chance
    
    def apply_to_enemy(self, enemy):
        enemy.has_camouflage = True
        enemy.miss_chance = min(0.8, self.miss_chance * self.current_stacks)  # Max 80% miss chance
    
    def remove_from_enemy(self, enemy):
        enemy.has_camouflage = False
        if hasattr(enemy, 'miss_chance'):
            delattr(enemy, 'miss_chance')

class PhaseShiftBuff(EnemyBuff):
    """Enemy periodically becomes untargetable"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.PHASE_SHIFT, config)
        self.phase_duration = config.get('phase_duration', 60)  # 1 second phased
        self.phase_cooldown = config.get('phase_cooldown', 300)  # 5 second cooldown
    
    def apply_to_enemy(self, enemy):
        enemy.can_phase_shift = True
        enemy.phase_duration = self.phase_duration
        enemy.phase_cooldown = self.phase_cooldown
        enemy.phase_timer = 0
        enemy.is_phased = False
    
    def remove_from_enemy(self, enemy):
        enemy.can_phase_shift = False
        for attr in ['phase_duration', 'phase_cooldown', 'phase_timer', 'is_phased']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)

class AdaptiveResistanceBuff(EnemyBuff):
    """Builds resistance to damage types that hit it repeatedly"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(BuffType.ADAPTIVE_RESISTANCE, config)
        self.resistance_buildup_rate = config.get('resistance_buildup_rate', 0.05)  # 5% per hit
        self.max_resistance = config.get('max_resistance', 0.8)  # 80% max resistance
        self.resistance_decay_rate = config.get('resistance_decay_rate', 0.01)  # 1% decay per second
    
    def apply_to_enemy(self, enemy):
        enemy.has_adaptive_resistance = True
        enemy.adaptive_resistances = {}  # tower_type -> resistance_amount
        enemy.resistance_buildup_rate = self.resistance_buildup_rate * self.current_stacks
        enemy.max_adaptive_resistance = self.max_resistance
        enemy.resistance_decay_rate = self.resistance_decay_rate
    
    def remove_from_enemy(self, enemy):
        enemy.has_adaptive_resistance = False
        for attr in ['adaptive_resistances', 'resistance_buildup_rate', 'max_adaptive_resistance', 'resistance_decay_rate']:
            if hasattr(enemy, attr):
                delattr(enemy, attr)

class EnemyBuffManager:
    """Manages all buffs on an enemy"""
    
    def __init__(self, enemy):
        self.enemy = enemy
        self.buffs: Dict[BuffType, List[EnemyBuff]] = {}
        self.buff_configs = self._load_buff_configs()
    
    def _load_buff_configs(self) -> Dict[str, Any]:
        """Load buff configurations from game config"""
        try:
            from config.game_config import get_balance_config
            config = get_balance_config()
            return config.get('enemy_buffs', {})
        except:
            return {}
    
    def add_buff(self, buff_type: BuffType, config: Optional[Dict[str, Any]] = None) -> bool:
        """Add a buff to the enemy"""
        if config is None:
            config = self.buff_configs.get(buff_type.value, {})
        
        # Ensure config is not None
        if not config:
            config = {}
        
        # INVINCIBILITY SAFEGUARD: Check if adding this buff would make enemy too powerful
        if self._would_make_invincible(buff_type):
            return False
        
        # Create buff instance
        buff = self._create_buff_instance(buff_type, config)
        if not buff:
            return False
        
        # Check if we can stack with existing buffs
        if buff_type in self.buffs:
            existing_buffs = self.buffs[buff_type]
            if buff.stackable:
                # Try to add stack to existing buff
                for existing_buff in existing_buffs:
                    if existing_buff.can_stack_with(buff):
                        existing_buff.add_stack()
                        existing_buff.apply_to_enemy(self.enemy)
                        return True
                
                # If no stackable buff found, add new one if under max
                if len(existing_buffs) < buff.max_stacks:
                    self.buffs[buff_type].append(buff)
                    buff.apply_to_enemy(self.enemy)
                    return True
                return False
            else:
                # Non-stackable buff, replace existing
                self.remove_buff(buff_type)
        
        # Add new buff
        if buff_type not in self.buffs:
            self.buffs[buff_type] = []
        
        self.buffs[buff_type].append(buff)
        buff.apply_to_enemy(self.enemy)
        return True
    
    def _would_make_invincible(self, new_buff_type: BuffType) -> bool:
        """Check if adding this buff would make the enemy too powerful (invincible)"""
        # Count current powerful buffs
        powerful_buffs = [
            BuffType.ARMOR, BuffType.SPELL_RESISTANCE, BuffType.FIRE_IMMUNITY,
            BuffType.FREEZE_IMMUNITY, BuffType.POISON_IMMUNITY, BuffType.ANTI_EXPLOSIVE,
            BuffType.DAMAGE_REFLECTION, BuffType.ADAPTIVE_RESISTANCE
        ]
        
        current_powerful_count = sum(1 for buff_type in powerful_buffs if self.has_buff(buff_type))
        
        # If adding another powerful buff and already have 4+, reject
        if new_buff_type in powerful_buffs and current_powerful_count >= 4:
            return True
        
        # Specific combinations that would be too strong
        dangerous_combinations = [
            {BuffType.ARMOR, BuffType.SPELL_RESISTANCE, BuffType.REGENERATION, BuffType.FIRE_IMMUNITY},
            {BuffType.FLYING, BuffType.INVISIBILITY, BuffType.PHASE_SHIFT},
            {BuffType.ADAPTIVE_RESISTANCE, BuffType.DAMAGE_REFLECTION, BuffType.ENERGY_SHIELD}
        ]
        
        current_buffs = set(self.buffs.keys())
        current_buffs.add(new_buff_type)
        
        for dangerous_combo in dangerous_combinations:
            if dangerous_combo.issubset(current_buffs):
                return True
        
        return False
    
    def remove_buff(self, buff_type: BuffType) -> bool:
        """Remove all buffs of a specific type"""
        if buff_type not in self.buffs:
            return False
        
        for buff in self.buffs[buff_type]:
            buff.remove_from_enemy(self.enemy)
        
        del self.buffs[buff_type]
        return True
    
    def has_buff(self, buff_type: BuffType) -> bool:
        """Check if enemy has a specific buff type"""
        return buff_type in self.buffs and len(self.buffs[buff_type]) > 0
    
    def get_buff_stacks(self, buff_type: BuffType) -> int:
        """Get total stacks for a buff type"""
        if buff_type not in self.buffs:
            return 0
        return sum(buff.current_stacks for buff in self.buffs[buff_type])
    
    def update(self):
        """Update all buffs"""
        buffs_to_remove = []
        
        for buff_type, buff_list in self.buffs.items():
            for buff in buff_list[:]:  # Use slice to avoid modification during iteration
                if not buff.update(self.enemy):
                    buff.remove_from_enemy(self.enemy)
                    buff_list.remove(buff)
            
            # Remove empty buff lists
            if not buff_list:
                buffs_to_remove.append(buff_type)
        
        for buff_type in buffs_to_remove:
            del self.buffs[buff_type]
    
    def draw_effects(self, screen: pygame.Surface):
        """Draw all buff effects"""
        for buff_list in self.buffs.values():
            for buff in buff_list:
                buff.draw_effect(screen, self.enemy)
    
    def draw_buff_indicators(self, screen: pygame.Surface, x: int, y: int):
        """Draw buff icons at specified position"""
        icon_size = 16
        icons_per_row = 4
        current_x = x
        current_y = y
        icon_count = 0
        
        for buff_list in self.buffs.values():
            for buff in buff_list:
                buff.draw_icon(screen, current_x, current_y, icon_size)
                current_x += icon_size + 2
                icon_count += 1
                
                if icon_count % icons_per_row == 0:
                    current_x = x
                    current_y += icon_size + 2
    
    def get_all_buffs(self) -> List[EnemyBuff]:
        """Get all active buffs"""
        all_buffs = []
        for buff_list in self.buffs.values():
            all_buffs.extend(buff_list)
        return all_buffs
    
    def _create_buff_instance(self, buff_type: BuffType, config: Dict[str, Any]) -> Optional[EnemyBuff]:
        """Create a buff instance based on type"""
        buff_classes = {
            BuffType.INVISIBILITY: InvisibilityBuff,
            BuffType.ANTI_EXPLOSIVE: AntiExplosiveBuff,
            BuffType.FLYING: FlyingBuff,
            BuffType.ARMOR: ArmorBuff,
            BuffType.SPEED_BOOST: SpeedBoostBuff,
            BuffType.REGENERATION: RegenerationBuff,
            BuffType.SPELL_RESISTANCE: SpellResistanceBuff,
            BuffType.DAMAGE_REFLECTION: DamageReflectionBuff,
            BuffType.TELEPORTATION: TeleportationBuff,
            BuffType.SPLIT_ON_DEATH: SplitOnDeathBuff,
            BuffType.TOXICITY: ToxicityBuff,
            BuffType.ENERGY_SHIELD: EnergyShieldBuff,
            BuffType.FIRE_IMMUNITY: FireImmunityBuff,
            BuffType.FREEZE_IMMUNITY: FreezeImmunityBuff,
            BuffType.POISON_IMMUNITY: PoisonImmunityBuff,
            BuffType.GROUND_IMMUNITY: GroundImmunityBuff,
            BuffType.BERSERKER: BerserkerBuff,
            BuffType.CAMOUFLAGE: CamouflageBuff,
            BuffType.PHASE_SHIFT: PhaseShiftBuff,
            BuffType.ADAPTIVE_RESISTANCE: AdaptiveResistanceBuff,
        }
        
        buff_class = buff_classes.get(buff_type)
        if buff_class:
            return buff_class(config)
        return None

def apply_buffs_from_config(enemy, buff_config: Dict[str, Any]):
    """Apply buffs to an enemy based on configuration"""
    if not hasattr(enemy, 'buff_manager'):
        enemy.buff_manager = EnemyBuffManager(enemy)
    
    for buff_name, buff_data in buff_config.items():
        try:
            buff_type = BuffType(buff_name)
            enemy.buff_manager.add_buff(buff_type, buff_data)
        except ValueError:
            print(f"Warning: Unknown buff type '{buff_name}' in config")

def apply_buff_combination(enemy, combination_name: str):
    """Apply a predefined buff combination to an enemy"""
    if not hasattr(enemy, 'buff_manager'):
        enemy.buff_manager = EnemyBuffManager(enemy)
    
    try:
        from config.game_config import get_balance_config
        config = get_balance_config()
        combinations = config.get('enemy_buff_combinations', {})
        
        if combination_name not in combinations:
            print(f"Warning: Unknown buff combination '{combination_name}'")
            return False
        
        combination = combinations[combination_name]
        wave_req = combination.get('wave_requirements', 0)
        
        # Check if current wave meets requirements
        if hasattr(enemy, 'wave_number') and enemy.wave_number < wave_req:
            return False
        
        # Apply all buffs in the combination
        buff_names = combination.get('buffs', [])
        success_count = 0
        
        for buff_name in buff_names:
            try:
                buff_type = BuffType(buff_name)
                if enemy.buff_manager.add_buff(buff_type):
                    success_count += 1
            except ValueError:
                print(f"Warning: Unknown buff type '{buff_name}' in combination '{combination_name}'")
        
        return success_count > 0
        
    except Exception as e:
        print(f"Error applying buff combination '{combination_name}': {e}")
        return False

def create_buffed_enemy(enemy_class, path, wave_number: int, buff_combinations: Optional[List[str]] = None, individual_buffs: Optional[List[str]] = None):
    """Create an enemy with specified buffs applied"""
    enemy = enemy_class(path, wave_number)
    
    # Apply buff combinations
    if buff_combinations:
        for combination in buff_combinations:
            apply_buff_combination(enemy, combination)
    
    # Apply individual buffs
    if individual_buffs:
        if not hasattr(enemy, 'buff_manager'):
            enemy.buff_manager = EnemyBuffManager(enemy)
        
        for buff_name in individual_buffs:
            try:
                buff_type = BuffType(buff_name)
                enemy.buff_manager.add_buff(buff_type)
            except ValueError:
                print(f"Warning: Unknown buff type '{buff_name}'")
    
    return enemy

def get_available_buffs_for_wave(wave_number: int) -> List[str]:
    """Get list of buffs available for a specific wave"""
    try:
        from config.game_config import get_balance_config
        config = get_balance_config()
        buff_config = config.get('buff_spawn_chances', {})
        wave_ranges = buff_config.get('wave_ranges', {})
        
        for wave_range, settings in wave_ranges.items():
            if '-' in wave_range:
                start, end = map(int, wave_range.split('-'))
                if start <= wave_number <= end:
                    return settings.get('allowed_buffs', [])
            elif wave_range.endswith('+'):
                min_wave = int(wave_range.replace('+', ''))
                if wave_number >= min_wave:
                    return settings.get('allowed_buffs', [])
            else:
                try:
                    if wave_number == int(wave_range):
                        return settings.get('allowed_buffs', [])
                except ValueError:
                    continue
        
        return []
    except:
        return []

def get_available_combinations_for_wave(wave_number: int) -> List[str]:
    """Get list of buff combinations available for a specific wave"""
    try:
        from config.game_config import get_balance_config
        config = get_balance_config()
        combinations = config.get('enemy_buff_combinations', {})
        
        available = []
        for name, combo in combinations.items():
            wave_req = combo.get('wave_requirements', 0)
            if wave_number >= wave_req:
                available.append(name)
        
        return available
    except:
        return [] 