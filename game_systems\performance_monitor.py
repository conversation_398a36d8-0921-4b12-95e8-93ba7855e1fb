"""
Real-time Performance Monitoring System
Tracks FPS, update times, and identifies performance bottlenecks
"""

import time
import pygame
from collections import deque
from typing import Dict, List, Optional


class PerformanceMonitor:
    """Real-time performance monitoring and profiling"""
    
    def __init__(self, max_samples: int = 60):
        """
        Initialize performance monitor
        
        Args:
            max_samples: Number of samples to keep for averaging
        """
        self.max_samples = max_samples
        
        # FPS tracking
        self.fps_samples = deque(maxlen=max_samples)
        self.frame_times = deque(maxlen=max_samples)
        self.last_frame_time = time.time()
        
        # Update time tracking
        self.update_times = {
            'enemies': deque(maxlen=max_samples),
            'towers': deque(maxlen=max_samples),
            'projectiles': deque(maxlen=max_samples),
            'rendering': deque(maxlen=max_samples),
            'total': deque(maxlen=max_samples)
        }
        
        # Current frame profiling
        self.current_frame_start = 0
        self.current_update_start = 0
        self.current_section = None
        
        # Performance warnings
        self.low_fps_threshold = 45
        self.high_update_time_threshold = 0.016  # 16ms for 60 FPS
        
        # Display settings
        self.show_overlay = False
        self.font = None
        
    def initialize_font(self):
        """Initialize font for overlay display"""
        try:
            self.font = pygame.font.Font(None, 24)
        except:
            self.font = None
    
    def start_frame(self):
        """Mark the start of a new frame"""
        current_time = time.time()
        
        # Calculate frame time and FPS
        frame_time = current_time - self.last_frame_time
        self.frame_times.append(frame_time)
        
        if frame_time > 0:
            fps = 1.0 / frame_time
            self.fps_samples.append(fps)
        
        self.last_frame_time = current_time
        self.current_frame_start = current_time
    
    def start_section(self, section_name: str):
        """Start timing a specific section"""
        self.current_section = section_name
        self.current_update_start = time.time()
    
    def end_section(self):
        """End timing the current section"""
        if self.current_section and self.current_update_start:
            update_time = time.time() - self.current_update_start

            # Create section if it doesn't exist
            if self.current_section not in self.update_times:
                self.update_times[self.current_section] = deque(maxlen=self.max_samples)

            self.update_times[self.current_section].append(update_time)

        self.current_section = None
        self.current_update_start = 0
    
    def get_average_fps(self) -> float:
        """Get average FPS over recent samples"""
        if not self.fps_samples:
            return 0.0
        return sum(self.fps_samples) / len(self.fps_samples)
    
    def get_current_fps(self) -> float:
        """Get current FPS"""
        if not self.fps_samples:
            return 0.0
        return self.fps_samples[-1]
    
    def get_average_update_time(self, section: str) -> float:
        """Get average update time for a section in milliseconds"""
        if section not in self.update_times or not self.update_times[section]:
            return 0.0
        times = self.update_times[section]
        return (sum(times) / len(times)) * 1000  # Convert to milliseconds
    
    def get_performance_stats(self) -> Dict:
        """Get comprehensive performance statistics"""
        stats = {
            'fps': {
                'current': self.get_current_fps(),
                'average': self.get_average_fps(),
                'min': min(self.fps_samples) if self.fps_samples else 0,
                'max': max(self.fps_samples) if self.fps_samples else 0
            },
            'update_times_ms': {},
            'warnings': []
        }
        
        # Calculate update times
        for section in self.update_times:
            stats['update_times_ms'][section] = self.get_average_update_time(section)
        
        # Check for performance warnings
        if stats['fps']['current'] < self.low_fps_threshold:
            stats['warnings'].append(f"Low FPS: {stats['fps']['current']:.1f}")
        
        total_update_time = stats['update_times_ms'].get('total', 0) / 1000
        if total_update_time > self.high_update_time_threshold:
            stats['warnings'].append(f"High update time: {total_update_time*1000:.1f}ms")
        
        return stats
    
    def toggle_overlay(self):
        """Toggle performance overlay display"""
        self.show_overlay = not self.show_overlay
        if self.show_overlay and not self.font:
            self.initialize_font()
    
    def draw_overlay(self, screen, x: int = 10, y: int = 10):
        """Draw performance overlay on screen"""
        if not self.show_overlay or not self.font:
            return
        
        stats = self.get_performance_stats()
        
        # Background for overlay
        overlay_width = 250
        overlay_height = 150
        overlay_surface = pygame.Surface((overlay_width, overlay_height))
        overlay_surface.set_alpha(180)
        overlay_surface.fill((0, 0, 0))
        screen.blit(overlay_surface, (x, y))
        
        # Draw performance text
        line_height = 20
        current_y = y + 5
        
        # FPS info
        fps_color = (255, 255, 255) if stats['fps']['current'] >= self.low_fps_threshold else (255, 100, 100)
        fps_text = f"FPS: {stats['fps']['current']:.1f} (avg: {stats['fps']['average']:.1f})"
        fps_surface = self.font.render(fps_text, True, fps_color)
        screen.blit(fps_surface, (x + 5, current_y))
        current_y += line_height
        
        # Update times
        for section, time_ms in stats['update_times_ms'].items():
            if time_ms > 0:
                color = (255, 255, 255) if time_ms < 5 else (255, 200, 100) if time_ms < 10 else (255, 100, 100)
                time_text = f"{section}: {time_ms:.1f}ms"
                time_surface = self.font.render(time_text, True, color)
                screen.blit(time_surface, (x + 5, current_y))
                current_y += line_height
        
        # Warnings
        for warning in stats['warnings']:
            warning_surface = self.font.render(warning, True, (255, 100, 100))
            screen.blit(warning_surface, (x + 5, current_y))
            current_y += line_height
    
    def get_optimization_suggestions(self) -> List[str]:
        """Get optimization suggestions based on current performance"""
        suggestions = []
        stats = self.get_performance_stats()
        
        if stats['fps']['current'] < 30:
            suggestions.append("Critical: FPS below 30 - reduce enemy count or disable effects")
        elif stats['fps']['current'] < 45:
            suggestions.append("Warning: FPS below 45 - consider optimizations")
        
        # Check which systems are taking the most time
        update_times = stats['update_times_ms']
        max_time_section = max(update_times.items(), key=lambda x: x[1]) if update_times else None
        
        if max_time_section and max_time_section[1] > 8:
            suggestions.append(f"Bottleneck: {max_time_section[0]} taking {max_time_section[1]:.1f}ms")
        
        return suggestions
    
    def reset_stats(self):
        """Reset all performance statistics"""
        self.fps_samples.clear()
        self.frame_times.clear()
        for section_times in self.update_times.values():
            section_times.clear()


# Context manager for easy section timing
class SectionTimer:
    """Context manager for timing code sections"""
    
    def __init__(self, monitor: PerformanceMonitor, section_name: str):
        self.monitor = monitor
        self.section_name = section_name
    
    def __enter__(self):
        self.monitor.start_section(self.section_name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.monitor.end_section()
