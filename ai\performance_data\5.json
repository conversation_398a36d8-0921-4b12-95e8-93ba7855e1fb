{"win_flag": false, "lives_remaining": 0, "starting_lives": 20, "towers_built": {"basic": 2, "freezer": 2, "detector": 2, "antiair": 3, "flame": 7, "poison": 3, "ice": 15, "lightning": 12, "splash": 2, "cannon": 4, "laser": 2}, "tower_diversity": 11, "wave_reached": 17, "final_wave": 17, "survival_rate": 0.0, "score": 44.0, "difficulty_adjusted_score": 44.0, "previous_config_details": null, "starting_money": 20, "money_remaining": 2184, "total_money_spent": 5531, "total_money_earned": 7715, "game_duration_seconds": 314.5959384441376, "average_fps": 60, "most_built_tower_type": "ice", "tower_build_order": ["basic", "basic", "freezer", "freezer", "detector", "detector", "antiair", "antiair", "antiair", "flame", "flame", "flame", "poison", "poison", "poison", "ice", "ice", "ice", "ice", "ice", "ice", "ice", "ice", "ice", "ice", "ice", "lightning", "lightning", "lightning", "splash", "splash", "lightning", "lightning", "lightning", "lightning", "cannon", "cannon", "cannon", "cannon", "ice", "flame", "flame", "flame", "flame", "ice", "ice", "ice", "laser", "laser", "lightning", "lightning", "lightning", "lightning", "lightning"], "economic_efficiency": 1.3948653046465378, "resource_management_score": 43.00933125972006, "defeat_cause": "mid_game_scaling", "tower_effectiveness_analysis": {"basic": {"count": 2, "usage_percentage": 3.7037037037037033, "effectiveness_rating": "low"}, "freezer": {"count": 2, "usage_percentage": 3.7037037037037033, "effectiveness_rating": "low"}, "detector": {"count": 2, "usage_percentage": 3.7037037037037033, "effectiveness_rating": "low"}, "antiair": {"count": 3, "usage_percentage": 5.555555555555555, "effectiveness_rating": "low"}, "flame": {"count": 7, "usage_percentage": 12.962962962962962, "effectiveness_rating": "low"}, "poison": {"count": 3, "usage_percentage": 5.555555555555555, "effectiveness_rating": "low"}, "ice": {"count": 15, "usage_percentage": 27.77777777777778, "effectiveness_rating": "medium"}, "lightning": {"count": 12, "usage_percentage": 22.22222222222222, "effectiveness_rating": "medium"}, "splash": {"count": 2, "usage_percentage": 3.7037037037037033, "effectiveness_rating": "low"}, "cannon": {"count": 4, "usage_percentage": 7.4074074074074066, "effectiveness_rating": "low"}, "laser": {"count": 2, "usage_percentage": 3.7037037037037033, "effectiveness_rating": "low"}}, "critical_decision_points": [{"phase": "early_game", "wave_range": "1-5", "decision": "initial_tower_selection", "outcome": "successful"}], "adaptation_patterns": {"tower_diversity": 11, "build_pattern": "diverse", "strategy_changes": 15, "adaptability": "medium"}, "enemy_type_struggles": null, "strategic_mistakes": ["unused_resources"], "successful_strategies": [], "config_difficulty_score": 1, "config_difficulty_description": "Hard - Challenging configuration requiring strategy", "config_difficulty_components": {"starting_resources": 25.0, "enemy_scaling": 22.5, "spawn_progression": 10.3, "special_rounds": 8.6, "tower_economics": 4.0}, "level_metadata": {"name": "Infernal Gauntlet", "description": "A brutal 80-wave challenge that pushes even experienced defenders to their limits. Face relentless swarms of enemies with minimal starting resources, where every decision counts and strategic tower placement is crucial for survival. Only the most skilled commanders will emerge victorious from this hellish battlefield.", "difficulty_rating": "Hard", "estimated_duration": "45-60 minutes", "recommended_for": "experienced players", "special_features": ["80 challenging waves", "Low starting resources (20 money, 20 lives)", "High enemy scaling and boss density", "Advanced enemy types with special abilities", "Dynamic enemy buff system"], "victory_rewards": {"victory_points": 11, "victory_points_description": "<PERSON>arn 11 Victory Points for completing this challenging level", "unlock_requirements": [], "completion_bonuses": ["Unlock advanced tower upgrades", "Access to elite enemy strategies", "Prestigious completion achievement"]}, "tips": ["Prioritize economy early with basic towers", "Save money for critical anti-air defenses", "Use detector towers to counter invisible enemies", "Plan for multiple boss encounters"]}, "waves_completed_successfully": 17, "total_waves_in_config": 80, "progression_percentage": 21.25, "buff_encounters": null, "buff_combinations_seen": null, "most_challenging_buffs": null, "tower_counter_effectiveness": null, "buff_adaptation_score": 0.0, "enemy_buff_intensity": "none", "buff_related_deaths": 0, "effective_counter_strategies": null, "config_file_path": "e:\\vsCode\\tower_defence_game\\config/base\\tower_defense_game.json"}