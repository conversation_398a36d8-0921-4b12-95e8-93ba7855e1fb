import pygame
import math
from typing import List, Tuple
from config.game_config import get_balance_config


class Enemy:
    """Base class for all enemies"""

    def __init__(self, path: List[Tuple[int, int]], wave_number: int = 1):
        self.path = path
        self.path_index = 0
        self.x = float(path[0][0])
        self.y = float(path[0][1])
        self.wave_number = wave_number

        # Base stats - to be overridden by subclasses
        self.max_health = 1
        self.health = self.max_health
        self.base_speed = 1.0  # Store original speed for terrain effects
        self.speed = 1.0
        self.reward = 4
        self.size = 10
        self.color = (255, 0, 0)  # Red by default
        self.damage = 1  # How many lives this enemy takes when reaching the end

        # Status effects
        self.frozen = False
        self.freeze_timer = 0
        self.complete_freeze = False  # Flag for complete movement stop from freezer tower
        self.wet = False
        self.wet_timer = 0
        self.lightning_damage_multiplier = 1.0

        # Progressive immunity system
        self.immunities = self._generate_random_immunities()

        # State
        self.reached_end = False
        self.distance_traveled = 0

        # Map reference for terrain effects
        self.map_reference = None

        self.freeze_hit_counter = 0  # Counts hits from freeze projectiles
        # Add default status effect attributes to avoid attribute errors
        self.burn_timer = 0
        self.burn_damage = 0
        self.poison_timer = 0
        self.poison_damage = 0
        self.poison_damage_timer = 0
        # Add default damage reduction for linter safety
        self.damage_reduction = 0.0

        # Initialize buff system
        self.buff_manager = None
        self.damage_resistances = {}  # For buff-based damage resistances
        self.armor_reduction = 0.0    # For armor buff effects
        self._initialize_buff_system()

        # Set enemy type for sprite system (use class name in lowercase)
        self.enemy_type = self.__class__.__name__.lower().replace('enemy', '')

    def _initialize_buff_system(self):
        """Initialize the enemy buff system"""
        try:
            from .enemy_buffs import EnemyBuffManager
            self.buff_manager = EnemyBuffManager(self)
            self._apply_random_buffs()
        except ImportError:
            # Buff system not available, continue without buffs
            pass

    def _apply_random_buffs(self):
        """Apply random buffs based on wave progression and configuration"""
        if not self.buff_manager:
            return

        import random

        try:
            config = get_balance_config()
            buff_config = config.get('buff_spawn_chances', {})
            wave_ranges = buff_config.get('wave_ranges', {})

            # Find appropriate wave range
            wave_settings = None
            for wave_range, settings in wave_ranges.items():
                if '-' in wave_range:
                    start, end = map(int, wave_range.split('-'))
                    if start <= self.wave_number <= end:
                        wave_settings = settings
                        break
                elif wave_range.endswith('+'):
                    min_wave = int(wave_range.replace('+', ''))
                    if self.wave_number >= min_wave:
                        wave_settings = settings
                        break
                else:
                    try:
                        if self.wave_number == int(wave_range):
                            wave_settings = settings
                            break
                    except ValueError:
                        continue

            if not wave_settings:
                return

            # Check if enemy gets buffs this wave
            base_chance = wave_settings.get('base_chance', 0.1)

            # Boss multipliers
            boss_multipliers = buff_config.get('boss_multipliers', {})
            if hasattr(self, 'damage_reduction') and self.damage_reduction > 0.5:
                base_chance *= boss_multipliers.get('super_boss', 4.0)
            elif hasattr(self, 'damage_reduction') and self.damage_reduction > 0.2:
                base_chance *= boss_multipliers.get('boss', 3.0)
            elif self.wave_number % 5 == 0:
                base_chance *= boss_multipliers.get('mini_boss', 2.0)

            if random.random() > base_chance:
                return

            # Apply random buffs
            max_buffs = wave_settings.get('max_buffs', 1)
            allowed_buffs = wave_settings.get('allowed_buffs', [])

            if not allowed_buffs:
                return

            num_buffs = random.randint(1, max_buffs)
            selected_buffs = random.sample(
                allowed_buffs, min(num_buffs, len(allowed_buffs)))

            from .enemy_buffs import BuffType
            for buff_name in selected_buffs:
                try:
                    buff_type = BuffType(buff_name)
                    self.buff_manager.add_buff(buff_type)
                except ValueError:
                    continue

        except Exception:
            # If buff application fails, continue without buffs
            pass

    def _generate_random_immunities(self) -> dict:
        """Generate random immunities based on wave progression using config values"""
        import random

        immunities = {
            'freeze_immune': False,
            'slow_immune': False,
            'poison_immune': False,
            'burn_immune': False,
            'wet_immune': False,
            'stun_immune': False
        }

        # Get immunity configuration
        config = get_balance_config()
        immunity_config = config['immunity']

        # Calculate immunity chances based on wave number using config
        base_chance = min(immunity_config['max_immunity_chance'],
                          self.wave_number * immunity_config['base_chance_per_wave'])

        # Special waves have higher immunity chances using config multipliers
        if self.wave_number % 10 == 0:  # Boss waves
            base_chance *= immunity_config['boss_wave_multiplier']
        elif self.wave_number % 5 == 0:  # Mini-boss waves
            base_chance *= immunity_config['mini_boss_multiplier']

        # Randomly assign immunities
        for immunity_type in immunities:
            if random.random() < base_chance:
                immunities[immunity_type] = True

        # Ensure at least some enemies remain vulnerable early game using config
        if self.wave_number <= immunity_config['early_game_waves']:
            # Force at most max_immunities for early waves
            immune_count = sum(immunities.values())
            max_immunities = immunity_config['early_game_max_immunities']
            if immune_count > max_immunities:
                # Keep only max_immunities random immunities
                immune_types = [k for k, v in immunities.items() if v]
                keep_immunities = random.sample(immune_types, max_immunities)
                immunities = {k: (k in keep_immunities) for k in immunities}

        return immunities

    def is_immune_to(self, effect_type: str) -> bool:
        """Check if enemy is immune to a specific effect"""
        return self.immunities.get(f"{effect_type}_immune", False)

    def is_immune_to_tower(self, tower_type: str) -> bool:
        """Check if enemy is immune to a specific tower type"""
        # REMOVED: The broken test call to take_damage that was causing counter effects
        # This method should only check immunities, not trigger damage effects
        return False  # Simplified - let the actual damage calculation handle immunities

    def has_resistance_to(self, effect_type: str) -> bool:
        """Check if enemy has resistance to a specific effect (same as immunity for now)"""
        return self.immunities.get(f"{effect_type}_immune", False)

    def set_map_reference(self, map_obj):
        """Set reference to map for terrain effects"""
        self.map_reference = map_obj

    def set_base_speed(self, speed: float):
        """Set base speed and current speed (for use by subclasses)"""
        self.base_speed = speed
        self.speed = speed

    def __setattr__(self, name, value):
        """Override to automatically update base_speed when speed is set during initialization"""
        super().__setattr__(name, value)
        # If setting speed and we don't have terrain effects applied yet, also set base_speed
        if name == 'speed' and hasattr(self, 'base_speed') and not hasattr(self, '_speed_initialized'):
            self.base_speed = value
            self._speed_initialized = True

    def apply_terrain_speed_effects(self):
        """Apply terrain-based speed modifications"""
        if not self.map_reference:
            return

        from game_systems.terrain_types import get_terrain_property, SAND

        # Get terrain at current position
        terrain_type = self.map_reference.get_terrain_at_pixel(
            int(self.x), int(self.y))

        # Reset speed to base (accounting for other effects like freeze)
        if not self.frozen:
            self.speed = self.base_speed

        # Handle complete freeze (freezer tower) - completely stops movement
        if self.frozen and hasattr(self, 'complete_freeze') and self.complete_freeze:
            self.speed = 0.0  # Complete movement stop
            return

        # Apply terrain effects
        if terrain_type == SAND:
            # Sand increases enemy speed by 50%
            speed_multiplier = 1.5
            if self.frozen:
                # If frozen, apply both freeze and sand effects
                config = get_balance_config()
                is_boss = hasattr(
                    self, 'damage_reduction') and self.damage_reduction > 0.2

                if is_boss:
                    # Boss enemies get moderate freeze effect (40% slow) - more noticeable
                    freeze_factor = 0.4
                elif self.has_resistance_to('freeze'):
                    # Resistant enemies get less slow effect
                    freeze_factor = config['freeze']['resistance_slow_factor']
                else:
                    # Normal enemies get full slow effect
                    freeze_factor = config['freeze']['slow_factor']
                self.speed = self.base_speed * speed_multiplier * freeze_factor
            else:
                self.speed = self.base_speed * speed_multiplier
        else:
            # Handle freeze on non-sand terrain
            if self.frozen:
                config = get_balance_config()
                is_boss = hasattr(
                    self, 'damage_reduction') and self.damage_reduction > 0.2

                if is_boss:
                    # Boss enemies get moderate freeze effect (40% slow) - more noticeable
                    freeze_factor = 0.4
                elif self.has_resistance_to('freeze'):
                    # Resistant enemies get less slow effect
                    freeze_factor = config['freeze']['resistance_slow_factor']
                else:
                    # Normal enemies get full slow effect
                    freeze_factor = config['freeze']['slow_factor']
                self.speed = self.base_speed * freeze_factor

    def update(self):
        """Update enemy position and state"""
        # Handle freeze effect - now with resistance instead of immunity
        if self.frozen:
            self.freeze_timer -= 1
            if self.freeze_timer <= 0:
                self.frozen = False
                self.complete_freeze = False  # Reset complete freeze flag

        # Handle wet effect (if not immune)
        if self.wet and not self.is_immune_to('wet'):
            self.wet_timer -= 1
            if self.wet_timer <= 0:
                self.wet = False
                self.lightning_damage_multiplier = 1.0
        elif self.is_immune_to('wet'):
            # Immune enemies can't be wet
            self.wet = False
            self.wet_timer = 0
            self.lightning_damage_multiplier = 1.0

        # REMOVED: Counter effects update logic

        # Update buff system
        if self.buff_manager:
            self.buff_manager.update()

        # Apply terrain-based speed effects (handles freeze interactions)
        self.apply_terrain_speed_effects()

        # Always move (even when "frozen" - just slower)
        self.move_along_path()

    def update_with_speed(self, speed_multiplier: float):
        """Update enemy with speed multiplier for performance optimization"""
        # Handle freeze effect - adjust timer based on speed
        if self.frozen:
            self.freeze_timer -= speed_multiplier
            if self.freeze_timer <= 0:
                self.frozen = False
                self.complete_freeze = False  # Reset complete freeze flag

        # Handle wet effect (if not immune) - adjust timer based on speed
        if self.wet and not self.is_immune_to('wet'):
            self.wet_timer -= speed_multiplier
            if self.wet_timer <= 0:
                self.wet = False
                self.lightning_damage_multiplier = 1.0
        elif self.is_immune_to('wet'):
            # Immune enemies can't be wet
            self.wet = False
            self.wet_timer = 0
            self.lightning_damage_multiplier = 1.0

        # REMOVED: Counter effects update logic

        # Update buff system
        if self.buff_manager:
            self.buff_manager.update()

        # Apply terrain-based speed effects (handles freeze interactions)
        self.apply_terrain_speed_effects()

        # Move multiple times based on speed multiplier for smooth fast movement
        for _ in range(int(speed_multiplier)):
            if not self.reached_end:
                self.move_along_path()

        # Handle fractional speed
        if speed_multiplier % 1 > 0:
            # Store original speed
            original_speed = self.speed
            # Apply fractional multiplier to speed for this partial movement
            self.speed *= (speed_multiplier % 1)
            if not self.reached_end:
                self.move_along_path()
            # Restore original speed
            self.speed = original_speed

    def move_along_path(self):
        """Move the enemy along the predefined path"""
        if self.path_index >= len(self.path) - 1:
            self.reached_end = True
            return

        # If completely frozen (speed = 0), don't move
        if self.speed <= 0:
            return

        # Get next waypoint
        next_point = self.path[self.path_index + 1]

        # Calculate direction from current position to next waypoint
        # This handles enemies that are offset from the path (like split enemies)
        dx = next_point[0] - self.x
        dy = next_point[1] - self.y
        distance_to_next = math.sqrt(dx**2 + dy**2)

        if distance_to_next == 0:
            self.path_index += 1
            return

        # Check if we've reached or passed the next waypoint (adaptive threshold based on speed)
        # Use larger threshold for fast enemies to prevent overshooting
        detection_threshold = max(5, self.speed * 1.5)

        if distance_to_next < detection_threshold:
            # Snap to waypoint to ensure exact path following
            self.x = float(next_point[0])
            self.y = float(next_point[1])
            self.path_index += 1
            return

        # Normalize direction and apply speed
        dx = (dx / distance_to_next) * self.speed
        dy = (dy / distance_to_next) * self.speed

        # Move towards next waypoint
        self.x += dx
        self.y += dy
        self.distance_traveled += self.speed

    def take_damage(self, damage: int, tower_type: str = 'basic'):
        """Apply damage to the enemy with counter system multipliers and buff resistances"""
        # Get counter system configuration
        config = get_balance_config()
        counter_config = config.get('counter_system', {})
        multipliers = counter_config.get('tower_enemy_multipliers', {})
        default_multiplier = counter_config.get('default_multiplier', 1.0)
        max_multiplier = counter_config.get('max_multiplier', 3.0)
        min_multiplier = counter_config.get('min_multiplier', 0.1)

        # Calculate damage multiplier
        damage_multiplier = default_multiplier

        # Get enemy class name
        enemy_class = self.__class__.__name__

        # Check for tower-specific multipliers
        if tower_type in multipliers:
            tower_multipliers = multipliers[tower_type]

            # Direct enemy type match
            if enemy_class in tower_multipliers:
                damage_multiplier = tower_multipliers[enemy_class]

            # Special case: Lightning towers do extra damage to wet enemies
            elif tower_type == 'lightning' and self.wet and 'wet_enemies' in tower_multipliers:
                damage_multiplier = tower_multipliers['wet_enemies']

        # Clamp multiplier to acceptable range
        damage_multiplier = max(min_multiplier, min(
            max_multiplier, damage_multiplier))

        # Apply multiplier to damage
        modified_damage = int(damage * damage_multiplier)

        # Apply 3x damage multiplier for frozen enemies
        if self.frozen:
            modified_damage = int(modified_damage * 3.0)

        # Apply buff-based damage reductions
        if hasattr(self, 'damage_resistances'):
            resistance = self.damage_resistances.get(tower_type, 0)
            modified_damage = int(modified_damage * (1 - resistance))

        # Apply armor reduction from armor buffs
        if hasattr(self, 'armor_reduction'):
            modified_damage = int(modified_damage * (1 - self.armor_reduction))

        # Calculate actual damage (can't deal more than remaining health)
        actual_damage = min(modified_damage, self.health)
        self.health -= actual_damage

        # REMOVED: Counter effects visual feedback system

        return actual_damage

    def apply_freeze(self, duration: int, complete_freeze: bool = False):
        """Apply freeze effect to the enemy with resistance reducing duration and effectiveness"""
        config = get_balance_config()

        # BOSS BALANCE FIX: Boss enemies should never be completely immune to freeze
        # They can have resistance, but should always be affected to some degree
        is_boss = hasattr(
            self, 'damage_reduction') and self.damage_reduction > 0.2

        if self.has_resistance_to('freeze') and not is_boss:
            # Regular resistant enemies get reduced duration
            reduced_duration = int(
                duration * config['freeze']['resistance_duration_multiplier'])
            self.frozen = True
            self.freeze_timer = max(self.freeze_timer, reduced_duration)
        elif is_boss:
            # Boss enemies get moderate resistance but are never immune
            boss_duration = int(duration * 0.7)  # 70% duration for bosses
            self.frozen = True
            self.freeze_timer = max(self.freeze_timer, boss_duration)
        else:
            # Normal enemies get full duration
            self.frozen = True
            self.freeze_timer = max(self.freeze_timer, duration)

        # Store complete freeze flag for movement handling
        self.complete_freeze = complete_freeze

        # Speed will be updated by apply_terrain_speed_effects() which handles freeze

    def apply_wet_status(self, duration: int, lightning_multiplier: float):
        """Apply wet status to the enemy (if not immune)"""
        if not self.is_immune_to('wet'):
            self.wet = True
            self.wet_timer = max(self.wet_timer, duration)
            self.lightning_damage_multiplier = lightning_multiplier

    def apply_speed_boost(self, multiplier: float):
        """Apply speed boost from rock removal rewards"""
        self.speed = self.base_speed * multiplier

    def remove_speed_boost(self):
        """Remove speed boost and restore normal speed"""
        # Reapply terrain effects to get correct speed
        self.apply_terrain_speed_effects()

    def get_distance_from_start(self) -> float:
        """Get the total distance traveled along the path"""
        return self.distance_traveled

    def draw_health_bar(self, screen: pygame.Surface, x_offset: int = 0, y_offset: int = 0):
        """Draw health bar above the enemy - can be called by subclasses with custom positioning"""
        if self.health < self.max_health and self.max_health > 0:
            bar_width = self.size * 2
            bar_height = 4
            bar_x = int(self.x - bar_width // 2) + x_offset
            bar_y = int(self.y - self.size - 8) + y_offset

            # Background (red)
            pygame.draw.rect(screen, (255, 0, 0),
                             (bar_x, bar_y, bar_width, bar_height))

            # Health (green) - safeguard against division by zero
            health_width = int((self.health / self.max_health) * bar_width)
            pygame.draw.rect(screen, (0, 255, 0),
                             (bar_x, bar_y, health_width, bar_height))

    def draw(self, screen: pygame.Surface):
        """Draw the enemy on the screen with sprite support"""
        # Check if sprite manager is available
        sprite_manager = getattr(self, '_sprite_manager', None)

        # Try to draw with sprite first
        if sprite_manager and sprite_manager.has_enemy_sprites():
            # Calculate movement angle for directional sprites
            movement_angle = None
            if hasattr(self, 'dx') and hasattr(self, 'dy'):
                if self.dx != 0 or self.dy != 0:
                    movement_angle = math.atan2(self.dy, self.dx)

            sprite = sprite_manager.get_enemy_sprite(
                self.enemy_type, movement_angle=movement_angle)
            if sprite:
                # Center the sprite on the enemy position
                sprite_rect = sprite.get_rect()
                sprite_rect.center = (int(self.x), int(self.y))

                # Apply status effect tinting
                if self.frozen:
                    # Create a blue tinted version
                    tinted_sprite = sprite.copy()
                    tinted_sprite.fill(
                        (100, 100, 255), special_flags=pygame.BLEND_MULT)
                    screen.blit(tinted_sprite, sprite_rect)
                elif self.wet:
                    # Create a darker, saturated version
                    tinted_sprite = sprite.copy()
                    tinted_sprite.fill(
                        (200, 200, 255), special_flags=pygame.BLEND_MULT)
                    screen.blit(tinted_sprite, sprite_rect)
                elif hasattr(self, 'poison_timer') and self.poison_timer > 0:
                    # Create a green tinted version
                    tinted_sprite = sprite.copy()
                    tinted_sprite.fill(
                        (100, 255, 100), special_flags=pygame.BLEND_MULT)
                    screen.blit(tinted_sprite, sprite_rect)
                else:
                    screen.blit(sprite, sprite_rect)

                # Draw status effect overlays and health bar
                self._draw_status_effects_and_ui(screen)
                return

        # Fallback to original geometric drawing
        # Draw main enemy circle with status effects
        color = self.color
        if self.frozen:
            color = (100, 100, 255)  # Blue when frozen
        elif self.wet:
            # Slightly darker and more saturated when wet
            color = tuple(max(0, min(255, int(c * 0.8))) for c in self.color)
        elif hasattr(self, 'poison_timer') and self.poison_timer > 0:
            # Green tint when poisoned
            color = tuple(max(0, min(255, int(c * 0.7) + 40 if i ==
                          1 else int(c * 0.7))) for i, c in enumerate(self.color))

        pygame.draw.circle(
            screen, color, (int(self.x), int(self.y)), self.size)

        # Draw status effect overlays and health bar
        self._draw_status_effects_and_ui(screen)

    def _draw_status_effects_and_ui(self, screen: pygame.Surface):
        """Draw status effects and UI elements (used by both sprite and geometric rendering)"""
        # Draw poison effect overlay
        if hasattr(self, 'poison_timer') and self.poison_timer > 0:
            # Draw poison bubbles around enemy
            bubble_count = 4
            for i in range(bubble_count):
                angle = (360 // bubble_count) * i + \
                    (pygame.time.get_ticks() * 0.1)
                rad = math.radians(angle)
                bubble_x = self.x + math.cos(rad) * (self.size + 2)
                bubble_y = self.y + math.sin(rad) * (self.size + 2)
                bubble_size = 2 + \
                    int(math.sin(pygame.time.get_ticks() * 0.01 + i) * 1)
                pygame.draw.circle(screen, (50, 205, 50),
                                   (int(bubble_x), int(bubble_y)), bubble_size)
                pygame.draw.circle(screen, (0, 100, 0), (int(
                    bubble_x), int(bubble_y)), bubble_size, 1)

        # Draw immunity indicators
        self._draw_immunity_indicators(screen)

        # Draw wet effect overlay
        if self.wet:
            # Draw water droplets around enemy
            for angle in [0, 120, 240]:
                rad = math.radians(angle)
                drop_x = self.x + math.cos(rad) * (self.size + 3)
                drop_y = self.y + math.sin(rad) * (self.size + 3)
                pygame.draw.circle(screen, (30, 144, 255),
                                   (int(drop_x), int(drop_y)), 2)

        # Draw buff effects
        if self.buff_manager:
            self.buff_manager.draw_effects(screen)

        # Draw buff indicators
        if self.buff_manager and len(self.buff_manager.get_all_buffs()) > 0:
            self.buff_manager.draw_buff_indicators(screen, int(
                self.x + self.size + 5), int(self.y - self.size))

        # Draw health bar using the new method
        self.draw_health_bar(screen)

    def _draw_immunity_indicators(self, screen: pygame.Surface):
        """Draw small indicators for resistances (formerly immunities)"""
        resistant_effects = [k.replace('_immune', '')
                             for k, v in self.immunities.items() if v]

        if not resistant_effects:
            return

        # Draw resistance shield symbol (different color to indicate partial protection)
        shield_color = (255, 165, 0)  # Orange (instead of gold for immunity)
        shield_x = int(self.x + self.size - 3)
        shield_y = int(self.y - self.size + 3)

        # Draw small shield with slightly different design
        shield_points = [
            (shield_x, shield_y),
            (shield_x + 4, shield_y),
            (shield_x + 4, shield_y + 6),
            (shield_x + 2, shield_y + 8),
            (shield_x, shield_y + 6)
        ]
        pygame.draw.polygon(screen, shield_color, shield_points)
        pygame.draw.polygon(screen, (0, 0, 0), shield_points, 1)

        # Add small "R" inside shield to indicate resistance
        font = pygame.font.Font(None, 8)
        text = font.render("R", True, (0, 0, 0))
        text_rect = text.get_rect(center=(shield_x + 2, shield_y + 4))
        screen.blit(text, text_rect)

    def register_freeze_hit(self, duration: int, complete_freeze: bool = False):
        """Increment freeze hit counter and freeze if threshold reached"""
        # Don't accumulate freeze hits while already frozen
        if self.frozen:
            return

        self.freeze_hit_counter += 1
        if self.freeze_hit_counter >= 25:  # Back to 25 shots
            self.apply_freeze(duration, complete_freeze)
            self.freeze_hit_counter = 0  # Reset after freezing

    def set_sprite_manager(self, sprite_manager):
        """Set the sprite manager for this enemy"""
        self._sprite_manager = sprite_manager
