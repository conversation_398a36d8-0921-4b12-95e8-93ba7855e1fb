from .enemy import Enemy
import pygame
import math
import random


class Crystal<PERSON>verlord(Enemy):
    """Ultra powerful crystalline boss that reflects attacks and creates crystal structures"""

    def __init__(self, path, wave_number=1):
        super().__init__(path, wave_number)
        self.health = 800  # Further reduced for better balance
        self.max_health = 800
        self.speed = 0.35
        self.reward = 1000
        self.color = (100, 255, 255)  # Cyan crystal
        self.size = 38

        # Crystal abilities
        self.barrier_timer = 0
        self.barrier_cooldown = 540  # 9 seconds
        self.active_barriers = []

        # Crystal shards
        self.shard_timer = 0
        self.shard_cooldown = 360  # 6 seconds
        self.active_shards = []

        # Boss resistances
        self.damage_reduction = 0.55

        # Barrier protection system
        self.barrier_radius = 12  # Collision radius for barriers

        # Shard protection system
        self.shard_radius = 10  # Collision radius for shards
        self.shard_health = 800  # Health per shard

        # Phase system
        self.phase = 1
        self.max_phases = 3

        # BALANCE FIX: Override immunities to ensure freeze effects can work
        # Bosses should never be completely immune to crowd control
        self.immunities['freeze_immune'] = False

        # Visual effects
        self.crystal_particles = []
        self.rotation = 0

    def update(self):
        """Update with crystal abilities"""
        # Update phase
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            self.phase = 1
        elif health_percentage > 0.33:
            self.phase = 2
        else:
            self.phase = 3

        super().update()

        # Update timers
        self.barrier_timer += 1
        self.shard_timer += 1
        self.rotation += 2

        # Update abilities
        if self.barrier_timer >= self.barrier_cooldown:
            self.create_barrier()

        if self.shard_timer >= self.shard_cooldown:
            self.create_shard()

    def update_with_speed(self, speed_multiplier: float):
        """Update with speed multiplier for performance optimization"""
        # Update phase
        health_percentage = self.health / self.max_health
        if health_percentage > 0.66:
            self.phase = 1
        elif health_percentage > 0.33:
            self.phase = 2
        else:
            self.phase = 3

        super().update_with_speed(speed_multiplier)

        # Update timers with speed multiplier
        self.barrier_timer += speed_multiplier
        self.shard_timer += speed_multiplier
        self.rotation += 2 * speed_multiplier

        # Update abilities
        if self.barrier_timer >= self.barrier_cooldown:
            self.create_barrier()

        if self.shard_timer >= self.shard_cooldown:
            self.create_shard()

        # Update active barriers
        import math
        for barrier in self.active_barriers[:]:
            barrier['life'] -= speed_multiplier
            if barrier['life'] <= 0:
                self.active_barriers.remove(barrier)
            else:
                # Slowly rotate barriers
                barrier['angle'] += 0.01 * speed_multiplier
                barrier['x'] = self.x + \
                    math.cos(barrier['angle']) * (self.size + 20)
                barrier['y'] = self.y + \
                    math.sin(barrier['angle']) * (self.size + 20)

                # Update flash effect
                if 'block_flash' in barrier and barrier['block_flash'] > 0:
                    barrier['block_flash'] -= speed_multiplier

        # Update active shards and handle spawning
        shards_to_remove = []
        if not hasattr(self, '_shards_to_spawn'):
            self._shards_to_spawn = []

        for shard in self.active_shards[:]:
            shard['life'] -= speed_multiplier
            if shard['life'] <= 0:
                # Check if this shard should spawn enemies before removing it
                if shard['health'] > 0:  # Shard expired naturally, wasn't destroyed
                    # Add to spawning queue
                    self._shards_to_spawn.append(shard.copy())
                shards_to_remove.append(shard)
            else:
                # Orbit shards around the boss
                shard['angle'] += shard['orbit_speed'] * speed_multiplier
                shard['x'] = self.x + \
                    math.cos(shard['angle']) * (self.size + 30)
                shard['y'] = self.y + \
                    math.sin(shard['angle']) * (self.size + 30)

                # Update damage flash effect
                if 'damage_flash' in shard and shard['damage_flash'] > 0:
                    shard['damage_flash'] -= speed_multiplier

        # Remove expired shards after checking for spawning
        for shard in shards_to_remove:
            self.active_shards.remove(shard)

        # Create crystal particles for visual effect
        if len(self.crystal_particles) < 20:
            import random
            particle = {
                'x': self.x + random.uniform(-self.size, self.size),
                'y': self.y + random.uniform(-self.size, self.size),
                'vx': random.uniform(-1, 1),
                'vy': random.uniform(-1, 1),
                'life': random.uniform(30, 60),
                'max_life': 60,
                'size': random.uniform(2, 4)
            }
            self.crystal_particles.append(particle)

        # Update crystal particles
        for particle in self.crystal_particles[:]:
            particle['life'] -= speed_multiplier
            particle['x'] += particle['vx'] * speed_multiplier
            particle['y'] += particle['vy'] * speed_multiplier
            if particle['life'] <= 0:
                self.crystal_particles.remove(particle)

    def create_barrier(self):
        """Create a crystal barrier"""
        self.barrier_timer = 0

        # Create barrier particles around the boss
        import math
        for i in range(8):  # 8 barrier segments
            angle = (i / 8) * 2 * math.pi
            barrier_x = self.x + math.cos(angle) * (self.size + 20)
            barrier_y = self.y + math.sin(angle) * (self.size + 20)

            barrier = {
                'x': barrier_x,
                'y': barrier_y,
                'life': 300,  # 5 seconds at 60 FPS
                'max_life': 300,
                'angle': angle,
                'type': 'barrier',
                'block_flash': 0  # Flash effect when blocking projectiles
            }
            self.active_barriers.append(barrier)

    def create_shard(self):
        """Create a crystal shard"""
        self.shard_timer = 0

        # Create crystal shards that orbit around the boss
        import math
        for i in range(4):  # 4 shards
            angle = (i / 4) * 2 * math.pi + self.rotation
            shard_x = self.x + math.cos(angle) * (self.size + 30)
            shard_y = self.y + math.sin(angle) * (self.size + 30)

            shard = {
                'x': shard_x,
                'y': shard_y,
                'life': 240,  # 4 seconds at 60 FPS
                'max_life': 240,
                'angle': angle,
                'orbit_speed': 0.05,
                'type': 'shard',
                'health': self.shard_health,  # 800 health per shard
                'max_health': self.shard_health,
                'damage_flash': 0  # Flash effect when taking damage
            }
            self.active_shards.append(shard)

    def check_barrier_collision(self, projectile):
        """Check if a projectile collides with any barrier and return collision info"""
        import math

        for barrier in self.active_barriers:
            # Calculate distance between projectile and barrier
            distance = math.sqrt(
                (projectile.x - barrier['x'])**2 + (projectile.y - barrier['y'])**2)

            # Check if projectile is within barrier collision radius
            if distance <= self.barrier_radius:
                # Mark barrier for removal after blocking the projectile
                # This will cause it to be removed in the next update
                barrier['life'] = 0

                # Add flash effect to the barrier that blocked the projectile
                barrier['block_flash'] = 20  # Flash for 20 frames

                # Return collision info including the tower that fired this projectile
                return {
                    'hit': True,
                    'barrier': barrier,
                    'tower_id': getattr(projectile, 'source_tower_id', None)
                }

        return {'hit': False, 'barrier': None, 'tower_id': None}

    def check_shard_collision(self, projectile):
        """Check if a projectile collides with any shard and apply damage"""
        import math

        for shard in self.active_shards:
            # Calculate distance between projectile and shard
            distance = math.sqrt(
                (projectile.x - shard['x'])**2 + (projectile.y - shard['y'])**2)

            # Check if projectile is within shard collision radius
            if distance <= self.shard_radius:
                # Apply damage to the shard
                damage_dealt = min(projectile.damage, shard['health'])
                shard['health'] -= projectile.damage

                # Add damage flash effect
                shard['damage_flash'] = 15  # Flash for 15 frames

                # Check if shard is destroyed
                if shard['health'] <= 0:
                    # Mark shard for removal
                    shard['life'] = 0
                    return {
                        'hit': True,
                        'shard': shard,
                        'destroyed': True,
                        'damage': damage_dealt
                    }
                else:
                    return {
                        'hit': True,
                        'shard': shard,
                        'destroyed': False,
                        'damage': damage_dealt
                    }

        return {'hit': False, 'shard': None, 'destroyed': False, 'damage': 0}

    def spawn_crystalline_enemies_from_shard(self):
        """Spawn 5 crystalline enemies from undestroyed shards when they expire"""
        spawned_enemies = []

        # Check if we have any shards marked for spawning (this gets set in update_with_speed)
        if hasattr(self, '_shards_to_spawn'):
            for shard_data in self._shards_to_spawn:
                from .crystalline_enemy import CrystallineEnemy

                for i in range(5):  # Spawn 5 crystalline enemies per shard
                    # Create enemy at the shard's position on the path
                    crystalline = CrystallineEnemy(self.path, self.wave_number)

                    # Position the enemy at the start of the path with some spacing
                    if self.path and len(self.path) > 0:
                        start_x, start_y = self.path[0]
                        # Add some random offset to prevent stacking
                        import random
                        offset_x = random.uniform(-20, 20)
                        offset_y = random.uniform(-20, 20)
                        crystalline.x = start_x + offset_x
                        crystalline.y = start_y + offset_y
                        crystalline.path_index = 0

                    spawned_enemies.append(crystalline)

                print(f"Crystal Overlord shard spawned 5 Crystalline enemies!")

            # Clear the spawning queue
            self._shards_to_spawn = []

        return spawned_enemies

    def take_damage(self, damage, tower_type: str = 'basic'):
        """Take damage with crystal resistances"""
        # Laser immunity
        if tower_type == 'laser':
            return 0

        # Apply 3x damage multiplier for frozen enemies
        if self.frozen:
            damage = int(damage * 3.0)

        # Apply damage reduction
        reduced_damage = damage * (1 - self.damage_reduction)
        actual_damage = min(reduced_damage, self.health)

        self.health -= reduced_damage
        return actual_damage

    def draw(self, screen):
        """Draw the Crystal Overlord with all crystal effects"""
        import math

        # Draw crystal particles first (background layer)
        for particle in self.crystal_particles:
            alpha = particle['life'] / particle['max_life']
            size = int(particle['size'] * alpha)
            if size > 0:
                color = (int(100 * alpha), int(255 * alpha), int(255 * alpha))
                pygame.draw.circle(screen, color,
                                   (int(particle['x']), int(particle['y'])), size)

        # Draw crystal barriers
        for barrier in self.active_barriers:
            alpha = barrier['life'] / barrier['max_life']

            # Check if barrier is flashing from blocking a projectile
            flash_intensity = 1.0
            if 'block_flash' in barrier and barrier['block_flash'] > 0:
                # Flash up to 3x brighter
                flash_intensity = 1.0 + (barrier['block_flash'] / 20.0) * 2.0

            # Draw barrier as a glowing crystal segment with flash effect
            barrier_color = (int(150 * alpha * flash_intensity),
                             int(200 * alpha * flash_intensity), int(255 * alpha * flash_intensity))
            # Clamp colors to 255
            barrier_color = (min(255, barrier_color[0]), min(
                255, barrier_color[1]), min(255, barrier_color[2]))

            pygame.draw.circle(screen, barrier_color,
                               (int(barrier['x']), int(barrier['y'])), 8)

            # Draw connecting lines to boss (also flash)
            line_color = (int(100 * flash_intensity), int(150 *
                          flash_intensity), int(200 * flash_intensity))
            line_color = (min(255, line_color[0]), min(
                255, line_color[1]), min(255, line_color[2]))
            pygame.draw.line(screen, line_color,
                             (int(self.x), int(self.y)),
                             (int(barrier['x']), int(barrier['y'])), 2)

        # Draw crystal shards
        for shard in self.active_shards:
            alpha = shard['life'] / shard['max_life']

            # Check if shard is flashing from taking damage
            flash_intensity = 1.0
            if 'damage_flash' in shard and shard['damage_flash'] > 0:
                # Flash red when taking damage
                flash_intensity = 1.0 + (shard['damage_flash'] / 15.0) * 2.0

            # Calculate health-based color intensity
            health_ratio = shard['health'] / shard['max_health']

            # Shard color changes from green to red as health decreases
            red_component = int(
                200 * alpha * (2.0 - health_ratio) * flash_intensity)
            green_component = int(255 * alpha * health_ratio * flash_intensity)
            blue_component = int(200 * alpha * health_ratio * flash_intensity)

            # Clamp colors to 255
            shard_color = (min(255, red_component), min(
                255, green_component), min(255, blue_component))

            # Draw shard as a diamond shape
            points = []
            for i in range(4):
                angle = shard['angle'] + (i * math.pi / 2)
                px = shard['x'] + math.cos(angle) * 6
                py = shard['y'] + math.sin(angle) * 6
                points.append((int(px), int(py)))
            if len(points) >= 3:
                pygame.draw.polygon(screen, shard_color, points)

            # Draw shard health bar
            if shard['health'] < shard['max_health']:
                bar_width = 20
                bar_height = 3
                bar_x = shard['x'] - bar_width // 2
                bar_y = shard['y'] - 15

                # Background
                pygame.draw.rect(screen, (50, 50, 50),
                                 (int(bar_x), int(bar_y), bar_width, bar_height))

                # Health bar
                health_percentage = shard['health'] / shard['max_health']
                health_bar_color = (
                    255, int(255 * health_ratio), int(255 * health_ratio))
                pygame.draw.rect(screen, health_bar_color,
                                 (int(bar_x), int(bar_y), int(bar_width * health_percentage), bar_height))

        # Draw main crystal body with rotation effect
        # Create a multi-layered crystal appearance
        for layer in range(3):
            layer_size = self.size - (layer * 8)
            layer_alpha = 255 - (layer * 60)
            layer_color = (int(self.color[0] * layer_alpha / 255),
                           int(self.color[1] * layer_alpha / 255),
                           int(self.color[2] * layer_alpha / 255))

            if layer_size > 0:
                pygame.draw.circle(screen, layer_color,
                                   (int(self.x), int(self.y)), layer_size)

        # Draw rotating crystal facets
        for i in range(6):
            angle = self.rotation + (i * math.pi / 3)
            facet_x = self.x + math.cos(angle) * (self.size - 10)
            facet_y = self.y + math.sin(angle) * (self.size - 10)
            pygame.draw.circle(screen, (255, 255, 255),
                               (int(facet_x), int(facet_y)), 4)

        # Draw outer crystal border
        pygame.draw.circle(screen, (255, 255, 255),
                           (int(self.x), int(self.y)), self.size, 4)

        # Draw health bar
        bar_width = self.size * 5
        bar_height = 15

        pygame.draw.rect(screen, (30, 30, 30),
                         (self.x - bar_width//2, self.y - self.size - 40, bar_width, bar_height))

        health_percentage = self.health / self.max_health
        pygame.draw.rect(screen, self.color,
                         (self.x - bar_width//2, self.y - self.size - 40,
                          int(bar_width * health_percentage), bar_height))

        # Draw boss title
        font = pygame.font.Font(None, 32)
        title_text = font.render("CRYSTAL OVERLORD", True, (255, 255, 255))
        title_rect = title_text.get_rect(
            center=(self.x, self.y - self.size - 65))
        screen.blit(title_text, title_rect)
