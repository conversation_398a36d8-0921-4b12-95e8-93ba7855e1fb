"""
Shared UI utilities for the tower defense game
Consolidates common UI rendering patterns and functionality
"""

import pygame
import math
import time
from typing import Tu<PERSON>, Dict, Any


class UIUtils:
    """Shared UI utility functions"""
    
    # Common colors
    WHITE = (255, 255, 255)
    BLACK = (0, 0, 0)
    RED = (255, 0, 0)
    GREEN = (0, 255, 0)
    BLUE = (0, 0, 255)
    YELLOW = (255, 255, 0)
    LIGHT_GRAY = (200, 200, 200)
    DARK_GRAY = (64, 64, 64)
    UI_BG = (40, 40, 40)
    UI_BORDER = (100, 100, 100)
    
    @staticmethod
    def draw_overlay(screen: pygame.Surface, alpha: int = 128, color: Tuple[int, int, int] = (0, 0, 0)):
        """Draw a semi-transparent overlay"""
        overlay = pygame.Surface(screen.get_size())
        overlay.set_alpha(alpha)
        overlay.fill(color)
        screen.blit(overlay, (0, 0))
    
    @staticmethod
    def draw_panel(screen: pygame.Surface, x: int, y: int, width: int, height: int, 
                   bg_color: Tuple[int, int, int] = None, border_color: Tuple[int, int, int] = None,
                   border_width: int = 2):
        """Draw a standard UI panel with background and border"""
        if bg_color is None:
            bg_color = UIUtils.UI_BG
        if border_color is None:
            border_color = UIUtils.UI_BORDER
            
        panel_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(screen, bg_color, panel_rect)
        if border_width > 0:
            pygame.draw.rect(screen, border_color, panel_rect, border_width)
        return panel_rect
    
    @staticmethod
    def draw_centered_text(screen: pygame.Surface, text: str, font: pygame.font.Font,
                          center_x: int, center_y: int, color: Tuple[int, int, int] = None):
        """Draw text centered at the given position"""
        if color is None:
            color = UIUtils.WHITE
        text_surface = font.render(text, True, color)
        text_rect = text_surface.get_rect(center=(center_x, center_y))
        screen.blit(text_surface, text_rect)
        return text_rect
    
    @staticmethod
    def draw_tooltip(screen: pygame.Surface, text: str, font: pygame.font.Font,
                    mouse_pos: Tuple[int, int], screen_width: int, screen_height: int,
                    bg_color: Tuple[int, int, int] = None, text_color: Tuple[int, int, int] = None,
                    padding: int = 5):
        """Draw a tooltip near the mouse position"""
        if bg_color is None:
            bg_color = UIUtils.UI_BG
        if text_color is None:
            text_color = UIUtils.WHITE
            
        text_surface = font.render(text, True, text_color)
        text_rect = text_surface.get_rect()
        
        # Position tooltip to avoid screen edges
        tooltip_x = min(mouse_pos[0] + 10, screen_width - text_rect.width - padding * 2 - 10)
        tooltip_y = max(mouse_pos[1] - text_rect.height - padding * 2 - 10, 10)
        
        # Draw background
        bg_rect = pygame.Rect(tooltip_x - padding, tooltip_y - padding,
                             text_rect.width + padding * 2, text_rect.height + padding * 2)
        pygame.draw.rect(screen, bg_color, bg_rect)
        pygame.draw.rect(screen, UIUtils.UI_BORDER, bg_rect, 2)
        
        # Draw text
        screen.blit(text_surface, (tooltip_x, tooltip_y))
        return bg_rect
    
    @staticmethod
    def draw_progress_bar(screen: pygame.Surface, x: int, y: int, width: int, height: int,
                         progress: float, bg_color: Tuple[int, int, int] = None,
                         fill_color: Tuple[int, int, int] = None, border_color: Tuple[int, int, int] = None):
        """Draw a progress bar"""
        if bg_color is None:
            bg_color = UIUtils.DARK_GRAY
        if fill_color is None:
            fill_color = UIUtils.GREEN
        if border_color is None:
            border_color = UIUtils.UI_BORDER
            
        # Clamp progress between 0 and 1
        progress = max(0, min(1, progress))
        
        # Draw background
        bg_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(screen, bg_color, bg_rect)
        
        # Draw fill
        if progress > 0:
            fill_width = int(width * progress)
            fill_rect = pygame.Rect(x, y, fill_width, height)
            pygame.draw.rect(screen, fill_color, fill_rect)
        
        # Draw border
        pygame.draw.rect(screen, border_color, bg_rect, 2)
        return bg_rect
    
    @staticmethod
    def get_pulsing_color(base_color: Tuple[int, int, int], intensity: int = 40, speed: float = 3.0):
        """Get a pulsing color effect"""
        pulse = int(intensity * (0.5 + 0.5 * math.sin(time.time() * speed)))
        return tuple(min(255, max(0, c + pulse)) for c in base_color)
    
    @staticmethod
    def draw_button(screen: pygame.Surface, x: int, y: int, width: int, height: int,
                   text: str, font: pygame.font.Font, enabled: bool = True,
                   bg_color: Tuple[int, int, int] = None, text_color: Tuple[int, int, int] = None,
                   border_color: Tuple[int, int, int] = None):
        """Draw a button with text"""
        if not enabled:
            bg_color = UIUtils.DARK_GRAY
            text_color = UIUtils.LIGHT_GRAY
            border_color = UIUtils.DARK_GRAY
        else:
            if bg_color is None:
                bg_color = UIUtils.UI_BG
            if text_color is None:
                text_color = UIUtils.WHITE
            if border_color is None:
                border_color = UIUtils.UI_BORDER
        
        # Draw button background
        button_rect = pygame.Rect(x, y, width, height)
        pygame.draw.rect(screen, bg_color, button_rect)
        pygame.draw.rect(screen, border_color, button_rect, 2)
        
        # Draw text centered
        UIUtils.draw_centered_text(screen, text, font, x + width // 2, y + height // 2, text_color)
        
        return button_rect
    
    @staticmethod
    def is_point_in_rect(point: Tuple[int, int], rect: pygame.Rect) -> bool:
        """Check if a point is inside a rectangle"""
        return rect.collidepoint(point)
    
    @staticmethod
    def clamp_to_screen(x: int, y: int, width: int, height: int, 
                       screen_width: int, screen_height: int) -> Tuple[int, int]:
        """Clamp a rectangle to stay within screen bounds"""
        x = max(0, min(x, screen_width - width))
        y = max(0, min(y, screen_height - height))
        return x, y


class FontManager:
    """Manages font loading and caching"""
    
    _fonts = {}
    
    @classmethod
    def get_font(cls, size: int, bold: bool = False) -> pygame.font.Font:
        """Get a font of the specified size, with caching"""
        key = (size, bold)
        if key not in cls._fonts:
            if bold:
                cls._fonts[key] = pygame.font.Font(None, size)
                cls._fonts[key].set_bold(True)
            else:
                cls._fonts[key] = pygame.font.Font(None, size)
        return cls._fonts[key]
    
    @classmethod
    def clear_cache(cls):
        """Clear the font cache"""
        cls._fonts.clear()


class AnimationUtils:
    """Utilities for UI animations"""
    
    @staticmethod
    def ease_in_out(t: float) -> float:
        """Ease in-out animation curve"""
        return t * t * (3.0 - 2.0 * t)
    
    @staticmethod
    def bounce(t: float) -> float:
        """Bounce animation curve"""
        if t < 0.5:
            return 2 * t * t
        else:
            return 1 - 2 * (1 - t) * (1 - t)
    
    @staticmethod
    def pulse(speed: float = 1.0, min_val: float = 0.0, max_val: float = 1.0) -> float:
        """Get a pulsing value between min_val and max_val"""
        t = (math.sin(time.time() * speed) + 1) / 2  # Normalize to 0-1
        return min_val + t * (max_val - min_val)
