"""
Spatial Partitioning System for Tower Defense Game
Optimizes enemy-tower distance calculations by dividing the game world into grid sectors
"""

import math
from typing import List, Dict, Set, Tuple, Optional


class SpatialGrid:
    """Spatial partitioning system using a grid to optimize distance calculations"""
    
    def __init__(self, world_width: int, world_height: int, cell_size: int = 100):
        """
        Initialize spatial grid
        
        Args:
            world_width: Width of the game world
            world_height: Height of the game world  
            cell_size: Size of each grid cell (larger = fewer cells, less precision)
        """
        self.world_width = world_width
        self.world_height = world_height
        self.cell_size = cell_size
        
        # Calculate grid dimensions
        self.grid_width = math.ceil(world_width / cell_size)
        self.grid_height = math.ceil(world_height / cell_size)
        
        # Grid storage: grid[y][x] = set of entities
        self.enemy_grid: List[List[Set]] = []
        self.tower_grid: List[List[Set]] = []
        
        # Initialize empty grid
        self.clear_grid()
        
        # Cache for nearby cells calculation
        self._nearby_cells_cache: Dict[Tuple[int, int, int], List[Tuple[int, int]]] = {}
    
    def clear_grid(self):
        """Clear and reinitialize the grid"""
        self.enemy_grid = [[set() for _ in range(self.grid_width)] for _ in range(self.grid_height)]
        self.tower_grid = [[set() for _ in range(self.grid_width)] for _ in range(self.grid_height)]
    
    def get_cell_coords(self, x: float, y: float) -> Tuple[int, int]:
        """Convert world coordinates to grid cell coordinates"""
        cell_x = max(0, min(self.grid_width - 1, int(x // self.cell_size)))
        cell_y = max(0, min(self.grid_height - 1, int(y // self.cell_size)))
        return cell_x, cell_y
    
    def add_enemy(self, enemy, x: float, y: float):
        """Add enemy to the spatial grid"""
        cell_x, cell_y = self.get_cell_coords(x, y)
        self.enemy_grid[cell_y][cell_x].add(enemy)
    
    def add_tower(self, tower, x: float, y: float):
        """Add tower to the spatial grid"""
        cell_x, cell_y = self.get_cell_coords(x, y)
        self.tower_grid[cell_y][cell_x].add(tower)
    
    def remove_enemy(self, enemy, x: float, y: float):
        """Remove enemy from the spatial grid"""
        cell_x, cell_y = self.get_cell_coords(x, y)
        self.enemy_grid[cell_y][cell_x].discard(enemy)
    
    def remove_tower(self, tower, x: float, y: float):
        """Remove tower from the spatial grid"""
        cell_x, cell_y = self.get_cell_coords(x, y)
        self.tower_grid[cell_y][cell_x].discard(tower)
    
    def get_nearby_cells(self, center_x: int, center_y: int, radius_cells: int) -> List[Tuple[int, int]]:
        """Get list of cell coordinates within radius of center cell (cached)"""
        cache_key = (center_x, center_y, radius_cells)
        
        if cache_key in self._nearby_cells_cache:
            return self._nearby_cells_cache[cache_key]
        
        nearby_cells = []
        for dy in range(-radius_cells, radius_cells + 1):
            for dx in range(-radius_cells, radius_cells + 1):
                cell_x = center_x + dx
                cell_y = center_y + dy
                
                # Check bounds
                if 0 <= cell_x < self.grid_width and 0 <= cell_y < self.grid_height:
                    nearby_cells.append((cell_x, cell_y))
        
        self._nearby_cells_cache[cache_key] = nearby_cells
        return nearby_cells
    
    def get_enemies_near_tower(self, tower_x: float, tower_y: float, tower_range: float) -> List:
        """Get enemies within potential range of a tower (optimized)"""
        # Calculate which cells to check based on tower range
        center_x, center_y = self.get_cell_coords(tower_x, tower_y)
        radius_cells = math.ceil(tower_range / self.cell_size) + 1  # +1 for safety margin
        
        nearby_enemies = []
        nearby_cells = self.get_nearby_cells(center_x, center_y, radius_cells)
        
        for cell_x, cell_y in nearby_cells:
            nearby_enemies.extend(self.enemy_grid[cell_y][cell_x])
        
        return nearby_enemies
    
    def get_towers_near_enemy(self, enemy_x: float, enemy_y: float, max_range: float) -> List:
        """Get towers within potential range of an enemy"""
        center_x, center_y = self.get_cell_coords(enemy_x, enemy_y)
        radius_cells = math.ceil(max_range / self.cell_size) + 1
        
        nearby_towers = []
        nearby_cells = self.get_nearby_cells(center_x, center_y, radius_cells)
        
        for cell_x, cell_y in nearby_cells:
            nearby_towers.extend(self.tower_grid[cell_y][cell_x])
        
        return nearby_towers
    
    def update_enemy_position(self, enemy, old_x: float, old_y: float, new_x: float, new_y: float):
        """Update enemy position in the grid (only if cell changed)"""
        old_cell = self.get_cell_coords(old_x, old_y)
        new_cell = self.get_cell_coords(new_x, new_y)
        
        if old_cell != new_cell:
            # Remove from old cell
            self.enemy_grid[old_cell[1]][old_cell[0]].discard(enemy)
            # Add to new cell
            self.enemy_grid[new_cell[1]][new_cell[0]].add(enemy)
    
    def rebuild_enemy_grid(self, enemies: List):
        """Rebuild the entire enemy grid (use sparingly)"""
        # Clear enemy grid
        for row in self.enemy_grid:
            for cell in row:
                cell.clear()
        
        # Re-add all enemies
        for enemy in enemies:
            if hasattr(enemy, 'x') and hasattr(enemy, 'y'):
                self.add_enemy(enemy, enemy.x, enemy.y)
    
    def rebuild_tower_grid(self, towers: List):
        """Rebuild the entire tower grid (use sparingly)"""
        # Clear tower grid
        for row in self.tower_grid:
            for cell in row:
                cell.clear()
        
        # Re-add all towers
        for tower in towers:
            if hasattr(tower, 'x') and hasattr(tower, 'y'):
                self.add_tower(tower, tower.x, tower.y)
    
    def get_stats(self) -> Dict:
        """Get statistics about the spatial grid"""
        total_enemies = sum(len(cell) for row in self.enemy_grid for cell in row)
        total_towers = sum(len(cell) for row in self.tower_grid for cell in row)
        
        # Find max entities per cell
        max_enemies_per_cell = max(len(cell) for row in self.enemy_grid for cell in row)
        max_towers_per_cell = max(len(cell) for row in self.tower_grid for cell in row)
        
        return {
            'grid_size': f"{self.grid_width}x{self.grid_height}",
            'cell_size': self.cell_size,
            'total_enemies': total_enemies,
            'total_towers': total_towers,
            'max_enemies_per_cell': max_enemies_per_cell,
            'max_towers_per_cell': max_towers_per_cell,
            'cache_size': len(self._nearby_cells_cache)
        }


class PerformanceOptimizer:
    """Performance optimization utilities for the game"""
    
    @staticmethod
    def distance_squared(x1: float, y1: float, x2: float, y2: float) -> float:
        """Calculate squared distance (avoids expensive sqrt)"""
        dx = x2 - x1
        dy = y2 - y1
        return dx * dx + dy * dy
    
    @staticmethod
    def is_in_range_squared(x1: float, y1: float, x2: float, y2: float, range_squared: float) -> bool:
        """Check if two points are within range using squared distance"""
        return PerformanceOptimizer.distance_squared(x1, y1, x2, y2) <= range_squared
    
    @staticmethod
    def batch_process(items: List, batch_size: int = 10):
        """Process items in batches to spread load across frames"""
        for i in range(0, len(items), batch_size):
            yield items[i:i + batch_size]
