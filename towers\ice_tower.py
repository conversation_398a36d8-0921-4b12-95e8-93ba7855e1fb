from .tower import Tower
import pygame
import math


class IceTower(Tower):
    """Advanced freezing tower that slows enemies in an area"""

    def __init__(self, x, y):
        super().__init__(x, y, 'ice')
        self.damage = 0  # Pure crowd control - no damage
        self.range = 95   # Slightly increased range for better utility
        self.fire_rate = 55  # Faster firing for better area control
        self.projectile_speed = 5
        self.size = 12
        self.color = (173, 216, 230)  # Light blue

        # Ice properties - improved for boss balance
        self.freeze_duration = 90  # Longer freeze (was 40 frames)
        self.slow_factor = 0.4  # More severe slow - 40% speed (was 50%)
        self.area_effect_radius = 50  # Larger area (was 40)

        # Targeting capabilities - can target flying enemies but not invisible
        self.can_target_flying = True
        self.can_target_invisible = False
        self.can_target_ground = True

        # CRITICAL: Finalize initialization to set base stats correctly
        self.finalize_initialization()

    def acquire_target(self, enemies):
        """Find target using targeting restrictions"""
        valid_targets = []

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.range and self.can_target_enemy(enemy):
                valid_targets.append((enemy, distance))

        if not valid_targets:
            self.target = None
            return

        # Target closest to end of path
        self.target = max(
            valid_targets, key=lambda x: x[0].get_distance_from_start())[0]

        # Calculate angle to target
        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def shoot(self, projectiles):
        """Create ice blast with area freeze effect"""
        if self.target:
            from projectiles import IceProjectile
            projectile = IceProjectile(
                self.x, self.y, self.target.x, self.target.y,
                self.projectile_speed, self.damage, self.tower_type, self.freeze_duration,
                self.area_effect_radius, self.slow_factor
            )
            # Link projectile to tower for damage tracking
            projectile.source_tower_id = self.tower_id
            projectiles.append(projectile)

            # Generate currency immediately when firing
            self.generate_firing_currency()

    def draw(self, screen, selected: bool = False):
        """Draw ice tower with sprite support"""
        # Check if sprite manager is available
        sprite_manager = getattr(self, '_sprite_manager', None)

        if selected:
            pygame.draw.circle(screen, (200, 200, 200),
                               (int(self.x), int(self.y)), int(self.range), 1)

        # Try to draw with sprite first
        if sprite_manager and sprite_manager.has_tower_sprites():
            sprite = sprite_manager.get_tower_sprite(
                self.tower_type, self.angle)
            if sprite:
                # Center the sprite on the tower position
                sprite_rect = sprite.get_rect()
                sprite_rect.center = (int(self.x), int(self.y))
                screen.blit(sprite, sprite_rect)

                # Draw upgrade indicator if available
                self.draw_upgrade_indicator(screen)
                return

        # Fallback to custom drawing
        pygame.draw.circle(screen, self.color,
                           (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, (0, 0, 0),
                           (int(self.x), int(self.y)), self.size, 2)

        # Draw ice crystals
        for angle in [0, 60, 120, 180, 240, 300]:
            rad = math.radians(angle)
            crystal_x = self.x + math.cos(rad) * 8
            crystal_y = self.y + math.sin(rad) * 8
            points = [
                (crystal_x, crystal_y - 4),
                (crystal_x - 2, crystal_y + 2),
                (crystal_x + 2, crystal_y + 2)
            ]
            pygame.draw.polygon(screen, (255, 255, 255), points)

        # Draw upgrade indicator if available
        self.draw_upgrade_indicator(screen)

    def acquire_target_optimized(self, enemies, spatial_grid=None):
        """Optimized targeting with restrictions and spatial partitioning"""
        if not enemies:
            self.target = None
            return

        # Use spatial partitioning if available
        if spatial_grid:
            nearby_enemies = spatial_grid.get_enemies_near_tower(
                self.x, self.y, self.range)
            if not nearby_enemies:
                self.target = None
                return
            enemies_to_check = nearby_enemies
        else:
            enemies_to_check = enemies

        range_squared = self.range * self.range
        valid_targets = []

        for enemy in enemies_to_check:
            dx = enemy.x - self.x
            dy = enemy.y - self.y
            distance_squared = dx * dx + dy * dy

            if distance_squared <= range_squared and self.can_target_enemy(enemy):
                actual_distance = math.sqrt(distance_squared)
                valid_targets.append((enemy, actual_distance))
                if len(valid_targets) >= 10:
                    break

        if not valid_targets:
            self.target = None
            return

        # Target closest to end of path (default strategy)
        self.target = max(
            valid_targets, key=lambda x: x[0].get_distance_from_start())[0]

        if self.target:
            dx = self.target.x - self.x
            dy = self.target.y - self.y
            self.angle = math.atan2(dy, dx)

    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Update with speed multiplier and targeting restrictions"""
        self.acquire_target_optimized(enemies)

        if self.target and self.fire_timer <= 0:
            self.shoot(projectiles)
            self.fire_timer = self.fire_rate

        # Check for time distortion effects from Timelord Boss
        time_distortion_multiplier = self.get_time_distortion_multiplier(
            enemies)

        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier * time_distortion_multiplier
