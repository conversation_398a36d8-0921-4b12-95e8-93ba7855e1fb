ENEMY AND TOWER COMBINATIONS FOR TESTING
========================================

ENEMIES:
--------
1. BasicEnemy - Basic enemy with no special abilities
2. FastEnemy - Fast moving enemy
3. TankEnemy - High health, slow enemy
4. FlyingEnemy - Flying enemy (immune to ground towers)
5. ArmoredEnemy - Armored enemy (reduced damage)
6. InvisibleEnemy - Invisible enemy (requires detector)
7. ShieldedEnemy - Has shield that must be broken
8. RegeneratingEnemy - Regenerates health over time
9. SplittingEnemy - Splits into smaller enemies when killed
10. TeleportingEnemy - Teleports randomly
11. ToxicEnemy - Leaves toxic trail
12. ToxicMutantEnemy - Toxic enemy with splash damage
13. FireElementalEnemy - Fire elemental (immune to fire)
14. GroundedEnemy - Grounded enemy (immune to air attacks)
15. EnergyShieldEnemy - Has energy shield
16. BlastProofEnemy - Resistant to explosive damage
17. PhaseShiftEnemy - Can phase through attacks
18. CrystallineEnemy - Crystalline enemy with reflection
19. AdaptiveEnemy - Adapts to damage types
20. VoidEnemy - Void enemy (immune to most attacks)
21. SpectralEnemy - Spectral enemy (ghost-like)
22. SpeedBoss - Boss that gets faster when damaged
23. MegaBoss - Massive boss with multiple phases
24. CrystalOverlord - Crystal boss with barriers
25. ShadowKing - Shadow boss with teleportation
26. TimeLordBoss - Time manipulation boss
27. NecromancerBoss - Undead boss with resurrection

TOWERS:
-------
1. BasicTower - Basic tower with no special abilities
2. CannonTower - Cannon tower with splash damage
3. SniperTower - Long range, high damage
4. MissileTower - Missile tower with homing
5. FlameTower - Flame tower with burn damage
6. IceTower - Ice tower with freeze effect
7. FreezerTower - Freezer tower with area freeze
8. PoisonTower - Poison tower with DoT
9. LaserTower - Laser tower with piercing
10. LightningTower - Lightning tower with chain
11. ExplosiveTower - Explosive tower with splash
12. SplashTower - Splash tower with area damage
13. AntiairTower - Anti-air tower (targets flying)
14. DetectorTower - Detector tower (reveals invisible)
15. DestroyerTower - Destroyer tower (high damage)

ENEMY-TOWER INTERACTIONS:
========================

BASIC ENEMIES (1-4):
- All towers can target these enemies normally
- No special immunities or resistances

ARMORED ENEMIES (5-8):
- ArmoredEnemy: Takes reduced damage from all sources
- InvisibleEnemy: Only targetable by DetectorTower or when detected
- ShieldedEnemy: Shield must be broken before taking damage
- RegeneratingEnemy: Regenerates health when not taking damage

SPECIAL ENEMIES (9-12):
- SplittingEnemy: Creates smaller enemies when killed
- TeleportingEnemy: Moves unpredictably, harder to target
- ToxicEnemy: Leaves toxic trail that damages towers
- ToxicMutantEnemy: Toxic splash damage when hit

ELEMENTAL ENEMIES (13-16):
- FireElementalEnemy: Immune to FlameTower, weak to IceTower
- GroundedEnemy: Immune to AntiairTower, weak to ground attacks
- EnergyShieldEnemy: Shield absorbs damage before health
- BlastProofEnemy: Resistant to ExplosiveTower and SplashTower

ADVANCED ENEMIES (17-21):
- PhaseShiftEnemy: Can phase through some attacks
- CrystallineEnemy: Reflects some damage back to towers
- AdaptiveEnemy: Becomes resistant to damage types over time
- VoidEnemy: Immune to most attacks, requires specific towers
- SpectralEnemy: Ghost-like, harder to hit

BOSS ENEMIES (22-27):
- SpeedBoss: Gets faster when damaged, has dash ability
- MegaBoss: Multiple phases, spawns minions
- CrystalOverlord: Creates barriers, reflects attacks
- ShadowKing: Teleports, creates duplicates
- TimeLordBoss: Slows down time, creates temporal rifts
- NecromancerBoss: Resurrects dead enemies, drains life

TOWER SPECIALIZATIONS:
=====================

GROUND TOWERS:
- BasicTower: Good against basic enemies
- CannonTower: Good against groups
- SniperTower: Good against single targets
- MissileTower: Good against moving targets

ELEMENTAL TOWERS:
- FlameTower: Good against groups, burn damage
- IceTower: Good for crowd control, freeze effect
- FreezerTower: Area freeze effect
- PoisonTower: Good for sustained damage

SPECIAL TOWERS:
- LaserTower: Pierces through enemies
- LightningTower: Chains between enemies
- ExplosiveTower: Area damage
- SplashTower: Area damage

COUNTER TOWERS:
- AntiairTower: Specifically targets FlyingEnemy
- DetectorTower: Reveals InvisibleEnemy
- DestroyerTower: High damage against bosses

RECOMMENDED TESTING SCENARIOS:
=============================

1. BASIC COMBAT:
   - BasicEnemy vs BasicTower
   - FastEnemy vs SniperTower
   - TankEnemy vs CannonTower

2. ELEMENTAL INTERACTIONS:
   - FireElementalEnemy vs FlameTower (immune)
   - FireElementalEnemy vs IceTower (weak)
   - GroundedEnemy vs AntiairTower (immune)

3. SPECIAL ABILITIES:
   - InvisibleEnemy vs DetectorTower
   - FlyingEnemy vs AntiairTower
   - SplittingEnemy vs any tower (test splitting)

4. BOSS FIGHTS:
   - SpeedBoss vs multiple towers
   - MegaBoss vs DestroyerTower
   - CrystalOverlord vs LaserTower (immune)

5. CROWD CONTROL:
   - Groups of enemies vs FlameTower
   - Groups of enemies vs IceTower
   - Groups of enemies vs ExplosiveTower

6. SUSTAINED DAMAGE:
   - TankEnemy vs PoisonTower
   - Boss vs PoisonTower
   - RegeneratingEnemy vs any tower

7. AREA DAMAGE:
   - Groups vs SplashTower
   - Groups vs ExplosiveTower
   - Groups vs LightningTower

8. PIERCING:
   - Multiple enemies in line vs LaserTower
   - Groups vs LightningTower

9. HOMING:
   - TeleportingEnemy vs MissileTower
   - FastEnemy vs MissileTower

10. BOSS ABILITIES:
    - TimeLordBoss time distortion effect
    - NecromancerBoss resurrection
    - ShadowKing teleportation
    - CrystalOverlord barriers

TESTING NOTES:
=============
- Test each enemy with multiple tower types
- Verify immunities work correctly
- Check that special abilities trigger
- Test boss phases and transformations
- Verify damage calculations
- Test crowd control effects
- Check projectile interactions
- Test tower targeting priorities
- Verify currency generation
- Test performance with many enemies
