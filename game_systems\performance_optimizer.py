"""
Performance Optimization System for Tower Defense Game

Implements various optimization techniques:
- Object pooling for enemies, projectiles, and particles
- Batch processing for updates and rendering
- Spatial partitioning for collision detection
- Frame rate adaptive updates
- Memory management and garbage collection optimization
"""

import pygame
import time
from typing import List, Dict, Set, Tuple, Optional, Callable
from collections import deque
import math
import weakref

class ObjectPool:
    """Generic object pool for reusing objects instead of creating/destroying"""
    
    def __init__(self, create_func: Callable, reset_func: Callable, initial_size: int = 10, max_size: int = 100):
        self.create_func = create_func
        self.reset_func = reset_func
        self.max_size = max_size
        self.active_objects: List = []
        self.inactive_objects: deque = deque()
        
        # Pre-populate pool
        for _ in range(initial_size):
            obj = create_func()
            self.inactive_objects.append(obj)
    
    def get_object(self):
        """Get an object from the pool"""
        if self.inactive_objects:
            obj = self.inactive_objects.popleft()
        else:
            obj = self.create_func()
        
        self.active_objects.append(obj)
        return obj
    
    def return_object(self, obj):
        """Return an object to the pool"""
        if obj in self.active_objects:
            self.active_objects.remove(obj)
            self.reset_func(obj)
            
            if len(self.inactive_objects) < self.max_size:
                self.inactive_objects.append(obj)
    
    def clear(self):
        """Clear all objects from the pool"""
        self.active_objects.clear()
        self.inactive_objects.clear()

class SpatialGrid:
    """Spatial partitioning grid for efficient collision detection"""
    
    def __init__(self, cell_size: int = 64):
        self.cell_size = cell_size
        self.grid: Dict[Tuple[int, int], Set] = {}
    
    def get_cell_key(self, x: float, y: float) -> Tuple[int, int]:
        """Get grid cell key for given coordinates"""
        return (int(x // self.cell_size), int(y // self.cell_size))
    
    def add_object(self, obj, x: float, y: float):
        """Add object to spatial grid"""
        cell_key = self.get_cell_key(x, y)
        if cell_key not in self.grid:
            self.grid[cell_key] = set()
        self.grid[cell_key].add(obj)
    
    def remove_object(self, obj, x: float, y: float):
        """Remove object from spatial grid"""
        cell_key = self.get_cell_key(x, y)
        if cell_key in self.grid and obj in self.grid[cell_key]:
            self.grid[cell_key].remove(obj)
            if not self.grid[cell_key]:
                del self.grid[cell_key]
    
    def get_nearby_objects(self, x: float, y: float, radius: float) -> Set:
        """Get all objects within radius of given position"""
        nearby = set()
        center_cell = self.get_cell_key(x, y)
        cells_to_check = int(radius // self.cell_size) + 1
        
        for dx in range(-cells_to_check, cells_to_check + 1):
            for dy in range(-cells_to_check, cells_to_check + 1):
                cell_key = (center_cell[0] + dx, center_cell[1] + dy)
                if cell_key in self.grid:
                    nearby.update(self.grid[cell_key])
        
        return nearby
    
    def clear(self):
        """Clear the spatial grid"""
        self.grid.clear()

class BatchProcessor:
    """Batch processing for updates and rendering to reduce overhead"""
    
    def __init__(self, batch_size: int = 10):
        self.batch_size = batch_size
        self.update_batches: List[List] = []
        self.render_batches: List[List] = []
    
    def add_to_update_batch(self, obj, update_func: Callable):
        """Add object to update batch"""
        if not self.update_batches or len(self.update_batches[-1]) >= self.batch_size:
            self.update_batches.append([])
        self.update_batches[-1].append((obj, update_func))
    
    def add_to_render_batch(self, obj, render_func: Callable):
        """Add object to render batch"""
        if not self.render_batches or len(self.render_batches[-1]) >= self.batch_size:
            self.render_batches.append([])
        self.render_batches[-1].append((obj, render_func))
    
    def process_update_batches(self):
        """Process all update batches"""
        for batch in self.update_batches:
            for obj, update_func in batch:
                try:
                    update_func(obj)
                except Exception as e:
                    print(f"Error in batch update: {e}")
        self.update_batches.clear()
    
    def process_render_batches(self, surface: pygame.Surface):
        """Process all render batches"""
        for batch in self.render_batches:
            for obj, render_func in batch:
                try:
                    render_func(obj, surface)
                except Exception as e:
                    print(f"Error in batch render: {e}")
        self.render_batches.clear()

class FrameRateOptimizer:
    """Adaptive frame rate and update frequency optimization"""
    
    def __init__(self, target_fps: int = 60):
        self.target_fps = target_fps
        self.target_frame_time = 1.0 / target_fps
        self.frame_times = deque(maxlen=60)
        self.update_frequencies = {
            'high_priority': 1,    # Every frame
            'medium_priority': 2,  # Every other frame
            'low_priority': 4,     # Every 4th frame
            'very_low_priority': 8 # Every 8th frame
        }
        self.frame_counter = 0
    
    def should_update(self, priority: str) -> bool:
        """Check if object should update this frame based on priority"""
        self.frame_counter += 1
        frequency = self.update_frequencies.get(priority, 1)
        return self.frame_counter % frequency == 0
    
    def adjust_for_performance(self, current_fps: float):
        """Adjust update frequencies based on current FPS"""
        if current_fps < self.target_fps * 0.8:  # Below 80% of target
            # Reduce update frequency for lower priority objects
            self.update_frequencies['medium_priority'] = 3
            self.update_frequencies['low_priority'] = 6
            self.update_frequencies['very_low_priority'] = 12
        elif current_fps > self.target_fps * 0.95:  # Above 95% of target
            # Increase update frequency
            self.update_frequencies['medium_priority'] = 2
            self.update_frequencies['low_priority'] = 4
            self.update_frequencies['very_low_priority'] = 8

class PerformanceOptimizer:
    """Main performance optimization system"""
    
    def __init__(self, screen_width: int, screen_height: int):
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # Initialize optimization systems
        self.spatial_grid = SpatialGrid(cell_size=64)
        self.batch_processor = BatchProcessor(batch_size=15)
        self.frame_rate_optimizer = FrameRateOptimizer(target_fps=60)
        
        # Object pools
        self.enemy_pool = None
        self.projectile_pool = None
        self.particle_pool = None
        
        # Performance tracking
        self.performance_metrics = {
            'fps': 60,
            'frame_time_ms': 16.67,
            'entity_count': 0,
            'collision_checks': 0,
            'memory_usage_mb': 0
        }
        
        # Optimization settings
        self.optimization_level = 'auto'  # 'auto', 'low', 'medium', 'high'
        self.enable_object_pooling = True
        self.enable_spatial_partitioning = True
        self.enable_batch_processing = True
        self.enable_adaptive_updates = True
        
        # Memory management
        self.last_gc_time = time.time()
        self.gc_interval = 30.0  # Garbage collection every 30 seconds
    
    def initialize_pools(self, enemy_class, projectile_class, particle_class=None):
        """Initialize object pools with game-specific classes"""
        def create_enemy():
            return enemy_class(None)  # Will be properly initialized when used
        
        def reset_enemy(enemy):
            enemy.health = 0
            enemy.x = 0
            enemy.y = 0
            enemy.reached_end = False
        
        def create_projectile():
            return projectile_class(0, 0, 0, 0, None)
        
        def reset_projectile(projectile):
            projectile.x = 0
            projectile.y = 0
            projectile.active = False
        
        self.enemy_pool = ObjectPool(create_enemy, reset_enemy, initial_size=20, max_size=200)
        self.projectile_pool = ObjectPool(create_projectile, reset_projectile, initial_size=50, max_size=500)
        
        if particle_class:
            def create_particle():
                return particle_class(0, 0, (255, 255, 255))
            
            def reset_particle(particle):
                particle.x = 0
                particle.y = 0
                particle.life = 0
            
            self.particle_pool = ObjectPool(create_particle, reset_particle, initial_size=100, max_size=1000)
    
    def optimize_entity_updates(self, entities: List, update_func: Callable, priority: str = 'medium_priority'):
        """Optimize entity updates using batching and adaptive frequency"""
        if not self.enable_batch_processing:
            # Standard update
            for entity in entities:
                update_func(entity)
            return
        
        # Batch processing with adaptive frequency
        if self.frame_rate_optimizer.should_update(priority):
            for entity in entities:
                self.batch_processor.add_to_update_batch(entity, update_func)
    
    def optimize_collision_detection(self, objects_a: List, objects_b: List, 
                                   collision_func: Callable) -> List:
        """Optimize collision detection using spatial partitioning"""
        if not self.enable_spatial_partitioning:
            # Standard collision detection
            collisions = []
            for obj_a in objects_a:
                for obj_b in objects_b:
                    if collision_func(obj_a, obj_b):
                        collisions.append((obj_a, obj_b))
            return collisions
        
        # Spatial partitioning optimization
        collisions = []
        
        # Update spatial grid with objects_a
        self.spatial_grid.clear()
        for obj in objects_a:
            if hasattr(obj, 'x') and hasattr(obj, 'y'):
                self.spatial_grid.add_object(obj, obj.x, obj.y)
        
        # Check collisions using spatial partitioning
        for obj_b in objects_b:
            if hasattr(obj_b, 'x') and hasattr(obj_b, 'y'):
                nearby_objects = self.spatial_grid.get_nearby_objects(obj_b.x, obj_b.y, 64)
                for obj_a in nearby_objects:
                    if collision_func(obj_a, obj_b):
                        collisions.append((obj_a, obj_b))
        
        return collisions
    
    def optimize_rendering(self, entities: List, render_func: Callable):
        """Optimize rendering using batching"""
        if not self.enable_batch_processing:
            return
        
        for entity in entities:
            self.batch_processor.add_to_render_batch(entity, render_func)
    
    def process_batches(self, surface: pygame.Surface):
        """Process all batched operations"""
        self.batch_processor.process_update_batches()
        self.batch_processor.process_render_batches(surface)
    
    def update_performance_metrics(self, current_fps: float, entity_count: int):
        """Update performance metrics and adjust optimizations"""
        self.performance_metrics['fps'] = current_fps
        self.performance_metrics['entity_count'] = entity_count
        
        # Adjust frame rate optimizations
        if self.enable_adaptive_updates:
            self.frame_rate_optimizer.adjust_for_performance(current_fps)
        
        # Memory management
        current_time = time.time()
        if current_time - self.last_gc_time > self.gc_interval:
            import gc
            gc.collect()
            self.last_gc_time = current_time
    
    def get_optimization_stats(self) -> Dict:
        """Get optimization statistics for debugging"""
        return {
            'performance_metrics': self.performance_metrics.copy(),
            'pool_stats': {
                'enemy_pool_active': len(self.enemy_pool.active_objects) if self.enemy_pool else 0,
                'enemy_pool_inactive': len(self.enemy_pool.inactive_objects) if self.enemy_pool else 0,
                'projectile_pool_active': len(self.projectile_pool.active_objects) if self.projectile_pool else 0,
                'projectile_pool_inactive': len(self.projectile_pool.inactive_objects) if self.projectile_pool else 0,
            },
            'spatial_grid_cells': len(self.spatial_grid.grid),
            'update_frequencies': self.frame_rate_optimizer.update_frequencies.copy()
        }
    
    def set_optimization_level(self, level: str):
        """Set optimization level"""
        self.optimization_level = level
        
        if level == 'low':
            self.enable_object_pooling = False
            self.enable_spatial_partitioning = False
            self.enable_batch_processing = False
            self.enable_adaptive_updates = False
        elif level == 'medium':
            self.enable_object_pooling = True
            self.enable_spatial_partitioning = True
            self.enable_batch_processing = False
            self.enable_adaptive_updates = True
        elif level == 'high':
            self.enable_object_pooling = True
            self.enable_spatial_partitioning = True
            self.enable_batch_processing = True
            self.enable_adaptive_updates = True
        # 'auto' keeps current settings and adjusts dynamically
    
    def cleanup(self):
        """Clean up optimization systems"""
        if self.enemy_pool:
            self.enemy_pool.clear()
        if self.projectile_pool:
            self.projectile_pool.clear()
        if self.particle_pool:
            self.particle_pool.clear()
        self.spatial_grid.clear() 