from .tower import Tower
import pygame
import math


class MissileTower(Tower):
    """Long-range tower that fires slow homing missiles with AOE damage"""

    def __init__(self, x, y):
        super().__init__(x, y, 'missile')
        self.damage = 38  # Slightly reduced damage for balance
        self.range = 380  # Slightly reduced range
        self.fire_rate = 85  # Faster firing for better DPS
        self.projectile_speed = 5.5  # Faster missile speed - increased from 3.0
        self.size = 17  # Increased size for 2.5x2.5 tower
        self.color = (128, 128, 128)  # Gray

        # Missile properties
        self.explosion_radius = 60  # Large AOE
        self.explosion_damage = 25  # Splash damage
        self.missile_count = 2  # Fires 2 missiles at once

        # Targeting capabilities - can target both ground and flying enemies
        self.can_target_flying = True
        self.can_target_invisible = False
        self.can_target_ground = True

        # Visual effects
        self.charging = False
        self.charge_timer = 0
        self.charge_duration = 120  # 2 second charge time

        # CRITICAL: Finalize initialization to set base_range correctly
        self.finalize_initialization()

    def acquire_target(self, enemies):
        """Find target - prioritize enemy closest to end of path"""
        valid_targets = []
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.range and self.can_target_enemy(enemy):
                valid_targets.append((enemy, distance))
        if valid_targets:
            # Target enemy closest to end of path
            self.target = max(
                valid_targets, key=lambda x: x[0].get_distance_from_start())[0]
            # Calculate angle to target
            if self.target:
                dx = self.target.x - self.x
                dy = self.target.y - self.y
                self.angle = math.atan2(dy, dx)
        else:
            self.target = None

    def is_target_valid(self, target, enemies):
        """Check if the current target is still valid (in range and alive)"""
        if not target or target not in enemies:
            return False

        distance = math.sqrt((target.x - self.x)**2 + (target.y - self.y)**2)
        return distance <= self.range and self.can_target_enemy(target)

    def update(self, enemies, projectiles):
        """Update missile tower with charging mechanism"""
        # Update fire timer
        if self.fire_timer > 0:
            self.fire_timer -= 1

        # Update charging
        if self.charging:
            self.charge_timer += 1
            if self.charge_timer >= self.charge_duration:
                # Fire missiles
                self.fire_missiles(projectiles)
                self.charging = False
                self.charge_timer = 0
                self.fire_timer = self.fire_rate

        # Only acquire new target if not charging to preserve targeting period
        if not self.charging:
            self.acquire_target(enemies)
        else:
            # If charging but current target is invalid, try to find a new one
            # but continue charging regardless
            if not self.target or not self.is_target_valid(self.target, enemies):
                self.acquire_target(enemies)
            else:
                # Continue tracking current target visually
                self.update_angle_to_target()

        # Start charging if ready and have target
        if self.target and self.fire_timer <= 0 and not self.charging:
            self.charging = True
            self.charge_timer = 0

    def update_with_speed(self, enemies, projectiles, speed_multiplier: float):
        """Update missile tower with speed multiplier for performance optimization"""
        # Update fire timer with speed multiplier
        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier

        # Update charging with speed multiplier
        if self.charging:
            self.charge_timer += speed_multiplier
            if self.charge_timer >= self.charge_duration:
                # Fire missiles
                self.fire_missiles(projectiles)
                self.charging = False
                self.charge_timer = 0
                self.fire_timer = self.fire_rate

        # Only acquire new target if not charging to preserve targeting period
        if not self.charging:
            self.acquire_target(enemies)
        else:
            # If charging but current target is invalid, try to find a new one
            # but continue charging regardless
            if not self.target or not self.is_target_valid(self.target, enemies):
                self.acquire_target(enemies)
            else:
                # Continue tracking current target visually
                self.update_angle_to_target()

        # Start charging if ready and have target
        if self.target and self.fire_timer <= 0 and not self.charging:
            self.charging = True
            self.charge_timer = 0

    def update_with_speed_optimized(self, enemies, projectiles, speed_multiplier: float):
        """Update missile tower with speed multiplier and optimizations"""
        # Update fire timer with speed multiplier
        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier

        # Update charging with speed multiplier
        if self.charging:
            self.charge_timer += speed_multiplier
            if self.charge_timer >= self.charge_duration:
                # Fire missiles
                self.fire_missiles(projectiles)
                self.charging = False
                self.charge_timer = 0
                self.fire_timer = self.fire_rate

        # Only acquire new target if not charging to preserve targeting period
        if not self.charging:
            self.acquire_target_optimized(enemies)
        else:
            # If charging but current target is invalid, try to find a new one
            # but continue charging regardless
            if not self.target or not self.is_target_valid(self.target, enemies):
                self.acquire_target_optimized(enemies)
            else:
                # Continue tracking current target visually
                self.update_angle_to_target()

        # Start charging if ready and have target
        if self.target and self.fire_timer <= 0 and not self.charging:
            self.charging = True
            self.charge_timer = 0

    def update_with_speed_optimized_spatial(self, enemies, projectiles, speed_multiplier: float, spatial_grid):
        """Update missile tower with speed multiplier, optimizations, and spatial partitioning"""
        # Update fire timer with speed multiplier
        if self.fire_timer > 0:
            self.fire_timer -= speed_multiplier

        # Update charging with speed multiplier
        if self.charging:
            self.charge_timer += speed_multiplier
            if self.charge_timer >= self.charge_duration:
                # Fire missiles
                self.fire_missiles(projectiles)
                self.charging = False
                self.charge_timer = 0
                self.fire_timer = self.fire_rate

        # Only acquire new target if not charging to preserve targeting period
        if not self.charging:
            self.acquire_target_optimized(enemies, spatial_grid)
        else:
            # If charging but current target is invalid, try to find a new one
            # but continue charging regardless
            if not self.target or not self.is_target_valid(self.target, enemies):
                self.acquire_target_optimized(enemies, spatial_grid)
            else:
                # Continue tracking current target visually
                self.update_angle_to_target()

        # Start charging if ready and have target
        if self.target and self.fire_timer <= 0 and not self.charging:
            self.charging = True
            self.charge_timer = 0

    def acquire_target_optimized(self, enemies, spatial_grid=None):
        """Optimized targeting for missile tower using squared distance and spatial partitioning"""
        if not enemies:
            self.target = None
            return

        # Use spatial partitioning if available
        if spatial_grid:
            nearby_enemies = spatial_grid.get_enemies_near_tower(
                self.x, self.y, self.range)
            if not nearby_enemies:
                self.target = None
                return
            enemies_to_check = nearby_enemies
        else:
            enemies_to_check = enemies

        range_squared = self.range * self.range
        valid_targets = []
        for enemy in enemies_to_check:
            dx = enemy.x - self.x
            dy = enemy.y - self.y
            distance_squared = dx * dx + dy * dy
            if distance_squared <= range_squared and self.can_target_enemy(enemy):
                actual_distance = math.sqrt(distance_squared)
                valid_targets.append((enemy, actual_distance))
                if len(valid_targets) >= 8:
                    break
        if valid_targets:
            # Target enemy closest to end of path
            self.target = max(
                valid_targets, key=lambda x: x[0].get_distance_from_start())[0]
            # Calculate angle to target
            if self.target:
                dx = self.target.x - self.x
                dy = self.target.y - self.y
                self.angle = math.atan2(dy, dx)
        else:
            self.target = None

    def shoot(self, projectiles):
        """This is called by the base class but we handle firing in update()"""
        pass

    def fire_missiles(self, projectiles):
        """Fire homing missiles"""
        if not self.target:
            return

        for i in range(self.missile_count):
            # Slight offset for multiple missiles
            offset_angle = (i - 0.5) * 0.3  # Spread missiles slightly
            launch_x = self.x + math.cos(self.angle + offset_angle) * 10
            launch_y = self.y + math.sin(self.angle + offset_angle) * 10

            missile = HomingMissile(
                launch_x, launch_y, self.target.x, self.target.y,
                self.projectile_speed, self.damage, self.explosion_radius, self.explosion_damage
            )
            missile.source_tower_id = self.tower_id
            projectiles.append(missile)

        # Generate currency immediately when firing
        self.generate_firing_currency()

    def draw(self, screen, selected: bool = False):
        """Draw missile tower with sprite support"""
        # Check if sprite manager is available
        sprite_manager = getattr(self, '_sprite_manager', None)

        # Draw range circle only when selected
        if selected:
            pygame.draw.circle(screen, (200, 200, 200),
                               (int(self.x), int(self.y)), int(self.range), 1)

        # Draw charging effect
        if self.charging:
            charge_progress = self.charge_timer / self.charge_duration
            charge_radius = int(20 + 10 * charge_progress)
            charge_color = (255, int(100 + 155 * charge_progress), 0)

            pygame.draw.circle(screen, charge_color, (int(
                self.x), int(self.y)), charge_radius, 2)

            # Draw charge sparks
            for i in range(int(charge_progress * 8)):
                spark_angle = (i * 45) * math.pi / 180
                spark_x = self.x + math.cos(spark_angle) * charge_radius
                spark_y = self.y + math.sin(spark_angle) * charge_radius
                pygame.draw.circle(screen, (255, 255, 0),
                                   (int(spark_x), int(spark_y)), 2)

        # Try to draw with sprite first
        if sprite_manager and sprite_manager.has_tower_sprites():
            sprite = sprite_manager.get_tower_sprite(
                self.tower_type, self.angle)
            if sprite:
                # Center the sprite on the tower position
                sprite_rect = sprite.get_rect()
                sprite_rect.center = (int(self.x), int(self.y))
                screen.blit(sprite, sprite_rect)

                # Draw upgrade indicator if available
                self.draw_upgrade_indicator(screen)
                return

        # Fallback to custom drawing
        # Draw main tower
        pygame.draw.circle(screen, self.color,
                           (int(self.x), int(self.y)), self.size)
        pygame.draw.circle(screen, (0, 0, 0),
                           (int(self.x), int(self.y)), self.size, 2)

        # Draw missile launchers
        launcher_positions = [
            (self.x - 8, self.y - 8),
            (self.x + 8, self.y - 8),
            (self.x - 8, self.y + 8),
            (self.x + 8, self.y + 8)
        ]

        for launcher_x, launcher_y in launcher_positions:
            pygame.draw.rect(screen, (100, 100, 100),
                             (int(launcher_x - 3), int(launcher_y - 3), 6, 6))
            # Draw missile in launcher
            pygame.draw.circle(screen, (255, 0, 0),
                               (int(launcher_x), int(launcher_y)), 2)

        # Draw targeting system
        pygame.draw.circle(screen, (0, 255, 0),
                           (int(self.x), int(self.y - 18)), 3)

        # Draw barrel pointing at target
        if self.target:
            barrel_length = self.size + 5
            end_x = self.x + math.cos(self.angle) * barrel_length
            end_y = self.y + math.sin(self.angle) * barrel_length
            pygame.draw.line(screen, (0, 0, 0), (int(self.x),
                             int(self.y)), (int(end_x), int(end_y)), 3)

        # Draw upgrade indicator if available
        self.draw_upgrade_indicator(screen)


class HomingMissile:
    """Homing missile projectile with AOE damage"""

    def __init__(self, x, y, target_x, target_y, speed, damage, explosion_radius, explosion_damage):
        self.x = x
        self.y = y
        self.speed = speed
        self.damage = damage
        self.explosion_radius = explosion_radius
        self.explosion_damage = explosion_damage
        self.color = (255, 100, 0)  # Orange missile
        self.tower_type = 'missile'  # Add tower_type for screenshake detection

        # Homing properties
        # How strongly it homes - increased for much faster turning
        self.homing_strength = 0.25
        self.target_x = target_x
        self.target_y = target_y

        # Calculate initial direction
        dx = target_x - x
        dy = target_y - y
        distance = math.sqrt(dx**2 + dy**2)
        if distance > 0:
            self.dx = (dx / distance) * speed
            self.dy = (dy / distance) * speed
        else:
            self.dx = self.dy = 0

        self.active = True
        self.should_remove = False
        self.trail_positions = []  # For visual trail

        # Explosion animation
        self.exploding = False
        self.explosion_timer = 0
        self.explosion_duration = 15
        self.explosion_particles = []

    def update(self, enemies):
        """Update missile position with homing"""
        if self.exploding:
            # Update explosion animation
            self.explosion_timer += 1
            if self.explosion_timer >= self.explosion_duration:
                self.should_remove = True
            return

        if not self.active:
            return

        # Find closest enemy to home towards
        closest_enemy = None
        closest_distance = float('inf')

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance < closest_distance:
                closest_distance = distance
                closest_enemy = enemy

        # Update target if we found a closer enemy
        if closest_enemy:
            self.target_x = closest_enemy.x
            self.target_y = closest_enemy.y

        # Calculate direction to target
        target_dx = self.target_x - self.x
        target_dy = self.target_y - self.y
        target_distance = math.sqrt(target_dx**2 + target_dy**2)

        if target_distance > 0:
            # Normalize target direction
            target_dx /= target_distance
            target_dy /= target_distance

            # Apply homing
            self.dx += target_dx * self.homing_strength
            self.dy += target_dy * self.homing_strength

            # Normalize velocity to maintain speed
            current_speed = math.sqrt(self.dx**2 + self.dy**2)
            if current_speed > 0:
                self.dx = (self.dx / current_speed) * self.speed
                self.dy = (self.dy / current_speed) * self.speed

        # Update position
        self.x += self.dx
        self.y += self.dy

        # Add to trail
        self.trail_positions.append((self.x, self.y))
        if len(self.trail_positions) > 8:
            self.trail_positions.pop(0)

        # Check if reached target or off screen
        if target_distance < 10 or self.x < 0 or self.x > 1200 or self.y < 0 or self.y > 800:
            self.explode(enemies)

    def update_with_speed(self, enemies, speed_multiplier: float):
        """Update missile position with homing and speed multiplier"""
        if self.exploding:
            # Update explosion animation with speed multiplier
            self.explosion_timer += speed_multiplier
            if self.explosion_timer >= self.explosion_duration:
                self.should_remove = True
            return

        if not self.active:
            return

        # Find closest enemy to home towards
        closest_enemy = None
        closest_distance = float('inf')

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance < closest_distance:
                closest_distance = distance
                closest_enemy = enemy

        # Update target if we found a closer enemy
        if closest_enemy:
            self.target_x = closest_enemy.x
            self.target_y = closest_enemy.y

        # Calculate direction to target
        target_dx = self.target_x - self.x
        target_dy = self.target_y - self.y
        target_distance = math.sqrt(target_dx**2 + target_dy**2)

        if target_distance > 0:
            # Normalize target direction
            target_dx /= target_distance
            target_dy /= target_distance

            # Apply homing with speed multiplier
            self.dx += target_dx * self.homing_strength * speed_multiplier
            self.dy += target_dy * self.homing_strength * speed_multiplier

            # Normalize velocity to maintain speed
            current_speed = math.sqrt(self.dx**2 + self.dy**2)
            if current_speed > 0:
                self.dx = (self.dx / current_speed) * self.speed
                self.dy = (self.dy / current_speed) * self.speed

        # Update position with speed multiplier
        self.x += self.dx * speed_multiplier
        self.y += self.dy * speed_multiplier

        # Add to trail
        self.trail_positions.append((self.x, self.y))
        if len(self.trail_positions) > 8:
            self.trail_positions.pop(0)

        # Check if reached target or off screen
        if target_distance < 10 or self.x < 0 or self.x > 1200 or self.y < 0 or self.y > 800:
            self.explode(enemies)

    def explode(self, enemies):
        """Create explosion and damage nearby enemies"""
        # Damage all enemies in explosion radius
        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= self.explosion_radius:
                # Direct hit gets full damage, splash gets reduced damage
                if distance < 15:  # Direct hit
                    enemy.take_damage(self.damage, 'missile')
                else:  # Splash damage
                    enemy.take_damage(self.explosion_damage, 'missile')

        # Start explosion animation
        self.active = False
        self.exploding = True
        self.explosion_timer = 0
        self.create_explosion_particles()

    def draw(self, screen):
        """Draw missile with trail or explosion"""
        if self.exploding:
            # Draw explosion animation
            self.draw_explosion(screen)
            return

        if not self.active:
            return

        # Draw trail
        for i, (trail_x, trail_y) in enumerate(self.trail_positions):
            alpha = (i + 1) / len(self.trail_positions)
            trail_color = (int(255 * alpha), int(100 * alpha), 0)
            pygame.draw.circle(screen, trail_color, (int(
                trail_x), int(trail_y)), max(1, int(3 * alpha)))

        # Draw missile body
        pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), 4)
        pygame.draw.circle(screen, (255, 255, 0),
                           (int(self.x), int(self.y)), 2)

        # Draw flame trail
        if len(self.trail_positions) > 1:
            prev_x, prev_y = self.trail_positions[-1]
            flame_x = prev_x - self.dx * 0.5
            flame_y = prev_y - self.dy * 0.5
            pygame.draw.circle(screen, (255, 0, 0),
                               (int(flame_x), int(flame_y)), 2)

    def draw_explosion(self, screen):
        """Draw explosion animation with particles"""
        # Update and draw explosion particles
        for particle in self.explosion_particles[:]:
            # Update particle position
            particle['x'] += particle['dx']
            particle['y'] += particle['dy']
            particle['life'] -= 1

            # Apply gravity and friction
            particle['dy'] += 0.2
            particle['dx'] *= 0.98

            if particle['life'] <= 0:
                self.explosion_particles.remove(particle)
                continue

            # Calculate alpha based on life remaining
            alpha = particle['life'] / particle['max_life']
            size = int(particle['size'] * alpha)

            if size > 0:
                # Draw particle with fading effect
                color = particle['color']
                faded_color = (
                    int(color[0] * alpha), int(color[1] * alpha), int(color[2] * alpha))
                pygame.draw.circle(screen, faded_color, (int(
                    particle['x']), int(particle['y'])), size)

        # Draw explosion shockwave
        progress = self.explosion_timer / self.explosion_duration
        shockwave_radius = int(self.explosion_radius * progress)
        shockwave_alpha = int(255 * (1 - progress))

        if shockwave_alpha > 0 and shockwave_radius > 0:
            shockwave_color = (255, 100, 0, shockwave_alpha)
            pygame.draw.circle(screen, shockwave_color[:3], (int(
                self.x), int(self.y)), shockwave_radius, 3)

    def check_collision(self, enemies):
        """Check collision with enemies"""
        if not self.active:
            return {'hit': False, 'damage': 0, 'tower_id': None}

        for enemy in enemies:
            distance = math.sqrt((enemy.x - self.x)**2 + (enemy.y - self.y)**2)
            if distance <= 8:  # Close enough for collision
                # Calculate total damage dealt in explosion
                total_damage = 0
                for enemy_in_range in enemies:
                    explosion_distance = math.sqrt(
                        (enemy_in_range.x - self.x)**2 + (enemy_in_range.y - self.y)**2)
                    if explosion_distance <= self.explosion_radius:
                        if explosion_distance < 15:  # Direct hit
                            damage_dealt = enemy_in_range.take_damage(
                                self.damage, 'missile')
                        else:  # Splash damage
                            damage_dealt = enemy_in_range.take_damage(
                                self.explosion_damage, 'missile')
                        total_damage += damage_dealt

                # Start explosion animation instead of immediate removal
                self.active = False
                self.exploding = True
                self.explosion_timer = 0
                self.create_explosion_particles()

                return {'hit': True, 'damage': total_damage, 'tower_id': getattr(self, 'source_tower_id', None)}

        return {'hit': False, 'damage': 0, 'tower_id': None}

    def create_explosion_particles(self):
        """Create explosion particle effects"""
        import random
        for _ in range(20):  # Create 20 explosion particles
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(2, 8)
            particle = {
                'x': self.x,
                'y': self.y,
                'dx': math.cos(angle) * speed,
                'dy': math.sin(angle) * speed,
                'life': random.randint(8, 15),
                'max_life': 15,
                'size': random.randint(2, 6),
                'color': random.choice([(255, 100, 0), (255, 150, 0), (255, 200, 0), (255, 255, 0)])
            }
            self.explosion_particles.append(particle)
