from typing import List, <PERSON>ple
from .enemy import Enemy
import pygame
import math
import random


class ToxicMutantEnemy(Enemy):
    """Toxic mutant enemy immune to physical damage - healed by poison, only vulnerable to flame"""

    def __init__(self, path: List[Tuple[int, int]], wave_number: int = 1):
        super().__init__(path, wave_number)
        self.max_health = 35
        self.health = self.max_health
        self.speed = 1.2
        self.reward = 40
        self.size = 14
        self.color = (100, 255, 50)  # Toxic green

        # Toxic properties
        self.mutation_timer = 0
        self.toxic_bubbles = []
        self.pulsation = 0

        # Only poison and flame can damage this enemy
        self.physical_immunities = ['basic', 'sniper', 'cannon', 'laser', 'lightning',
                                    'missile', 'ice', 'antiair', 'explosive', 'splash']

    def take_damage(self, damage: int, tower_type: str = 'basic'):
        """Toxic mutants are healed by poison, only vulnerable to flame"""
        # Immune to all physical and energy attacks
        if tower_type in self.physical_immunities:
            # Create toxic splash when attacked
            self.create_toxic_splash()
            return 0

        # Poison heals the toxic mutant instead of damaging it
        if tower_type == 'poison':
            # Heal for the amount of damage that would have been dealt
            healing_amount = min(damage, self.max_health - self.health)
            if healing_amount > 0:
                self.health += healing_amount
                # Create healing particles
                self.create_healing_particles()
            return -healing_amount  # Return negative to indicate healing

        # Only flame can actually damage toxic mutants
        if tower_type != 'flame':
            self.create_toxic_splash()
            return 0

        # Use parent counter system for flame damage (gets 2x multiplier from config)
        actual_damage = super().take_damage(damage, tower_type)

        # Create mutation particles when damaged
        self.create_mutation_particles()
        return actual_damage

    def create_toxic_splash(self):
        """Create toxic splash effect when attacked by immune weapons"""
        for _ in range(8):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(2, 5)
            particle = {
                'x': self.x,
                'y': self.y,
                'dx': math.cos(angle) * speed,
                'dy': math.sin(angle) * speed,
                'life': random.randint(20, 35),
                'color': (50, 200, 50)
            }
            self.toxic_bubbles.append(particle)

    def create_mutation_particles(self):
        """Create mutation effect when damaged by effective weapons"""
        for _ in range(4):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(1, 3)
            particle = {
                'x': self.x,
                'y': self.y,
                'dx': math.cos(angle) * speed,
                'dy': math.sin(angle) * speed,
                'life': random.randint(15, 25),
                'color': (200, 100, 50)  # Orange for flame/poison reaction
            }
            self.toxic_bubbles.append(particle)

    def create_healing_particles(self):
        """Create healing particles when healed by poison"""
        for _ in range(15):  # More particles for better visibility
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(1, 4)
            particle = {
                'x': self.x + random.uniform(-5, 5),  # Spread around enemy
                'y': self.y + random.uniform(-5, 5),
                'dx': math.cos(angle) * speed,
                'dy': math.sin(angle) * speed - 2,  # Float upward faster
                'life': random.randint(40, 60),  # Last longer
                'color': (100, 255, 100)  # Brighter green for healing
            }
            self.toxic_bubbles.append(particle)

    def _handle_poison_healing(self, speed_multiplier: float):
        """Handle poison effects as healing instead of damage"""
        if hasattr(self, 'poison_timer') and self.poison_timer > 0:
            # Decrease poison timer
            self.poison_timer -= speed_multiplier

            # Handle poison damage timer for healing
            if hasattr(self, 'poison_damage_timer'):
                self.poison_damage_timer += speed_multiplier

                # Heal every 60 frames (1 second at 60 FPS)
                if self.poison_damage_timer >= 60:
                    if hasattr(self, 'poison_damage'):
                        # Heal for the poison damage amount
                        healing_amount = min(
                            self.poison_damage, self.max_health - self.health)
                        self.health += healing_amount

                        # Create small healing particles
                        if random.random() < 0.3:
                            self.create_healing_particles()

                    self.poison_damage_timer = 0

            # Remove poison effect when timer expires
            if self.poison_timer <= 0:
                if hasattr(self, 'poison_damage'):
                    delattr(self, 'poison_damage')
                if hasattr(self, 'poison_damage_timer'):
                    delattr(self, 'poison_damage_timer')

    def update(self):
        """Update with toxic effects"""
        super().update()
        self._update_toxic_effects(1.0)

    def update_with_speed(self, speed_multiplier: float):
        """Update with speed multiplier for performance optimization"""
        super().update_with_speed(speed_multiplier)
        self._update_toxic_effects(speed_multiplier)

    def _update_toxic_effects(self, speed_multiplier: float):
        """Update toxic effects with proper speed multiplier handling"""
        # Override poison effect to heal instead of damage
        self._handle_poison_healing(speed_multiplier)

        # Update mutation timer for visual effects
        self.mutation_timer += 0.15 * speed_multiplier
        self.pulsation += 0.2 * speed_multiplier

        # Create random toxic bubbles (scale probability by speed)
        if random.random() < 0.2 * speed_multiplier:
            bubble = {
                'x': self.x + random.uniform(-self.size/2, self.size/2),
                'y': self.y + random.uniform(-self.size/2, self.size/2),
                'dx': random.uniform(-1, 1),
                'dy': random.uniform(-2, 0),
                'life': random.randint(30, 50),
                'color': (random.randint(50, 150), random.randint(200, 255), random.randint(50, 100))
            }
            self.toxic_bubbles.append(bubble)

        # Update toxic bubbles
        for bubble in self.toxic_bubbles[:]:
            bubble['x'] += bubble['dx'] * speed_multiplier
            bubble['y'] += bubble['dy'] * speed_multiplier
            bubble['life'] -= speed_multiplier
            bubble['dy'] -= 0.1 * speed_multiplier  # Gravity effect

            if bubble['life'] <= 0:
                self.toxic_bubbles.remove(bubble)

    def draw(self, screen: pygame.Surface):
        """Draw toxic mutant enemy with mutation effects"""
        # Draw toxic bubbles
        for bubble in self.toxic_bubbles:
            alpha = bubble['life'] / 50.0
            if alpha > 0:
                bubble_color = (int(bubble['color'][0] * alpha),
                                int(bubble['color'][1] * alpha),
                                int(bubble['color'][2] * alpha))
                size = max(1, int(3 * alpha))
                pygame.draw.circle(screen, bubble_color,
                                   (int(bubble['x']), int(bubble['y'])), size)

        # Main toxic body with pulsing effect
        pulse = math.sin(self.pulsation) * 0.2 + 0.8
        body_size = int(self.size * pulse)

        # Draw multiple layers for toxic effect
        for i in range(3):
            layer_size = body_size - i * 2
            if layer_size > 0:
                alpha = (3 - i) / 3.0
                layer_color = (int(100 * alpha),
                               int(255 * alpha), int(50 * alpha))
                pygame.draw.circle(screen, layer_color,
                                   (int(self.x), int(self.y)), layer_size)

        # Draw mutation spots
        spot_count = 4 + int(math.sin(self.mutation_timer) * 2)
        for i in range(spot_count):
            angle = (i * 360 / spot_count +
                     self.mutation_timer * 10) * math.pi / 180
            spot_x = self.x + math.cos(angle) * (self.size - 4)
            spot_y = self.y + math.sin(angle) * (self.size - 4)
            spot_size = 2 + int(math.sin(self.mutation_timer + i) * 1)
            pygame.draw.circle(screen, (200, 100, 200),
                               (int(spot_x), int(spot_y)), spot_size)

        # Draw toxic aura
        aura_radius = self.size + 8 + \
            int(math.sin(self.mutation_timer * 2) * 4)
        pygame.draw.circle(screen, (50, 200, 50),
                           (int(self.x), int(self.y)), aura_radius, 1)

        # Draw health bar using centralized method
        self.draw_health_bar(screen)

        # Draw toxic immunity indicator
        font = pygame.font.Font(None, 10)
        immunity_text = font.render("TOXIC", True, (100, 255, 50))
        text_rect = immunity_text.get_rect(
            center=(self.x, self.y + self.size + 12))
        screen.blit(immunity_text, text_rect)
